{"name": "customine", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "export": "next export", "api-server": "node server/index.js", "dev-full": "concurrently \"npm run dev\" \"npm run api-server\"", "dev-with-api": "concurrently --names \"NEXT,API\" --prefix-colors \"blue,green\" \"npm run dev\" \"npm run api-server\"", "deploy": "chmod +x scripts/deploy.sh && scripts/deploy.sh", "deploy:hostgator": "npm run build && node scripts/cleanup-build.js && cp public/.htaccess out/.htaccess && echo 'Build complete! Upload the out/ directory contents to HostGator public_html'", "static": "next build && next export && npx serve out", "cleanup": "node scripts/cleanup-build.js"}, "dependencies": {"@aws-amplify/storage": "^6.9.3", "@aws-amplify/ui-react": "^6.11.2", "aws-amplify": "^6.15.3", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.468.0", "next": "15.3.5", "razorpay": "^2.9.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "sqlite3": "^5.1.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "concurrently": "^7.6.0", "eslint": "^9", "eslint-config-next": "15.3.5", "nodemon": "^3.0.1", "tailwindcss": "^4", "typescript": "^5"}}