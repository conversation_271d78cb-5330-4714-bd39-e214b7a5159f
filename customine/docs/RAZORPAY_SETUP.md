# AWS Amplify-Based Razorpay Payment Integration for Customine

This document provides step-by-step instructions for the AWS Amplify-based Razorpay payment gateway integration in the Customine project. This implementation works with static export and HostGator hosting.

## Prerequisites

1. A Razorpay account (sign up at https://razorpay.com/)
2. Razorpay API keys (Key ID only - no server-side secret needed)
3. AWS Amplify configured with GraphQL API
4. Next.js project with static export capability

## Setup Instructions

### 1. Install Dependencies

The Razorpay dependency has been added to package.json. Install it by running:

```bash
npm install
```

### 2. Get Razorpay API Keys

1. Log in to your Razorpay Dashboard
2. Go to Settings → API Keys
3. Generate API Keys if you haven't already
4. Copy the Key ID and Key Secret

### 3. Configure Environment Variables

1. Copy `.env.example` to `.env.local`:
```bash
cp .env.example .env.local
```

2. Add your Razorpay credentials to `.env.local`:
```env
NEXT_PUBLIC_RAZORPAY_KEY_ID=rzp_test_your_key_id_here
```

**Important Security Notes:**
- Never commit `.env.local` to version control
- Use test keys for development (they start with `rzp_test_`)
- Use live keys only in production (they start with `rzp_live_`)
- No server-side secret needed for client-side integration

### 4. Add Razorpay Logo (Optional)

Replace `public/razorpay-logo.png` with the official Razorpay logo from their brand kit.

## Features Implemented

### 1. Payment Processing
- **Online Payment**: Credit/Debit cards, Net Banking, UPI, Wallets
- **Cash on Delivery**: Traditional COD option
- **Payment Verification**: Server-side signature verification
- **Error Handling**: Comprehensive error handling and user feedback

### 2. User Experience
- **Payment Method Selection**: Radio buttons for payment options
- **Real-time Validation**: Form validation before payment
- **Loading States**: Visual feedback during payment processing
- **Success/Failure Handling**: Appropriate user notifications

### 3. Security
- **Server-side Verification**: Payment signatures verified on server
- **Environment Variables**: Sensitive data stored securely
- **HTTPS Required**: Razorpay requires HTTPS in production

## AWS Amplify Integration

### GraphQL Mutations
The payment system uses AWS Amplify GraphQL mutations for order management:

**Create Order:**
```graphql
mutation CreateOrder($input: CreateOrderInput!) {
  createOrder(input: $input) {
    id
    orderNumber
    status
    total
    paymentStatus
  }
}
```

**Update Order (Payment Verification):**
```graphql
mutation UpdateOrder($input: UpdateOrderInput!) {
  updateOrder(input: $input) {
    id
    paymentStatus
    status
    razorpayPaymentId
  }
}
```

### Client-Side Razorpay Integration
- No server-side API routes needed
- Direct client-side Razorpay checkout integration
- Payment verification through GraphQL mutations
- Compatible with static export and HostGator hosting

## Testing

### Test Mode
- Use test API keys (starting with `rzp_test_`)
- Use test card numbers provided by Razorpay
- No real money is charged in test mode

### Test Cards
```
Card Number: 4111 1111 1111 1111
CVV: Any 3 digits
Expiry: Any future date
```

### Test UPI
```
UPI ID: success@razorpay
```

## Production Deployment

### 1. Switch to Live Keys
Replace test keys with live keys in production environment:
```env
NEXT_PUBLIC_RAZORPAY_KEY_ID=rzp_live_your_live_key_id
RAZORPAY_KEY_SECRET=your_live_key_secret
```

### 2. Enable HTTPS
Razorpay requires HTTPS in production. Ensure your domain has SSL certificate.

### 3. Webhook Setup (Optional)
Set up webhooks in Razorpay Dashboard for payment status updates:
- URL: `https://yourdomain.com/api/razorpay/webhook`
- Events: `payment.captured`, `payment.failed`

## Troubleshooting

### Common Issues

1. **"Razorpay is not defined" Error**
   - Ensure the Razorpay script is loaded before opening checkout
   - Check network connectivity

2. **Payment Verification Failed**
   - Verify Key Secret is correct
   - Check signature generation logic

3. **CORS Errors**
   - Ensure proper domain configuration in Razorpay Dashboard
   - Check HTTPS setup

### Debug Mode
Enable debug logging by adding to your environment:
```env
NODE_ENV=development
```

## Support

- **Razorpay Documentation**: https://razorpay.com/docs/
- **Razorpay Support**: https://razorpay.com/support/
- **Integration Guide**: https://razorpay.com/docs/payments/payment-gateway/web-integration/

## File Structure

```
src/
├── components/
│   └── CheckoutForm.tsx         # AWS Amplify-based checkout form
├── hooks/
│   └── usePayment.ts           # React hook for AWS Amplify payment processing
├── utils/
│   └── paymentAPI.ts           # AWS Amplify payment API utilities
├── app/checkout/
│   └── page.tsx                # Checkout page using CheckoutForm
└── graphql/
    ├── mutations.js            # GraphQL mutations for orders
    └── queries.js              # GraphQL queries for orders
```

## Next Steps

1. Test the integration thoroughly in test mode
2. Set up order management system to store payment details
3. Implement webhook handling for payment status updates
4. Add refund functionality if needed
5. Set up monitoring and analytics for payment success rates
