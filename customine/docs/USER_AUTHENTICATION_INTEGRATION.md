# User Authentication Integration for Customine Order System

This document outlines the complete user authentication integration for the Customine order tracking and management system.

## Overview

The system now uses proper user authentication to:
- **Secure Order Creation** - Only logged-in users can place orders
- **User-Specific Orders** - Each user sees only their own orders
- **Admin Access Control** - Admin-only access to order management
- **Personalized Experience** - User-specific dashboards and tracking

## Authentication Integration Points

### 1. Checkout Process (`src/app/checkout/page.tsx`)

#### User Authentication Check
```typescript
import { useAuth } from '../../context/AuthContext';

const { user, isLoggedIn } = useAuth();

// Check if user is logged in before order creation
if (!user?.id) {
  throw new Error('Please log in to place an order');
}

// Use actual user ID in order creation
const orderData = {
  userId: user.id, // Real user ID instead of hardcoded 'user123'
  // ... other order data
};
```

#### Features Implemented
- **Login Requirement** - Users must be logged in to checkout
- **User ID Integration** - Orders linked to actual user accounts
- **Payment Integration** - Payment records linked to user accounts
- **Error Handling** - Clear messaging for authentication issues

### 2. Orders Dashboard (`src/app/dashboard/orders/page.tsx`)

#### User-Specific Order Display
```typescript
import { useAuth } from '../../../context/AuthContext';

const { user, isLoggedIn } = useAuth();

// Fetch orders for the logged-in user only
useEffect(() => {
  if (user?.id) {
    fetchUserOrders(user.id);
  }
}, [fetchUserOrders, user?.id]);
```

#### Features Implemented
- **Authentication Guard** - Login required to access dashboard
- **User Profile Display** - Shows current user information
- **User-Specific Orders** - Only shows orders for the logged-in user
- **Refresh Functionality** - Refreshes orders for the current user

### 3. Admin Order Management (`src/app/dashboard/orders/page.tsx`)

#### Admin Access Control
```typescript
import { useAuth } from '../../../context/AuthContext';

const { user, isLoggedIn } = useAuth();

// Check if user has admin privileges
const isAdmin = user?.role === 'admin' || user?.email?.includes('admin');

// Authentication and authorization checks
if (!isLoggedIn || !user?.id) {
  // Show login prompt
}

if (!isAdmin) {
  // Show access denied message
}
```

#### Features Implemented
- **Admin Authentication** - Only admin users can access
- **Role-Based Access** - Checks user role or email for admin privileges
- **Access Denied Handling** - Clear messaging for non-admin users
- **Order Status Management** - Admin can update any order status

## User Interface Updates

### 1. Dashboard User Profile
```typescript
{/* User Info Display */}
<div className="bg-white rounded-lg shadow-sm p-4 mb-6">
  <div className="flex items-center space-x-3">
    <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
      <span className="text-blue-600 font-semibold">
        {user?.name?.charAt(0)?.toUpperCase() || user?.email?.charAt(0)?.toUpperCase() || 'U'}
      </span>
    </div>
    <div>
      <h2 className="font-semibold text-gray-900">
        {user?.name || 'User'}
      </h2>
      <p className="text-sm text-gray-600">{user?.email}</p>
    </div>
  </div>
</div>
```

### 2. Authentication Guards
- **Login Prompts** - Clear UI for unauthenticated users
- **Access Denied Pages** - Proper messaging for unauthorized access
- **Redirect Links** - Easy navigation to login or appropriate pages

## Data Flow with Authentication

### Order Creation Flow
1. **User Login Check** → Verify user is authenticated
2. **User ID Extraction** → Get user.id from auth context
3. **Order Creation** → Create order with actual user ID
4. **Payment Processing** → Link payment to user account
5. **Order Confirmation** → User-specific order confirmation

### Order Tracking Flow
1. **User Authentication** → Check if user is logged in
2. **User ID Filtering** → Filter orders by user.id
3. **Order Display** → Show only user's orders
4. **Real-time Updates** → User-specific order updates

### Admin Management Flow
1. **Admin Authentication** → Verify admin credentials
2. **Role Verification** → Check admin privileges
3. **Order Management** → Admin can manage all orders
4. **Status Updates** → Admin updates reflected in user dashboards

## Security Considerations

### 1. Data Isolation
- **User-Specific Queries** - Orders filtered by user ID
- **Admin-Only Operations** - Order status updates restricted to admins
- **Authentication Guards** - All sensitive operations require authentication

### 2. Authorization Levels
- **Public** - Order tracking by order number (no auth required)
- **User** - Personal order history and dashboard
- **Admin** - Order management and status updates

### 3. Error Handling
- **Authentication Errors** - Clear messaging for login issues
- **Authorization Errors** - Proper access denied handling
- **Data Validation** - User ID validation in all operations

## User Roles and Permissions

### Regular User Permissions
- ✅ Place orders (when logged in)
- ✅ View own order history
- ✅ Track own orders
- ✅ Update own profile
- ❌ View other users' orders
- ❌ Admin order management

### Admin User Permissions
- ✅ All regular user permissions
- ✅ View all orders
- ✅ Update order status
- ✅ Manage tracking information
- ✅ Access admin dashboard
- ✅ User management (if implemented)

## Integration with AWS Amplify

### GraphQL Queries with User Context
```typescript
// User-specific order queries
const result = await client.graphql({
  query: ordersByUserId,
  variables: { userId: user.id }
});

// Order creation with user context
const orderResult = await client.graphql({
  query: createOrder,
  variables: {
    input: {
      userId: user.id,
      // ... other order data
    }
  }
});
```

### Authentication with AWS Cognito
- **User Pool Integration** - Ready for AWS Cognito user management
- **JWT Token Handling** - Automatic token management via Amplify
- **Session Management** - Persistent login sessions
- **Role-Based Access** - Custom attributes for user roles

## Testing User Authentication

### Test Scenarios
1. **Unauthenticated Access** - Verify login prompts appear
2. **User Order Isolation** - Confirm users see only their orders
3. **Admin Access Control** - Test admin-only functionality
4. **Order Creation** - Verify orders are linked to correct users
5. **Dashboard Functionality** - Test user-specific dashboards

### Test Users
- **Regular User** - Standard customer account
- **Admin User** - Account with admin privileges
- **Unauthenticated** - No login session

## Future Enhancements

### Planned Features
1. **User Profile Management** - Edit profile information
2. **Order Notifications** - Email/SMS notifications for order updates
3. **Wishlist Integration** - User-specific wishlists
4. **Order History Export** - Download order history
5. **Multi-factor Authentication** - Enhanced security

### Advanced Authentication
1. **Social Login** - Google, Facebook integration
2. **Guest Checkout** - Limited functionality without account
3. **Account Verification** - Email verification for new accounts
4. **Password Reset** - Self-service password management

## Troubleshooting

### Common Issues
1. **User ID Not Found** - Check authentication context
2. **Orders Not Loading** - Verify user ID in queries
3. **Admin Access Denied** - Check user role configuration
4. **Authentication Loops** - Clear browser storage and retry

### Debug Steps
1. Check browser console for authentication errors
2. Verify user object in React DevTools
3. Test GraphQL queries in AWS AppSync console
4. Check AWS Cognito user pool configuration

This authentication integration provides a secure, user-centric experience for the Customine order management system with proper data isolation and role-based access control.
