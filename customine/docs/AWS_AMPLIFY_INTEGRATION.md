# AWS Amplify Integration for Customine Order & Payment System

This document outlines the complete AWS Amplify integration for the Customine order tracking and payment system.

## Architecture Overview

The system uses AWS Amplify with GraphQL for:
- **Order Management** - Creating, tracking, and updating orders
- **Payment Processing** - Storing payment records and transaction data
- **User Management** - User authentication and profiles
- **Real-time Updates** - GraphQL subscriptions for order status changes

## Components Integrated

### 1. Order Management (`src/utils/orderAPI.ts`)
- **Direct GraphQL Integration** - Uses AWS Amplify GraphQL client
- **Order Creation** - Creates orders with order items in DynamoDB
- **Order Tracking** - Fetches orders by order number with product details
- **User Orders** - Gets all orders for authenticated users
- **Status Updates** - Admin functionality for order lifecycle management

### 2. Payment Integration (`src/utils/paymentAPI.ts`)
- **Payment Records** - Stores payment data in AWS DynamoDB
- **Razorpay Integration** - Links Razorpay transactions with Amplify
- **Payment Status Tracking** - Manages payment lifecycle
- **Transaction History** - Complete payment audit trail

### 3. Frontend Integration

#### Checkout Process (`src/app/checkout/page.tsx`)
```typescript
// Direct Amplify integration
import { Amplify } from 'aws-amplify';
import amplifyconfig from '../../amplifyconfiguration.json';
import { orderAPI } from '../../utils/orderAPI';
import { paymentAPI } from '../../utils/paymentAPI';

// Configure Amplify
Amplify.configure(amplifyconfig);
```

#### Order Tracking (`src/hooks/useOrderTracking.ts`)
```typescript
// Uses orderAPI directly with GraphQL
const result = await orderAPI.trackOrder(orderNumber);
const userOrders = await orderAPI.getUserOrders(userId);
```

#### Admin Management (`src/app/dashboard/orders/page.tsx`)
```typescript
// Direct order status updates
const result = await orderAPI.updateOrderStatus(
  orderId, status, trackingNumber, estimatedDelivery
);
```

## GraphQL Schema Integration

### Order Schema
```graphql
type Order @model @auth(rules: [{allow: public, operations: [read]}]) {
  id: ID!
  orderNumber: String! @index(name: "byOrderNumber")
  userId: String! @index(name: "byUserId")
  status: OrderStatus!
  paymentStatus: PaymentStatus!
  total: Float!
  currency: String!
  shippingAddress: ShippingAddress!
  items: [OrderItem] @hasMany(indexName: "byOrderId", fields: ["id"])
  trackingNumber: String
  estimatedDelivery: AWSDateTime
  deliveredAt: AWSDateTime
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}
```

### Payment Schema
```graphql
type Payment @model @auth(rules: [{allow: public, operations: [read]}]) {
  id: ID!
  orderId: String! @index(name: "byOrderId")
  userId: String! @index(name: "byUserId")
  amount: Float!
  currency: String!
  paymentMethod: String!
  razorpayOrderId: String
  razorpayPaymentId: String
  razorpaySignature: String
  status: PaymentStatus!
  metadata: String
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}
```

## Data Flow

### Order Creation Flow
1. **User Checkout** → Frontend collects order data
2. **Order API** → `orderAPI.create()` creates order in DynamoDB
3. **Payment Processing** → Razorpay handles payment
4. **Payment Record** → `paymentAPI.createPayment()` stores payment data
5. **Order Confirmation** → User receives order number

### Order Tracking Flow
1. **User Input** → Order number entered
2. **GraphQL Query** → `ordersByOrderNumber` fetches order
3. **Product Details** → Related products fetched via `getProduct`
4. **Order Items** → `orderItemsByOrderId` gets order items
5. **Display** → Complete order information shown

### Admin Management Flow
1. **Admin Access** → Authenticated admin user
2. **Status Update** → Admin updates order status
3. **GraphQL Mutation** → `updateOrder` mutation executed
4. **Real-time Update** → Changes reflected immediately
5. **Customer Notification** → Status change visible to customer

## Environment Configuration

### Required Environment Variables
```env
# AWS Amplify Configuration (auto-generated)
NEXT_PUBLIC_AWS_PROJECT_REGION=ap-south-1
NEXT_PUBLIC_AWS_COGNITO_IDENTITY_POOL_ID=your_identity_pool_id
NEXT_PUBLIC_AWS_COGNITO_REGION=ap-south-1
NEXT_PUBLIC_AWS_USER_POOLS_ID=your_user_pool_id
NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID=your_client_id
NEXT_PUBLIC_AWS_APPSYNC_GRAPHQLENDPOINT=your_graphql_endpoint
NEXT_PUBLIC_AWS_APPSYNC_REGION=ap-south-1
NEXT_PUBLIC_AWS_APPSYNC_AUTHENTICATIONTYPE=API_KEY
NEXT_PUBLIC_AWS_APPSYNC_APIKEY=your_api_key

# Razorpay Integration
NEXT_PUBLIC_RAZORPAY_KEY_ID=your_razorpay_key_id
RAZORPAY_KEY_SECRET=your_razorpay_key_secret
```

### Amplify Configuration Files
- `src/amplifyconfiguration.json` - Auto-generated Amplify config
- `src/aws-exports.js` - Legacy Amplify config (if using Gen 1)
- `amplify/backend/` - Amplify backend configuration

## Security & Authentication

### API Security
- **API Key Authentication** - For public read operations
- **Cognito User Pools** - For authenticated operations
- **IAM Roles** - For admin operations
- **GraphQL Authorization** - Field-level security

### Data Protection
- **Encryption at Rest** - DynamoDB encryption
- **Encryption in Transit** - HTTPS/TLS
- **Input Validation** - GraphQL schema validation
- **SQL Injection Protection** - GraphQL prevents SQL injection

## Performance Optimization

### GraphQL Efficiency
- **Selective Queries** - Fetch only required fields
- **Batch Operations** - Multiple items in single request
- **Connection Patterns** - Efficient relationship queries
- **Caching** - AWS AppSync caching

### Frontend Optimization
- **React Hooks** - Efficient state management
- **Error Boundaries** - Graceful error handling
- **Loading States** - User experience optimization
- **Toast Notifications** - Real-time feedback

## Deployment

### Development Environment
```bash
# Install Amplify CLI
npm install -g @aws-amplify/cli

# Initialize Amplify
amplify init

# Deploy backend
amplify push

# Start development server
npm run dev
```

### Production Deployment
```bash
# Build for production
npm run build

# Deploy to Amplify Hosting
amplify publish

# Or deploy to custom hosting
npm run export
```

## Monitoring & Analytics

### AWS CloudWatch
- **API Performance** - GraphQL query metrics
- **Error Tracking** - Failed operations monitoring
- **Usage Analytics** - API usage patterns

### Application Monitoring
- **Order Metrics** - Order creation/completion rates
- **Payment Analytics** - Payment success/failure rates
- **User Behavior** - Order tracking usage

## Troubleshooting

### Common Issues
1. **Authentication Errors** - Check Amplify configuration
2. **GraphQL Errors** - Verify schema and permissions
3. **Network Issues** - Check AWS region and endpoints
4. **CORS Issues** - Configure Amplify CORS settings

### Debug Tools
- **AWS AppSync Console** - Query testing
- **Amplify CLI** - Backend status and logs
- **Browser DevTools** - Network and console debugging
- **CloudWatch Logs** - Server-side error tracking

## Next Steps

### Planned Enhancements
1. **Real-time Subscriptions** - Live order status updates
2. **Advanced Analytics** - Order and payment insights
3. **Multi-region Deployment** - Global availability
4. **Enhanced Security** - Advanced authentication flows

### Integration Opportunities
1. **Email Notifications** - SES integration for order updates
2. **SMS Notifications** - SNS integration for delivery alerts
3. **Inventory Management** - Real-time stock tracking
4. **Customer Support** - Integrated help desk system

This AWS Amplify integration provides a robust, scalable foundation for the Customine order and payment system with real-time capabilities and enterprise-grade security.
