"use client";
import React, { useState } from 'react';
import { authAPI } from '../utils/api';
import { validatePhoneNumber, cleanPhoneInput, getPhonePlaceholder } from '../utils/phoneUtils';

interface SignupModalProps {
  isOpen: boolean;
  onClose: () => void;
  onLogin?: () => void;
  onSignup?: () => void;
}

type SignupStep = 'signup' | 'verification';

export default function SignupModal({ isOpen, onClose, onLogin, onSignup }: SignupModalProps) {
  const [step, setStep] = useState<SignupStep>('signup');
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [phone, setPhone] = useState('');
  const [otp, setOtp] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [resendLoading, setResendLoading] = useState(false);
  const [resendCooldown, setResendCooldown] = useState(0);

  // Cooldown timer for resend button
  React.useEffect(() => {
    if (resendCooldown > 0) {
      const timer = setTimeout(() => {
        setResendCooldown(resendCooldown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [resendCooldown]);

  // Reset form when modal closes
  React.useEffect(() => {
    if (!isOpen) {
      setStep('signup');
      setName('');
      setEmail('');
      setPassword('');
      setPhone('');
      setOtp('');
      setError('');
      setResendCooldown(0);
    }
  }, [isOpen]);

  if (!isOpen) return null;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    if (!name || !email || !password) {
      setError('Please fill all fields.');
      setLoading(false);
      return;
    }

    // Validate phone number if provided
    if (phone) {
      const phoneValidation = validatePhoneNumber(phone);
      if (!phoneValidation.isValid) {
        setError(phoneValidation.error || 'Invalid phone number format');
        setLoading(false);
        return;
      }
    }

    try {
      const result = await authAPI.signUp({
        name,
        email,
        password,
        phone: phone || undefined
      });

      if (result.success) {
        // Move to verification step
        setStep('verification');
        setError('');
      } else {
        setError(result.error || 'Signup failed');
      }
    } catch (error) {
      console.error('Signup error:', error);
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleOTPVerification = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    if (!otp || otp.length !== 6) {
      setError('Please enter a valid 6-digit verification code.');
      setLoading(false);
      return;
    }

    try {
      const result = await authAPI.confirmSignUp(email, otp);

      if (result.success) {
        // Success! Close modal and call onSignup
        if (onSignup) onSignup();
        onClose();
      } else {
        setError(result.error || 'Verification failed. Please check your code.');
      }
    } catch (error) {
      console.error('OTP verification error:', error);
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleResendOTP = async () => {
    setResendLoading(true);
    setError('');

    try {
      const result = await authAPI.resendConfirmationCode(email);

      if (result.success) {
        setResendCooldown(60); // 60 seconds cooldown
      } else {
        setError(result.error || 'Failed to resend verification code.');
      }
    } catch (error) {
      console.error('Resend OTP error:', error);
      setError('Network error. Please try again.');
    } finally {
      setResendLoading(false);
    }
  };

  const handleOtpChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, '').slice(0, 6);
    setOtp(value);
  };

  const handleSocial = (provider: string) => {
    alert(`Social signup with ${provider} (not implemented)`);
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center pointer-events-auto p-4">
      {/* Enhanced overlay background */}
      <div className="absolute inset-0 bg-gradient-to-br from-black/50 to-black/70 backdrop-blur-sm z-0" onClick={onClose} />

      {/* Modern modal container */}
      <div className="relative w-full max-w-md bg-white rounded-3xl shadow-2xl z-10 overflow-hidden animate-fade-in">
        {/* Header with gradient background */}
        <div className="relative bg-gradient-to-br from-[var(--color-navy)] via-[var(--color-teal)] to-[var(--color-navy)] p-8 pb-6">
          {/* Close button */}
          <button
            onClick={onClose}
            className="absolute top-4 right-4 w-8 h-8 flex items-center justify-center rounded-full bg-white/10 hover:bg-white/20 text-white transition-all duration-200 backdrop-blur-sm"
            aria-label="Close signup modal"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>

          {/* Logo/Brand area */}
          <div className="text-center mb-4">
            <div className="w-16 h-16 mx-auto mb-4 bg-white/10 rounded-2xl flex items-center justify-center backdrop-blur-sm">
              <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
              </svg>
            </div>
            <h2 className="text-2xl font-bold text-white mb-2">
              {step === 'signup' ? 'Create Account' : 'Verify Your Email'}
            </h2>
            <p className="text-white/80 text-sm">
              {step === 'signup' ? 'Join us today and get started' : 'Enter the code we sent you'}
            </p>
          </div>
        </div>

        {/* Content area */}
        <div className="p-8 pt-6 animate-slide-up">

          {step === 'signup' && (
            <div className="mb-6">
              <button
                onClick={() => handleSocial('Google')}
                className="w-full flex items-center justify-center py-3.5 px-4 rounded-xl border border-gray-200 bg-white text-gray-700 font-medium hover:bg-gray-50 hover:border-gray-300 transition-all duration-200 shadow-sm hover:shadow-md group"
              >
                {/* eslint-disable-next-line @next/next/no-img-element */}
                <img src="https://www.svgrepo.com/show/475656/google-color.svg" alt="Google" width={20} height={20} className="mr-3 group-hover:scale-110 transition-transform duration-200" />
                <span className="text-gray-800">Sign up with Google</span>
            </button>
          </div>
        )}

          {step === 'verification' && (
            <div className="mb-6 text-center">
              <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
                <p className="text-[var(--color-navy)] mb-2 text-sm">
                  We&apos;ve sent a 6-digit verification code to:
                </p>
                <p className="text-[var(--color-teal)] font-semibold mb-3 break-all text-sm">
                  {email}
                </p>
                <p className="text-xs text-gray-600">
                  Please enter the code below to verify your email address.
                </p>
              </div>
            </div>
          )}

          {step === 'signup' && (
            <div className="flex items-center my-6">
              <div className="flex-1 h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent" />
              <span className="mx-4 text-gray-500 text-sm font-medium bg-white px-2">or</span>
              <div className="flex-1 h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent" />
            </div>
          )}

          {/* Signup Form */}
          {step === 'signup' && (
            <form onSubmit={handleSubmit} className="space-y-5">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                  <input
                    type="text"
                    className="w-full px-4 py-3.5 border border-gray-200 rounded-xl bg-gray-50 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[var(--color-teal)] focus:border-transparent transition-all duration-200"
                    placeholder="Enter your full name"
                    value={name}
                    onChange={e => setName(e.target.value)}
                    autoComplete="name"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                  <input
                    type="email"
                    className="w-full px-4 py-3.5 border border-gray-200 rounded-xl bg-gray-50 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[var(--color-teal)] focus:border-transparent transition-all duration-200"
                    placeholder="Enter your email"
                    value={email}
                    onChange={e => setEmail(e.target.value)}
                    autoComplete="email"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Phone Number <span className="text-xs text-gray-500">(Optional)</span>
                  </label>
                  <input
                    type="tel"
                    className="w-full px-4 py-3.5 border border-gray-200 rounded-xl bg-gray-50 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[var(--color-teal)] focus:border-transparent transition-all duration-200"
                    value={phone}
                    onChange={e => setPhone(cleanPhoneInput(e.target.value))}
                    placeholder={getPhonePlaceholder()}
                    autoComplete="tel"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Enter 10-digit mobile number (e.g., 9876543210)
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Password</label>
                  <input
                    type="password"
                    className="w-full px-4 py-3.5 border border-gray-200 rounded-xl bg-gray-50 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[var(--color-teal)] focus:border-transparent transition-all duration-200"
                    placeholder="Create a password"
                    value={password}
                    onChange={e => setPassword(e.target.value)}
                    autoComplete="new-password"
                    required
            />
                </div>
              </div>

              {error && (
                <div className="bg-red-50 border border-red-200 rounded-xl p-3">
                  <p className="text-red-600 text-sm text-center">{error}</p>
                </div>
              )}

              <button
                type="submit"
                className="w-full py-3.5 rounded-xl font-semibold text-white bg-gradient-to-r from-[var(--color-navy)] to-[var(--color-teal)] hover:from-[var(--color-teal)] hover:to-[var(--color-navy)] transition-all duration-300 disabled:opacity-60 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                disabled={loading}
              >
                {loading ? 'Creating Account...' : 'Create Account'}
              </button>
            </form>
          )}

          {/* OTP Verification Form */}
          {step === 'verification' && (
            <form onSubmit={handleOTPVerification} className="space-y-5">
              <div>
                <label htmlFor="otp" className="block text-sm font-medium text-gray-700 mb-2">
                  Verification Code
                </label>
                <input
                  type="text"
                  id="otp"
                  value={otp}
                  onChange={handleOtpChange}
                  placeholder="000000"
                  className="w-full px-4 py-3.5 border border-gray-200 rounded-xl bg-gray-50 text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[var(--color-teal)] focus:border-transparent text-center text-2xl tracking-[0.5em] font-mono transition-all duration-200"
                  maxLength={6}
                  autoComplete="one-time-code"
                  required
                />
              </div>

              {error && (
                <div className="bg-red-50 border border-red-200 rounded-xl p-3">
                  <p className="text-red-600 text-sm text-center">{error}</p>
                </div>
              )}

              <button
                type="submit"
                disabled={loading || otp.length !== 6}
                className="w-full py-3.5 rounded-xl font-semibold text-white bg-gradient-to-r from-[var(--color-navy)] to-[var(--color-teal)] hover:from-[var(--color-teal)] hover:to-[var(--color-navy)] transition-all duration-300 disabled:opacity-60 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                {loading ? 'Verifying...' : 'Verify Email'}
              </button>

              <div className="text-center">
                <p className="text-sm text-gray-600 mb-3">
                  Didn&apos;t receive the code?
                </p>
                <button
                  type="button"
                  onClick={handleResendOTP}
                  disabled={resendLoading || resendCooldown > 0}
                  className="text-[var(--color-teal)] hover:text-[var(--color-navy)] font-medium text-sm disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                >
                  {resendLoading ? 'Sending...' :
                   resendCooldown > 0 ? `Resend in ${resendCooldown}s` :
                   'Resend Code'}
                </button>
              </div>

              <div className="text-center">
                <button
                  type="button"
                  onClick={() => setStep('signup')}
                  className="text-sm text-gray-600 hover:text-[var(--color-teal)] font-medium transition-colors duration-200 flex items-center justify-center mx-auto"
                >
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                  Back to Sign Up
                </button>
              </div>
            </form>
          )}

          {step === 'signup' && (
            <div className="mt-6 pt-6 border-t border-gray-100 text-center">
              <p className="text-sm text-gray-600">
                Already have an account?{' '}
                <button
                  type="button"
                  className="text-[var(--color-teal)] font-semibold hover:text-[var(--color-navy)] transition-colors duration-200"
                  onClick={() => { onClose(); if (onLogin) onLogin(); }}
                >
                  Login
                </button>
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}