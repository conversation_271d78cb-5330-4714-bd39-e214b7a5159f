'use client';

import React from 'react';
import Link from 'next/link';

const Footer = () => {
  return (
    <footer className="bg-gray-900 text-white">
      {/* Main Footer Content */}
      <div className="py-12">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center md:text-left">
            {/* Column 1 */}
            <div className="flex flex-col items-center md:items-start">
              <h4 className="text-lg font-semibold mb-4">Shop</h4>
              <ul className="space-y-2 text-sm">
                <li><Link href="/" className="text-gray-300 hover:text-white transition-colors">Home</Link></li>
                <li><Link href="/collections/all" className="text-gray-300 hover:text-white transition-colors">Ready-to-Ship Gifts</Link></li>
                <li><Link href="/pages/custom-gift-design" className="text-gray-300 hover:text-white transition-colors">Custom Gift Design</Link></li>
                <li><Link href="/pages/special-occasions" className="text-gray-300 hover:text-white transition-colors">Special Occasions</Link></li>
                <li><Link href="/pages/everyday-occasions" className="text-gray-300 hover:text-white transition-colors">Everyday Occasions</Link></li>
                <li><Link href="/pages/in-house-products" className="text-gray-300 hover:text-white transition-colors">In-House Products</Link></li>
                <li><Link href="/pages/luxury-cards" className="text-gray-300 hover:text-white transition-colors">Luxury Cards</Link></li>
                <li><Link href="/pages/diwali-gifting" className="text-gray-300 hover:text-white transition-colors">Seasonal Gifting</Link></li>
              </ul>
            </div>
            {/* Column 2 */}
            <div className="flex flex-col items-center md:items-start">
              <h4 className="text-lg font-semibold mb-4">Company</h4>
              <ul className="space-y-2 text-sm">
                <li><Link href="/pages/about-us" className="text-gray-300 hover:text-white transition-colors">About Us</Link></li>
                <li><Link href="/pages/contact-us" className="text-gray-300 hover:text-white transition-colors">Contact Us</Link></li>
                <li><Link href="/faq" className="text-gray-300 hover:text-white transition-colors">FAQ</Link></li>
                <li><Link href="/blogs/marigold-blog" className="text-gray-300 hover:text-white transition-colors">Blog</Link></li>
                <li><Link href="/terms-conditions" className="text-gray-300 hover:text-white transition-colors">Terms & Conditions</Link></li>
                <li><Link href="/pages/disclaimer" className="text-gray-300 hover:text-white transition-colors">Legal Disclaimer</Link></li>
              </ul>
            </div>
            {/* Column 3: Social & Contact */}
            <div className="flex flex-col items-center md:items-start">
              <h4 className="text-lg font-semibold mb-4">Connect</h4>
              <div className="space-y-2 text-sm">
                <Link href="mailto:<EMAIL>" className="flex items-center text-gray-300 hover:text-white transition-colors">
                  <EMAIL>
                </Link>
                <Link href="tel:+15717668690" className="flex items-center text-gray-300 hover:text-white transition-colors">
                +91- 90426 71801
                </Link>
              </div>
              <div className="flex space-x-4 mt-6">
                <Link href="https://www.instagram.com/customine/" className="text-gray-300 hover:text-white transition-colors" aria-label="Instagram">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/></svg>
                </Link>
                <Link href="https://www.facebook.com/customine" className="text-gray-300 hover:text-white transition-colors" aria-label="Facebook">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24"><path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/></svg>
                </Link>
                <Link href="https://www.linkedin.com/company/customine" className="text-gray-300 hover:text-white transition-colors" aria-label="LinkedIn">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24"><path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/></svg>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="border-t border-gray-700 py-6">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row md:justify-between items-center text-center md:text-left text-sm text-gray-400">
            <p>
              Copyright © 2025 CUSTOMINE | ALL RIGHTS RESERVED
            </p>
            <div className="flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-4 mt-4 md:mt-0 items-center md:items-start">
                <span className="hover:text-white transition-colors">
                  Designed by <a href="https://dedicinfotech.com/" target="_blank" rel="noopener noreferrer" className="underline hover:text-blue-300">Dedic Infotech</a>
                </span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
