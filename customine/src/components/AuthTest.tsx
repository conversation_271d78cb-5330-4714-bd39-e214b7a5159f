/**
 * Authentication Test Component
 * Simple component to test AWS Cognito authentication
 */

"use client";
import React, { useState } from 'react';
import { authAPI } from '../utils/api';

export default function AuthTest() {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('TestPass123!');
  const [name, setName] = useState('Test User');
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testSignUp = async () => {
    setLoading(true);
    setResult('Testing signup...');
    
    try {
      const result = await authAPI.signUp({
        name,
        email,
        password
      });
      
      setResult(JSON.stringify(result, null, 2));
    } catch (error) {
      setResult(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const testSignIn = async () => {
    setLoading(true);
    setResult('Testing signin...');
    
    try {
      const result = await authAPI.signIn({
        email,
        password
      });
      
      setResult(JSON.stringify(result, null, 2));
    } catch (error) {
      setResult(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const testCurrentUser = async () => {
    setLoading(true);
    setResult('Testing current user...');
    
    try {
      const result = await authAPI.getCurrentUser();
      setResult(JSON.stringify(result, null, 2));
    } catch (error) {
      setResult(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const testSignOut = async () => {
    setLoading(true);
    setResult('Testing signout...');
    
    try {
      const result = await authAPI.signOut();
      setResult(JSON.stringify(result, null, 2));
    } catch (error) {
      setResult(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6 max-w-2xl mx-auto">
      <h2 className="text-2xl font-bold mb-4">Authentication Test</h2>
      
      <div className="space-y-4 mb-6">
        <div>
          <label className="block text-sm font-medium mb-1">Name</label>
          <input
            type="text"
            value={name}
            onChange={(e) => setName(e.target.value)}
            className="w-full px-3 py-2 border rounded-md"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium mb-1">Email</label>
          <input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="w-full px-3 py-2 border rounded-md"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium mb-1">Password</label>
          <input
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            className="w-full px-3 py-2 border rounded-md"
          />
        </div>
      </div>

      <div className="flex flex-wrap gap-2 mb-6">
        <button
          onClick={testSignUp}
          disabled={loading}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
        >
          Test Sign Up
        </button>
        
        <button
          onClick={testSignIn}
          disabled={loading}
          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
        >
          Test Sign In
        </button>
        
        <button
          onClick={testCurrentUser}
          disabled={loading}
          className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:opacity-50"
        >
          Test Current User
        </button>
        
        <button
          onClick={testSignOut}
          disabled={loading}
          className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 disabled:opacity-50"
        >
          Test Sign Out
        </button>
      </div>

      <div className="bg-gray-100 p-4 rounded-md">
        <h3 className="font-medium mb-2">Result:</h3>
        <pre className="text-sm overflow-auto max-h-96">
          {result || 'No test run yet'}
        </pre>
      </div>
    </div>
  );
}
