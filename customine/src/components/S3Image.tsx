'use client';

import React, { useState, useEffect } from 'react';
import { Amplify } from 'aws-amplify';
import { getUrl } from 'aws-amplify/storage';
import amplifyconfig from '../amplifyconfiguration.json';

// Configure Amplify
Amplify.configure(amplifyconfig);

interface S3ImageProps {
  src: string;
  alt: string;
  className?: string;
  fallbackSrc?: string;
  onLoad?: () => void;
  onError?: () => void;
}

const S3Image: React.FC<S3ImageProps> = ({
  src,
  alt,
  className = '',
  fallbackSrc = '/placeholder-product.png',
  onLoad,
  onError
}) => {
  const [imageUrl, setImageUrl] = useState<string>(src);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);

  useEffect(() => {
    const processImageUrl = async () => {
      try {
        setLoading(true);
        setError(false);

        // If it's a relative path, use it as is
        if (src.startsWith('/')) {
          setImageUrl(src);
          setLoading(false);
          return;
        }

        // If it's an S3 key, convert to URL
        if (src.startsWith('products/') || src.includes('products/')) {
          console.log('Converting S3 key to URL:', src);
          const { url } = await getUrl({ 
            key: src,
            options: { expiresIn: 86400 } // 24 hours
          });
          const urlString = url.toString();
          console.log('Generated fresh S3 URL');
          setImageUrl(urlString);
          setLoading(false);
          return;
        }

        // If it's already a URL, try to extract key and refresh
        if (src.startsWith('http')) {
          console.log('Attempting to refresh expired S3 URL:', src.substring(0, 100) + '...');
          try {
            // Handle malformed nested URLs
            let cleanUrl = src;

            // Check if URL contains encoded URLs (double encoding issue)
            if (src.includes('https%3A') || src.includes('http%3A')) {
              console.log('Detected double-encoded URL, attempting to clean...');
              // Try to extract the actual S3 key from the mess
              const match = src.match(/products\/[^?&%]+/);
              if (match) {
                const key = decodeURIComponent(match[0]);
                console.log('Extracted clean S3 key from malformed URL:', key);

                const { url } = await getUrl({
                  key,
                  options: { expiresIn: 86400 }
                });
                const refreshedUrl = url.toString();
                console.log('Generated fresh URL from extracted key');
                setImageUrl(refreshedUrl);
                setLoading(false);
                return;
              }
            }

            // Normal URL processing
            const urlParts = cleanUrl.split('/');
            const keyIndex = urlParts.findIndex(part => part === 'products');
            if (keyIndex !== -1 && keyIndex < urlParts.length - 1) {
              const key = urlParts.slice(keyIndex).join('/');
              // Decode any URL encoding in the key
              const decodedKey = decodeURIComponent(key);
              console.log('Extracted S3 key:', decodedKey);

              const { url } = await getUrl({
                key: decodedKey,
                options: { expiresIn: 86400 }
              });
              const refreshedUrl = url.toString();
              console.log('Refreshed S3 URL successfully');
              setImageUrl(refreshedUrl);
              setLoading(false);
              return;
            }
          } catch (refreshError) {
            console.warn('Could not refresh S3 URL:', refreshError);
          }

          // If all else fails, try using the original URL
          console.log('Using original URL as last resort');
          setImageUrl(src);
          setLoading(false);
          return;
        }

        // For any other format, use fallback
        console.log('Unknown image format, using fallback');
        setImageUrl(fallbackSrc);
        setLoading(false);

      } catch (err) {
        console.error('Error processing image URL:', err);
        setImageUrl(fallbackSrc);
        setError(true);
        setLoading(false);
      }
    };

    processImageUrl();
  }, [src, fallbackSrc]);

  const handleImageLoad = () => {
    setLoading(false);
    setError(false);
    onLoad?.();
  };

  const handleImageError = () => {
    console.error('Image failed to load:', imageUrl);
    if (imageUrl !== fallbackSrc) {
      setImageUrl(fallbackSrc);
      setError(true);
    }
    onError?.();
  };

  if (loading) {
    return (
      <div className={`bg-gray-200 animate-pulse flex items-center justify-center ${className}`}>
        <div className="text-gray-400 text-sm">Loading...</div>
      </div>
    );
  }

  return (
    <img
      src={imageUrl}
      alt={alt}
      className={className}
      onLoad={handleImageLoad}
      onError={handleImageError}
    />
  );
};

export default S3Image;
