"use client";

import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { fetchPublicHomepageHeroGallery, fetchGalleryBySubcategory, fetchGalleryByCategory, type GalleryImage } from '@/utils/galleryAPI';

interface GallerySliderProps {
  useGalleryAPI?: boolean;
  galleryCategory?: 'HOME' | 'LUXURY' | 'CORPORATE' | 'WEDDING' | 'FESTIVAL' | 'PERSONAL';
  gallerySubcategory?: string;
  height?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | '5xl';
  rounded?: boolean;
}

const GallerySlider: React.FC<GallerySliderProps> = ({ 
  useGalleryAPI = false, 
  galleryCategory = 'HOME', 
  gallerySubcategory,
  height = 'lg',
  rounded = true 
}) => {
  const [images, setImages] = useState<string[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Height classes for different sizes
  const heightClasses = {
    sm: 'h-48',      // 192px
    md: 'h-64',      // 256px
    lg: 'h-80',      // 320px
    xl: 'h-96',      // 384px
    '2xl': 'h-[28rem]', // 448px
    '3xl': 'h-[32rem]', // 512px
    '4xl': 'h-[36rem]', // 576px
    '5xl': 'h-[40rem]'  // 640px
  };

  useEffect(() => {
    const loadGalleryImages = async () => {
      if (!useGalleryAPI) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        let galleryImages: GalleryImage[] = [];

        if (galleryCategory === 'HOME') {
          console.log('🏠 GallerySlider: Fetching HOME category images');
          galleryImages = await fetchPublicHomepageHeroGallery();
          console.log(`📸 GallerySlider: Found ${galleryImages.length} HOME images`, galleryImages);
        } else if (gallerySubcategory) {
          console.log(`🎯 GallerySlider: Fetching ${galleryCategory}/${gallerySubcategory} images`);
          galleryImages = await fetchGalleryBySubcategory(galleryCategory, gallerySubcategory);
          console.log(`📸 GallerySlider: Found ${galleryImages.length} images for ${galleryCategory}/${gallerySubcategory}`, galleryImages);
        } else {
          console.log(`📂 GallerySlider: Fetching ${galleryCategory} category images`);
          galleryImages = await fetchGalleryByCategory(galleryCategory);
          console.log(`📸 GallerySlider: Found ${galleryImages.length} images for ${galleryCategory}`, galleryImages);
        }

        if (galleryImages && galleryImages.length > 0) {
          const imageUrls = galleryImages.map(img => img.imageUrl).filter(Boolean);
          console.log(`✅ GallerySlider: Setting ${imageUrls.length} image URLs`, imageUrls);
          setImages(imageUrls);
        } else {
          console.log('⚠️ GallerySlider: No images found, using demo images');
          // Use demo images for testing navigation
          setImages([
            '/logo.png',
            '/logo_notecard.png',
            '/file.svg'
          ]);
        }
      } catch (err) {
        console.error('❌ GallerySlider: Error loading gallery images:', err);
        setError('Failed to load gallery images');
        // Use demo images for testing navigation
        setImages([
          '/logo.png',
          '/logo_notecard.png',
          '/file.svg'
        ]);
      } finally {
        setLoading(false);
      }
    };

    loadGalleryImages();
  }, [useGalleryAPI, galleryCategory, gallerySubcategory]);

  const nextSlide = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex === images.length - 1 ? 0 : prevIndex + 1
    );
  };

  const prevSlide = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex === 0 ? images.length - 1 : prevIndex - 1
    );
  };

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
  };

  if (loading) {
    return (
      <div className={`relative w-full ${heightClasses[height]} overflow-hidden bg-gradient-to-br from-gray-100 to-gray-200 ${rounded ? 'rounded-lg' : ''} animate-pulse`}>
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="flex space-x-2">
            <div className="w-3 h-3 bg-gray-400 rounded-full animate-bounce"></div>
            <div className="w-3 h-3 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
            <div className="w-3 h-3 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`relative w-full ${heightClasses[height]} overflow-hidden bg-gradient-to-br from-red-50 to-red-100 ${rounded ? 'rounded-lg' : ''} flex items-center justify-center`}>
        <div className="text-center p-6">
          <div className="w-16 h-16 bg-red-200 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-red-600 text-2xl">⚠️</span>
          </div>
          <p className="text-red-700 font-medium">Failed to load gallery</p>
          <p className="text-red-600 text-sm mt-1">{error}</p>
        </div>
      </div>
    );
  }

  if (images.length === 0) {
    return (
      <div className={`relative w-full ${heightClasses[height]} overflow-hidden bg-gradient-to-br from-slate-50 to-slate-100 ${rounded ? 'rounded-lg' : ''} flex items-center justify-center border border-slate-200`}>
        <div className="text-center p-6">
          <div className="w-20 h-20 bg-gradient-to-br from-slate-200 to-slate-300 rounded-full flex items-center justify-center mx-auto mb-4 shadow-inner">
            <svg className="w-10 h-10 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </div>
          <p className="text-slate-600 font-medium text-lg">Gallery Coming Soon</p>
          <p className="text-slate-500 text-sm mt-2">Images will be available shortly</p>
        </div>
      </div>
    );
  }

  console.log(`🖼️ GallerySlider Render: ${images.length} images, currentIndex: ${currentIndex}`, images);

  return (
    <div className={`relative w-full ${heightClasses[height]} overflow-hidden bg-gradient-to-br from-gray-100 to-gray-200 ${rounded ? 'rounded-lg' : ''} group`}>
      {/* Main Image */}
      <div className="relative w-full h-full">
        <img
          src={images[currentIndex]}
          alt={`Gallery image ${currentIndex + 1}`}
          className="w-full h-full object-cover transition-all duration-500 ease-in-out hover:scale-105"
          onError={(e) => {
            const currentSrc = e.currentTarget.src;
            console.error('Image failed to load:', images[currentIndex]);
            // Only set fallback if we're not already showing it to prevent infinite loop
            if (!currentSrc.includes('/logo.png') && !currentSrc.includes('data:image')) {
              e.currentTarget.src = '/logo.png';
            }
          }}
        />
        
        {/* Hover Overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
      </div>

      {/* Navigation Arrows - Show when there are images */}
      {images.length > 0 && (
        <>
          <button
            onClick={prevSlide}
            className="absolute left-3 top-1/2 transform -translate-y-1/2 bg-white/90 backdrop-blur-sm hover:bg-white text-gray-800 p-3 rounded-full shadow-xl opacity-80 hover:opacity-100 transition-all duration-300 hover:scale-110 z-20"
            aria-label="Previous image"
          >
            <ChevronLeft className="w-6 h-6" />
          </button>

          <button
            onClick={nextSlide}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 bg-white/90 backdrop-blur-sm hover:bg-white text-gray-800 p-3 rounded-full shadow-xl opacity-80 hover:opacity-100 transition-all duration-300 hover:scale-110 z-20"
            aria-label="Next image"
          >
            <ChevronRight className="w-6 h-6" />
          </button>
        </>
      )}

      {/* Dot Indicators - Show when there are multiple images */}
      {images.length > 1 && (
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2 opacity-90 hover:opacity-100 transition-opacity duration-300 z-10">
          {images.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={`transition-all duration-300 rounded-full shadow-lg ${
                index === currentIndex
                  ? 'w-8 h-3 bg-white'
                  : 'w-3 h-3 bg-white/70 hover:bg-white/90'
              }`}
              aria-label={`Go to image ${index + 1}`}
            />
          ))}
        </div>
      )}

      {/* Image Counter */}
      {images.length > 1 && (
        <div className="absolute top-4 right-4 bg-black/50 text-white px-3 py-1 rounded-full text-sm font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          {currentIndex + 1} / {images.length}
        </div>
      )}
    </div>
  );
};

export default GallerySlider;
