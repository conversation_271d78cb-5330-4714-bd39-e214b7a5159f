"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import { fetchFeaturedProducts } from "@/utils/productAPI";

// API Product interface to match the backend structure
interface APIProduct {
  id: string;
  name: string;
  slug: string;
  price: number;
  originalPrice?: number;
  images?: string[];
  shortDescription?: string;
  category?: string;
  tags?: string[];
  inStock?: boolean;
  isFeatured?: boolean;
}

// Local Product interface for display
interface Product {
  id: string;
  name: string;
  price: number;
  originalPrice?: number;
  image: string;
  hoverImage?: string;
  slug: string;
  badge?: string;
}

// Fallback images for products without images
const fallbackImages = [
  "/Creative_Canvas.png",
  "/Custom_Gardening_Hamper.png",
  "/Personalized_Gift_Hamper.png",
  "/Bottle_of_champagne.png",
  "/Custom_Gift_Hamper_coffee.png",
  "/Relaxation_Gift_Hamper.png",
  "/Snuggle_Season.png",
  "/Semi_Custom_image.png",
];

// Function to convert API product to display product
const convertAPIProduct = (apiProduct: APIProduct, index: number): Product => {
  const primaryImage =
    apiProduct.images?.[0] || fallbackImages[index % fallbackImages.length];
  const secondaryImage = apiProduct.images?.[1] || primaryImage;

  // Determine badge based on product properties
  let badge = "";
  if (apiProduct.isFeatured) {
    badge = "Featured";
  } else if (apiProduct.category === "coffee") {
    badge = "Coffee Lover";
  } else if (apiProduct.tags?.includes("new")) {
    badge = "New";
  } else if (apiProduct.tags?.includes("popular")) {
    badge = "Popular";
  }

  return {
    id: apiProduct.id,
    name: apiProduct.name,
    price: apiProduct.price,
    originalPrice: apiProduct.originalPrice,
    image: primaryImage,
    hoverImage: secondaryImage,
    slug: apiProduct.slug,
    badge: badge,
  };
};

interface ProductCardProps {
  product: Product;
}

const ProductCard: React.FC<ProductCardProps> = ({ product }) => {
  // Helper function to get product image from API data
  const getProductImage = (
    product: ProductWithImages,
  ): string => {
    // For API data (has images array)
    if (product.image) {
      return `https://customine4ae3bb77bc244f68885ca662b9bd1b4e1a04b-dev.s3.ap-south-1.amazonaws.com/public/${
        product.image
      }`;
    }
    // Fallback to placeholder
    return "/placeholder-image.jpg";
  };
  return (
    <div className="group">
      <Link href={`/product?id=${product.id}`} className="block">
        <div className="relative overflow-hidden rounded-lg bg-gray-100 aspect-square">
          {/* Product Badge */}
          {product.badge && (
            <div className="absolute top-3 left-3 z-10">
              <span
                className={`px-3 py-1 text-xs font-bold rounded-full shadow-sm ${
                  product.badge === "Bestseller"
                    ? "bg-emerald-100 text-emerald-800"
                    : product.badge === "Featured"
                    ? "bg-emerald-100 text-emerald-800"
                    : product.badge === "New"
                    ? "bg-blue-100 text-blue-800"
                    : product.badge === "Popular"
                    ? "bg-purple-100 text-purple-800"
                    : product.badge === "Coffee Lover"
                    ? "bg-amber-100 text-amber-800"
                    : "bg-gray-100 text-gray-800"
                }`}
              >
                {product.badge}
              </span>
            </div>
          )}

          {/* Thumbnail Images */}
          {/* eslint-disable-next-line @next/next/no-img-element */}
          <img
            src={getProductImage(product, 0)}
            alt={product.name}
            className="object-cover transition-transform duration-500 group-hover:scale-105"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
          />
          {getProductImage(product, 1) !== getProductImage(product, 0) && (
            /* eslint-disable-next-line @next/next/no-img-element */
            <img
              src={getProductImage(product, 1)}
              alt={`${product.name} - alternate view`}
              className="object-cover opacity-0 transition-all duration-500 group-hover:opacity-100 group-hover:scale-105"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
            />
          )}

          {/* Secondary Image (hover effect) */}
          {product.hoverImage && product.hoverImage !== product.image && (
            /* eslint-disable-next-line @next/next/no-img-element */
            <img
              src={product.hoverImage}
              alt={`${product.name} - alternate view`}
              className="absolute inset-0 w-full h-full object-cover opacity-0 transition-opacity duration-300 group-hover:opacity-100"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
              onError={(e) => {
                // Hide the hover image if it fails to load
                const target = e.target as HTMLImageElement;
                target.style.display = "none";
              }}
            />
          )}
        </div>

        <div className="mt-4 text-center">
          <h3 className="text-lg font-medium text-gray-900 group-hover:text-gray-700 transition-colors">
            {product.name}
          </h3>
          <div className="mt-1 flex items-center justify-center space-x-2">
            <span className="text-lg font-semibold text-gray-900">
              ₹{product.price.toLocaleString()}
            </span>
            {product.originalPrice && product.originalPrice > product.price && (
              <span className="text-sm text-gray-500 line-through">
                ₹{product.originalPrice.toLocaleString()}
              </span>
            )}
          </div>
        </div>
      </Link>
    </div>
  );
};

const ProductGrid = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadProducts = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch featured products from API
        const apiProducts = await fetchFeaturedProducts(4); // Get exactly 4 featured products

        if (apiProducts && apiProducts.length > 0) {
          // Convert API products to display format (limit to 4 products)
          const displayProducts = apiProducts
            .slice(0, 4) // Ensure we only show maximum 4 products
            .map((apiProduct, index) => convertAPIProduct(apiProduct, index));
          setProducts(displayProducts);
        } else {
          // Fallback to static products if no API products (only 4 products)
          const fallbackProducts: Product[] = [
            {
              id: "1",
              name: "Creative Canvas",
              price: 2499.0,
              originalPrice: 2999.0,
              image: fallbackImages[0],
              hoverImage: fallbackImages[0],
              slug: "creative-canvas",
              badge: "Bestseller",
            },
            {
              id: "2",
              name: "Green Thumb Haven",
              price: 1899.0,
              originalPrice: 2299.0,
              image: fallbackImages[1],
              hoverImage: fallbackImages[1],
              slug: "green-thumb-haven",
              badge: "New",
            },
            {
              id: "3",
              name: "Home Sweet Nest",
              price: 2199.0,
              originalPrice: 2599.0,
              image: fallbackImages[2],
              hoverImage: fallbackImages[2],
              slug: "home-sweet-nest",
              badge: "Popular",
            },
            {
              id: "4",
              name: "Coffee Craze",
              price: 2399.0,
              originalPrice: 2899.0,
              image: fallbackImages[3],
              hoverImage: fallbackImages[3],
              slug: "coffee-craze",
              badge: "Coffee Lover",
            },
          ];
          setProducts(fallbackProducts);
        }
      } catch (err) {
        console.error("Error loading products:", err);
        setError("Failed to load products");

        // Use fallback products on error (only 4 products)
        const fallbackProducts: Product[] = [
          {
            id: "1",
            name: "Creative Canvas",
            price: 2499.0,
            originalPrice: 2999.0,
            image: fallbackImages[0],
            hoverImage: fallbackImages[0],
            slug: "creative-canvas",
            badge: "Bestseller",
          },
          {
            id: "2",
            name: "Green Thumb Haven",
            price: 1899.0,
            originalPrice: 2299.0,
            image: fallbackImages[1],
            hoverImage: fallbackImages[1],
            slug: "green-thumb-haven",
            badge: "New",
          },
          {
            id: "3",
            name: "Home Sweet Nest",
            price: 2199.0,
            originalPrice: 2599.0,
            image: fallbackImages[2],
            hoverImage: fallbackImages[2],
            slug: "home-sweet-nest",
            badge: "Popular",
          },
          {
            id: "4",
            name: "Coffee Craze",
            price: 2399.0,
            originalPrice: 2899.0,
            image: fallbackImages[3],
            hoverImage: fallbackImages[3],
            slug: "coffee-craze",
            badge: "Coffee Lover",
          },
        ];
        setProducts(fallbackProducts);
      } finally {
        setLoading(false);
      }
    };

    loadProducts();
  }, []);

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-4">
            <div className="text-2xl font-bold text-gray-900 bg-gray-100 px-6 py-2 rounded-lg">
              ⭐ BEST SELLING READY-TO-SHIP ⭐
            </div>
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Featured Products
          </h2>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="text-center py-12">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            <p className="mt-2 text-gray-600">Loading products...</p>
          </div>
        )}

        {/* Error State */}
        {error && !loading && (
          <div className="text-center py-12">
            <p className="text-red-600 mb-4">{error}</p>
            <p className="text-gray-600">Showing fallback products</p>
          </div>
        )}

        {/* Product Grid */}
        {!loading && products.length > 0 && (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
            {products.map((product) => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>
        )}

        {/* Shop All Button */}
        {!loading && (
          <div className="text-center">
            <Link
              href="/collections/all"
              className="inline-flex items-center px-8 py-3 text-lg font-semibold text-gray-900 bg-white border-2 border-gray-900 hover:bg-gray-900 hover:text-white transition-colors duration-200 rounded-md"
            >
              SHOP ALL
              <svg
                className="ml-2 w-4 h-4"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
                  clipRule="evenodd"
                />
              </svg>
            </Link>
          </div>
        )}
      </div>
    </section>
  );
};

export default ProductGrid;
