'use client';

import React, { useState } from 'react';

/**
 * Generate public S3 URL for images
 * This works because our S3 bucket has guest read access enabled
 */
function generatePublicS3Url(key: string): string {
  // Extract bucket name from amplify config
  const bucketName = 'customine4ae3bb77bc244f68885ca662b9bd1b4e'; // From cli-inputs.json
  const region = 'us-east-1'; // Default Amplify region
  
  // Generate public URL
  return `https://${bucketName}.s3.${region}.amazonaws.com/public/${key}`;
}

/**
 * Determines if a string is an S3 key that needs URL conversion
 */
const isS3Key = (src: string): boolean => {
  return src.startsWith('products/') || src.includes('products/') || src.startsWith('gallery/') || src.includes('gallery/');
};

/**
 * Determines if a string is a relative path
 */
const isRelativePath = (src: string): boolean => {
  return src.startsWith('/');
};

/**
 * Determines if a string is a full URL
 */
const isFullUrl = (src: string): boolean => {
  return src.startsWith('http');
};

/**
 * Processes image source for PUBLIC display (no authentication required)
 * Converts S3 keys to public URLs for home page and public components
 */
const processPublicImageSrc = (src: string): string => {
  // If it's a relative path, use it as is
  if (isRelativePath(src)) {
    return src;
  }

  // If it's an S3 key, convert to public URL
  if (isS3Key(src)) {
    return generatePublicS3Url(src);
  }

  // If it's already a full URL, use it
  if (isFullUrl(src)) {
    return src;
  }

  // For any other format, use fallback
  return '/placeholder-product.png';
};

interface PublicS3ImageProps {
  src: string;
  alt: string;
  className?: string;
  fallbackSrc?: string;
  onLoad?: () => void;
  onError?: () => void;
  sizes?: string;
}

/**
 * PublicS3Image component for displaying S3 images without authentication
 * Perfect for home page, collections, hero slider, and other public-facing components
 * Uses direct S3 URLs instead of signed URLs for better performance
 */
const PublicS3Image: React.FC<PublicS3ImageProps> = ({
  src,
  alt,
  className = '',
  fallbackSrc = '/placeholder-product.png',
  onLoad,
  onError,
  sizes
}) => {
  const [error, setError] = useState(false);
  const [loading, setLoading] = useState(true);

  // Process the image source to get the best public URL
  const imageUrl = error ? fallbackSrc : src;

  const handleLoad = () => {
    setLoading(false);
    if (onLoad) onLoad();
  };

  const handleError = () => {
    console.warn('Failed to load image:', imageUrl);
    setError(true);
    setLoading(false);
    if (onError) onError();
  };

  return (
    <div className={`relative ${loading ? 'bg-gray-200 animate-pulse' : ''}`}>
      {/* eslint-disable-next-line @next/next/no-img-element */}
      <img
        src={imageUrl}
        alt={alt}
        className={`${className} ${loading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`}
        onLoad={handleLoad}
        onError={handleError}
        sizes={sizes}
      />
    </div>
  );
};

export default PublicS3Image;
