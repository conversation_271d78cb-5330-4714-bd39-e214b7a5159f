"use client";
import React, { useState } from 'react';
import { authAPI } from '../utils/api';

interface LoginModalProps {
  open: boolean;
  onClose: () => void;
  onSignup?: () => void;
  onLogin?: () => void;
}

type LoginStep = 'login' | 'forgotPassword' | 'verification' | 'success';

export default function LoginModal({ open, onClose, onSignup, onLogin }: LoginModalProps) {
  const [step, setStep] = useState<LoginStep>('login');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [resendLoading, setResendLoading] = useState(false);
  const [resendCooldown, setResendCooldown] = useState(0);

  // Cooldown timer for resend button
  React.useEffect(() => {
    if (resendCooldown > 0) {
      const timer = setTimeout(() => {
        setResendCooldown(resendCooldown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [resendCooldown]);

  // Reset form when modal closes
  React.useEffect(() => {
    if (!open) {
      setStep('login');
      setEmail('');
      setPassword('');
      setVerificationCode('');
      setNewPassword('');
      setConfirmPassword('');
      setError('');
      setResendCooldown(0);
    }
  }, [open]);

  if (!open) return null;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    if (!email || !password) {
      setError('Please enter both email and password.');
      setLoading(false);
      return;
    }

    try {
      const result = await authAPI.signIn({ email, password });

      if (result.success) {
        // Store user data
        localStorage.setItem('user', JSON.stringify(result.data.user));

        // Call parent login handler
        if (onLogin) onLogin();

        // Close modal
        onClose();

        // Reset form
        setEmail('');
        setPassword('');

      } else {
        setError(result.error || 'Login failed');
      }
    } catch (error) {
      console.error('Login error:', error);
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleForgotPasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    if (!email) {
      setError('Please enter your email address.');
      setLoading(false);
      return;
    }

    try {
      const result = await authAPI.resetPassword(email);

      if (result.success) {
        setStep('verification');
        setError('');
      } else {
        setError(result.error || 'Failed to send reset code. Please try again.');
      }
    } catch (error) {
      console.error('Reset password error:', error);
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordReset = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    if (!verificationCode || !newPassword || !confirmPassword) {
      setError('Please fill in all fields.');
      setLoading(false);
      return;
    }

    if (newPassword !== confirmPassword) {
      setError('Passwords do not match.');
      setLoading(false);
      return;
    }

    if (newPassword.length < 8) {
      setError('Password must be at least 8 characters long.');
      setLoading(false);
      return;
    }

    try {
      const result = await authAPI.confirmResetPassword(email, verificationCode, newPassword);

      if (result.success) {
        setStep('success');
        setTimeout(() => {
          setStep('login');
          setEmail('');
          setPassword('');
          setVerificationCode('');
          setNewPassword('');
          setConfirmPassword('');
          setError('');
        }, 3000);
      } else {
        setError(result.error || 'Failed to reset password. Please check your verification code.');
      }
    } catch (error) {
      console.error('Confirm reset password error:', error);
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleResendCode = async () => {
    setResendLoading(true);
    setError('');

    try {
      const result = await authAPI.resetPassword(email);
      if (result.success) {
        setResendCooldown(60); // 60 seconds cooldown
      } else {
        setError(result.error || 'Failed to resend code.');
      }
    } catch (error) {
      setError('Network error. Please try again.');
    } finally {
      setResendLoading(false);
    }
  };

  const handleSocial = (provider: string) => {
    alert(`Social login with ${provider} (not implemented)`);
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center pointer-events-auto p-4">
      {/* Enhanced overlay background */}
      <div className="absolute inset-0 bg-gradient-to-br from-black/50 to-black/70 backdrop-blur-sm z-0" onClick={onClose} />

      {/* Modern modal container */}
      <div className="relative w-full max-w-md bg-white rounded-3xl shadow-2xl z-10 overflow-hidden animate-fade-in">
        {/* Header with gradient background */}
        <div className="relative bg-gradient-to-br from-[var(--color-navy)] via-[var(--color-teal)] to-[var(--color-navy)] p-8 pb-6">
          {/* Close button */}
          <button
            onClick={onClose}
            className="absolute top-4 right-4 w-8 h-8 flex items-center justify-center rounded-full bg-white/10 hover:bg-white/20 text-white transition-all duration-200 backdrop-blur-sm"
            aria-label="Close login modal"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>

          {/* Logo/Brand area */}
          <div className="text-center mb-4">
            <div className="w-16 h-16 mx-auto mb-4 bg-white/10 rounded-2xl flex items-center justify-center backdrop-blur-sm">
              <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </div>
            <h2 className="text-2xl font-bold text-white mb-2">
              {step === 'login' && 'Welcome Back'}
              {step === 'forgotPassword' && 'Reset Password'}
              {step === 'verification' && 'Verify Your Account'}
              {step === 'success' && 'Success!'}
            </h2>
            <p className="text-white/80 text-sm">
              {step === 'login' && 'Sign in to your account'}
              {step === 'forgotPassword' && 'We\'ll help you reset your password'}
              {step === 'verification' && 'Enter the code we sent you'}
              {step === 'success' && 'Your password has been reset'}
            </p>
          </div>
        </div>

        {/* Content area */}
        <div className="p-8 pt-6 animate-slide-up">

          {step === 'login' && (
            <div className="mb-6">
              <button
                onClick={() => handleSocial('Google')}
                className="w-full flex items-center justify-center py-3.5 px-4 rounded-xl border border-gray-200 bg-white text-gray-700 font-medium hover:bg-gray-50 hover:border-gray-300 transition-all duration-200 shadow-sm hover:shadow-md group"
              >
                {/* eslint-disable-next-line @next/next/no-img-element */}
                <img src="https://www.svgrepo.com/show/475656/google-color.svg" alt="Google" width={20} height={20} className="mr-3 group-hover:scale-110 transition-transform duration-200" />
                <span className="text-gray-800">Continue with Google</span>
              </button>
            </div>
          )}

          {step === 'forgotPassword' && (
            <div className="mb-6 text-center">
              <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
                <p className="text-[var(--color-navy)] text-sm leading-relaxed">
                  Enter your email address and we&apos;ll send you a verification code to reset your password.
                </p>
              </div>
            </div>
          )}

          {step === 'verification' && (
            <div className="mb-6 text-center">
              <div className="bg-green-50 border border-green-200 rounded-xl p-4">
                <p className="text-[var(--color-navy)] mb-2 text-sm">
                  We&apos;ve sent a verification code to:
                </p>
                <p className="text-[var(--color-teal)] font-semibold mb-3 break-all text-sm">
                  {email}
                </p>
                <p className="text-xs text-gray-600">
                  Please enter the code and your new password below.
                </p>
              </div>
            </div>
          )}

          {step === 'login' && (
            <div className="flex items-center my-6">
              <div className="flex-1 h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent" />
              <span className="mx-4 text-gray-500 text-sm font-medium bg-white px-2">or</span>
              <div className="flex-1 h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent" />
            </div>
          )}

          {/* Login Form */}
          {step === 'login' && (
            <form onSubmit={handleSubmit} className="space-y-5">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                  <input
                    type="email"
                    className="w-full px-4 py-3.5 border border-gray-200 rounded-xl bg-gray-50 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[var(--color-teal)] focus:border-transparent transition-all duration-200"
                    placeholder="Enter your email"
                    value={email}
                    onChange={e => setEmail(e.target.value)}
                    autoComplete="email"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Password</label>
                  <input
                    type="password"
                    className="w-full px-4 py-3.5 border border-gray-200 rounded-xl bg-gray-50 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[var(--color-teal)] focus:border-transparent transition-all duration-200"
                    placeholder="Enter your password"
                    value={password}
                    onChange={e => setPassword(e.target.value)}
                    autoComplete="current-password"
                    required
                  />
                </div>
              </div>

              <div className="text-right">
                <button
                  type="button"
                  onClick={() => setStep('forgotPassword')}
                  className="text-sm text-[var(--color-teal)] hover:text-[var(--color-navy)] font-medium transition-colors duration-200"
                >
                  Forgot Password?
                </button>
              </div>

              {error && (
                <div className="bg-red-50 border border-red-200 rounded-xl p-3">
                  <p className="text-red-600 text-sm text-center">{error}</p>
                </div>
              )}

              <button
                type="submit"
                className="w-full py-3.5 rounded-xl font-semibold text-white bg-gradient-to-r from-[var(--color-navy)] to-[var(--color-teal)] hover:from-[var(--color-teal)] hover:to-[var(--color-navy)] transition-all duration-300 disabled:opacity-60 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              disabled={loading}
            >
              {loading ? 'Logging in...' : 'Login'}
            </button>
          </form>
        )}

          {/* Forgot Password Form */}
          {step === 'forgotPassword' && (
            <form onSubmit={handleForgotPasswordSubmit} className="space-y-5">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                <input
                  type="email"
                  className="w-full px-4 py-3.5 border border-gray-200 rounded-xl bg-gray-50 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[var(--color-teal)] focus:border-transparent transition-all duration-200"
                  placeholder="Enter your email"
                  value={email}
                  onChange={e => setEmail(e.target.value)}
                  autoComplete="email"
                  required
                />
              </div>

              {error && (
                <div className="bg-red-50 border border-red-200 rounded-xl p-3">
                  <p className="text-red-600 text-sm text-center">{error}</p>
                </div>
              )}

              <button
                type="submit"
                disabled={loading}
                className="w-full py-3.5 rounded-xl font-semibold text-white bg-gradient-to-r from-[var(--color-navy)] to-[var(--color-teal)] hover:from-[var(--color-teal)] hover:to-[var(--color-navy)] transition-all duration-300 disabled:opacity-60 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                {loading ? 'Sending...' : 'Send Reset Code'}
              </button>

              <div className="text-center">
                <button
                  type="button"
                  onClick={() => setStep('login')}
                  className="text-sm text-gray-600 hover:text-[var(--color-teal)] font-medium transition-colors duration-200 flex items-center justify-center mx-auto"
                >
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                  Back to Login
                </button>
              </div>
            </form>
          )}

          {/* Verification & New Password Form */}
          {step === 'verification' && (
            <form onSubmit={handlePasswordReset} className="space-y-5">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Verification Code</label>
                <input
                  type="text"
                  value={verificationCode}
                  onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                  placeholder="000000"
                  className="w-full px-4 py-3.5 border border-gray-200 rounded-xl bg-gray-50 text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[var(--color-teal)] focus:border-transparent text-center text-2xl tracking-[0.5em] font-mono transition-all duration-200"
                  maxLength={6}
                  required
                />
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">New Password</label>
                  <input
                    type="password"
                    value={newPassword}
                    onChange={(e) => setNewPassword(e.target.value)}
                    placeholder="Enter new password"
                    className="w-full px-4 py-3.5 border border-gray-200 rounded-xl bg-gray-50 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[var(--color-teal)] focus:border-transparent transition-all duration-200"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Confirm New Password</label>
                  <input
                    type="password"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    placeholder="Confirm new password"
                    className="w-full px-4 py-3.5 border border-gray-200 rounded-xl bg-gray-50 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[var(--color-teal)] focus:border-transparent transition-all duration-200"
                    required
                  />
                </div>
              </div>

              {error && (
                <div className="bg-red-50 border border-red-200 rounded-xl p-3">
                  <p className="text-red-600 text-sm text-center">{error}</p>
                </div>
              )}

              <button
                type="submit"
                disabled={loading}
                className="w-full py-3.5 rounded-xl font-semibold text-white bg-gradient-to-r from-[var(--color-navy)] to-[var(--color-teal)] hover:from-[var(--color-teal)] hover:to-[var(--color-navy)] transition-all duration-300 disabled:opacity-60 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                {loading ? 'Resetting...' : 'Reset Password'}
              </button>

              <div className="text-center">
                <p className="text-sm text-gray-600 mb-3">
                  Didn&apos;t receive the code?
                </p>
                <button
                  type="button"
                  onClick={handleResendCode}
                  disabled={resendLoading || resendCooldown > 0}
                  className="text-[var(--color-teal)] hover:text-[var(--color-navy)] font-medium text-sm disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                >
                  {resendLoading ? 'Sending...' :
                   resendCooldown > 0 ? `Resend in ${resendCooldown}s` :
                   'Resend Code'}
                </button>
              </div>

              <div className="text-center">
                <button
                  type="button"
                  onClick={() => setStep('forgotPassword')}
                  className="text-sm text-gray-600 hover:text-[var(--color-teal)] font-medium transition-colors duration-200 flex items-center justify-center mx-auto"
                >
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                  Back to Email
                </button>
              </div>
            </form>
          )}

          {/* Success Message */}
          {step === 'success' && (
            <div className="text-center space-y-6">
              <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                <svg className="w-10 h-10 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Password Reset Successful!</h3>
                <p className="text-gray-600 text-sm leading-relaxed">
                  Your password has been successfully reset. You can now sign in with your new password.
                </p>
              </div>
              <button
                onClick={() => setStep('login')}
                className="w-full py-3.5 rounded-xl font-semibold text-white bg-gradient-to-r from-[var(--color-navy)] to-[var(--color-teal)] hover:from-[var(--color-teal)] hover:to-[var(--color-navy)] transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                Continue to Login
              </button>
            </div>
          )}

          {step === 'login' && (
            <div className="mt-6 pt-6 border-t border-gray-100 text-center">
              <p className="text-sm text-gray-600">
                Don&apos;t have an account?{' '}
                <button
                  type="button"
                  className="text-[var(--color-teal)] font-semibold hover:text-[var(--color-navy)] transition-colors duration-200"
                  onClick={() => { onClose(); if (onSignup) onSignup(); }}
                >
                  Sign Up
                </button>
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}