'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { ChevronLeft, ChevronRight } from './Icons';
import { fetchGalleryByCategory, fetchGalleryBySubcategory, fetchPublicHomepageHeroGallery, type GalleryImage } from '@/utils/galleryAPI';
import PublicS3Image from './PublicS3Image';

interface HeroSliderProps {
  images?: string[];
  onlyImages?: boolean;
  useGalleryAPI?: boolean; // New prop to enable API gallery fetching
  galleryCategory?: 'HOME' | 'LUXURY' | 'CORPORATE' | 'WEDDING' | 'FESTIVAL' | 'PERSONAL'; // Gallery category to fetch
  gallerySubcategory?: string; // Optional subcategory filter
  rounded?: boolean; // Whether to apply rounded corners (default: true for gallery, false for hero)
}

// If images prop is provided, use it for slides, otherwise use default slides
const slides = [
  {
    id: 1,
    title: "NEW GIFT BOX DESIGNS ARE HERE!",
    subtitle: "Fresh Curations for Work and Life with Free Shipping + Handwritten Notecards",
    buttonText: "GIFT SOMEONE NOW",
    buttonLink: "/collections/new-arrival-gift-boxes"
  },
  {
    id: 2,
    title: "NEW! BUDGET-FRIENDLY BOXES FOR ALL OCCASIONS",
    subtitle: "Curated Mailer Style Boxes Featuring Small Batch Brands",
    buttonText: "SHOP CUSTOMINE ESSENTIALS",
    buttonLink: "/collections/customine-essentials"
  },
  {
    id: 3,
    title: "OUR READY-TO-SHIP GIFT BOXES + YOUR LOGO",
    subtitle: "Order On-Demand as You Need Them. Low Order Minimums. Free Shipping (Boxes Over $100)",
    buttonText: "GET STARTED",
    buttonLink: "/pages/branded-gift-boxes-for-clients"
  },
  {
    id: 4,
    title: "Corporate Event Gifts Should Turn Heads",
    subtitle: "Full-Service Custom Gift Design and Delivery Service",
    buttonText: "GET STARTED",
    buttonLink: "/pages/corporate-event-gifts"
  }
];

const HeroSlider = ({ images, onlyImages, useGalleryAPI = false, galleryCategory = 'HOME', gallerySubcategory, rounded = true }: HeroSliderProps) => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const [galleryImages, setGalleryImages] = useState<GalleryImage[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Determine which images to use
  const displayImages = useGalleryAPI ? galleryImages.map(img => img.imageUrl || '') : images;
  const hasImages = displayImages && displayImages.length > 0;

  // Calculate total slides based on what will actually be displayed
  const totalSlides = onlyImages && hasImages
    ? displayImages.length
    : onlyImages && useGalleryAPI && loading
    ? 1 // Loading state shows 1 slide
    : slides.length; // Default slides or fallback

  // Fetch gallery images when useGalleryAPI is enabled
  useEffect(() => {
    if (useGalleryAPI) {
      const loadGalleryImages = async () => {
        try {
          setLoading(true);
          setError(null);

          // Use public API for better performance and no authentication required
          let images: GalleryImage[];
          if (galleryCategory === 'HOME') {
            console.log('🏠 HeroSlider: Fetching HOME category images');
            images = await fetchPublicHomepageHeroGallery();
            console.log(`📸 HeroSlider: Found ${images.length} HOME images`, images);
          } else if (gallerySubcategory) {
            // Fetch by subcategory if provided
            console.log(`🔍 HeroSlider: Fetching ${galleryCategory} > ${gallerySubcategory}`);
            images = await fetchGalleryBySubcategory(galleryCategory, gallerySubcategory);
            console.log(`📸 HeroSlider: Found ${images.length} images for ${gallerySubcategory}`, images);
          } else {
            images = await fetchGalleryByCategory(galleryCategory);
          }

          setGalleryImages(images);
        } catch (err) {
          console.error('Error loading gallery images:', err);
          setError('Failed to load gallery images');
        } finally {
          setLoading(false);
        }
      };

      loadGalleryImages();
    }
  }, [useGalleryAPI, galleryCategory, gallerySubcategory]);

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlaying) return;

    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % totalSlides);
    }, 5000);

    return () => clearInterval(interval);
  }, [isAutoPlaying, totalSlides]);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % totalSlides);
    setIsAutoPlaying(false);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + totalSlides) % totalSlides);
    setIsAutoPlaying(false);
  };

  const goToSlide = (index: number) => {
    setCurrentSlide(index);
    setIsAutoPlaying(false);
  };
  

  return (
    <div className={`relative w-full h-full overflow-hidden bg-gradient-to-br from-gray-100 to-gray-200 group ${rounded ? 'rounded-lg' : ''}`}>
      {/* Slides */}
      <div
        className="flex transition-transform duration-700 ease-in-out h-full"
        style={{ transform: `translateX(-${currentSlide * 100}%)` }}
      >
        {onlyImages && hasImages
          ? displayImages.map((img, idx) => {
              // Get gallery image data if using API
              const galleryImg = useGalleryAPI ? galleryImages[idx] : null;
              const altText = galleryImg?.altText || galleryImg?.title || "Gallery Image";

              return (
                <div
                  key={useGalleryAPI ? galleryImg?.id || idx : idx}
                  className="w-full h-full flex-shrink-0 relative group"
                >
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <PublicS3Image
                    src={img}
                    alt={altText}
                    className="object-cover w-full h-full transition-transform duration-300 group-hover:scale-105"
                    fallbackSrc="/placeholder-image.jpg"
                    sizes="100vw"
                  />
                  {/* Image title overlay */}
                  {galleryImg?.title && (
                    <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4 z-20 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <h4 className="text-white text-sm font-medium truncate">{galleryImg.title}</h4>
                    </div>
                  )}
                </div>
              );
            })
          : onlyImages && useGalleryAPI && loading
          ? (
              // Enhanced loading state for gallery API
              <div className="w-full h-full flex-shrink-0 relative flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200">
                <div className="text-center">
                  <div className="relative">
                    <div className="w-16 h-16 border-4 border-gray-300 border-t-purple-600 rounded-full animate-spin mx-auto mb-4"></div>
                    <div className="absolute inset-0 w-16 h-16 border-4 border-transparent border-r-pink-600 rounded-full animate-spin mx-auto" style={{ animationDirection: 'reverse', animationDuration: '1.5s' }}></div>
                  </div>
                  <p className="text-gray-600 font-medium">Loading gallery images...</p>
                  <div className="mt-2 flex justify-center space-x-1">
                    <div className="w-2 h-2 bg-purple-600 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-pink-600 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              </div>
            )
          : onlyImages && useGalleryAPI && !loading && !hasImages
          ? (
              // Enhanced empty state when no gallery images found
              <div className="w-full h-full flex-shrink-0 relative flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100">
                <div className="text-center px-6">
                  <div className="w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-purple-100 to-pink-100 rounded-full flex items-center justify-center">
                    <svg className="w-12 h-12 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-700 mb-2">No Images Available</h3>
                  <p className="text-gray-500 text-sm">
                    {gallerySubcategory
                      ? `No images found for ${gallerySubcategory.replace(/_/g, ' ').toLowerCase()}`
                      : `No images found for ${galleryCategory?.toLowerCase()} category`
                    }
                  </p>
                </div>
              </div>
            )
          : slides.map((slide) => (
              <div
                key={slide.id}
                className="w-full h-full flex-shrink-0 relative flex items-center justify-center"
                style={{ backgroundColor: '#2F4156' }}
              >
                {/* Content */}
                <div className="relative z-10 h-full flex items-center justify-center w-full">
                  <div className="text-center text-white px-4 max-w-4xl mx-auto">
                    <h1 className="text-3xl md:text-5xl font-bold mb-4 leading-tight">
                      {slide.title}
                    </h1>
                    <p className="text-lg md:text-xl mb-8 leading-relaxed">
                      {slide.subtitle}
                    </p>
                    <Link
                      href={slide.buttonLink}
                      className="inline-block bg-white text-gray-900 px-8 py-3 text-lg font-semibold hover:bg-gray-100 transition-colors duration-200 rounded-md"
                    >
                      {slide.buttonText}
                    </Link>
                  </div>
                </div>
              </div>
            ))}
      </div>

      {/* Enhanced Navigation Arrows - Only show if more than 1 slide */}
      {totalSlides > 1 && (
        <>
          <button
            onClick={prevSlide}
            className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-white/90 backdrop-blur-sm hover:bg-white text-gray-800 p-3 rounded-full shadow-lg transition-all duration-300 z-20 opacity-0 group-hover:opacity-100 hover:scale-110"
            aria-label="Previous slide"
          >
            <ChevronLeft size={20} />
          </button>

          <button
            onClick={nextSlide}
            className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-white/90 backdrop-blur-sm hover:bg-white text-gray-800 p-3 rounded-full shadow-lg transition-all duration-300 z-20 opacity-0 group-hover:opacity-100 hover:scale-110"
            aria-label="Next slide"
          >
            <ChevronRight size={20} />
          </button>
        </>
      )}

      {/* Enhanced Dots Indicator - Only show if more than 1 slide */}
      {totalSlides > 1 && (
        <div className="absolute bottom-3 left-1/2 transform -translate-x-1/2 flex space-x-2 z-20">
          {Array.from({ length: totalSlides }).map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={`transition-all duration-300 rounded-full ${
                index === currentSlide
                  ? 'w-8 h-2 bg-white shadow-md'
                  : 'w-2 h-2 bg-white/60 hover:bg-white/80 hover:scale-125'
              }`}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
      )}

      {/* Loading indicator for gallery API */}
      {useGalleryAPI && loading && (
        <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center z-30">
          <div className="text-white text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-white mb-2"></div>
            <p>Loading gallery...</p>
          </div>
        </div>
      )}

      {/* Error indicator for gallery API */}
      {useGalleryAPI && error && !loading && (
        <div className="absolute bottom-16 left-1/2 transform -translate-x-1/2 bg-red-600 text-white px-4 py-2 rounded-md z-30">
          {error}
        </div>
      )}
    </div>
  );
};

export default HeroSlider;
