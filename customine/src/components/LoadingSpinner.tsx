import React from 'react';
import { Loader2, ShoppingBag, CreditCard } from 'lucide-react';

interface LoadingSpinnerProps {
  type?: 'cart' | 'checkout' | 'general';
  message?: string;
}

export default function LoadingSpinner({ type = 'general', message }: LoadingSpinnerProps) {
  const getIcon = () => {
    switch (type) {
      case 'cart':
        return <ShoppingBag className="w-12 h-12 text-[var(--color-teal)] mb-4" />;
      case 'checkout':
        return <CreditCard className="w-12 h-12 text-[var(--color-teal)] mb-4" />;
      default:
        return <Loader2 className="w-12 h-12 text-[var(--color-teal)] mb-4 animate-spin" />;
    }
  };

  const getMessage = () => {
    if (message) return message;
    
    switch (type) {
      case 'cart':
        return 'Loading your cart...';
      case 'checkout':
        return 'Preparing checkout...';
      default:
        return 'Loading...';
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center" style={{ background: 'var(--color-bg-main)' }}>
      <div className="text-center">
        <div className="relative">
          {getIcon()}
          {type !== 'general' && (
            <div className="absolute inset-0 flex items-center justify-center">
              <Loader2 className="w-6 h-6 text-[var(--color-navy)] animate-spin" />
            </div>
          )}
        </div>
        <h2 className="text-xl font-semibold text-[var(--color-navy)] mb-2">{getMessage()}</h2>
        <p className="text-[var(--color-text-secondary)] text-sm">Please wait while we fetch your data...</p>
        
        {/* Loading dots animation */}
        <div className="flex justify-center mt-4 space-x-1">
          <div className="w-2 h-2 bg-[var(--color-teal)] rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
          <div className="w-2 h-2 bg-[var(--color-teal)] rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
          <div className="w-2 h-2 bg-[var(--color-teal)] rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
        </div>
      </div>
    </div>
  );
}

// Skeleton loader for cart items
export function CartItemSkeleton() {
  return (
    <div className="bg-[var(--color-bg-card)] rounded-xl shadow-sm border border-[var(--color-sky-blue)]/20 p-6 animate-pulse">
      <div className="flex items-start gap-6">
        {/* Image skeleton */}
        <div className="w-24 h-24 bg-[var(--color-bg-alt)] rounded-lg"></div>
        
        {/* Content skeleton */}
        <div className="flex-1 min-w-0">
          <div className="h-5 bg-[var(--color-bg-alt)] rounded mb-2 w-3/4"></div>
          <div className="h-4 bg-[var(--color-bg-alt)] rounded mb-3 w-1/2"></div>
          <div className="h-4 bg-[var(--color-bg-alt)] rounded mb-4 w-1/3"></div>
          
          {/* Quantity controls skeleton */}
          <div className="flex items-center space-x-3">
            <div className="h-4 bg-[var(--color-bg-alt)] rounded w-16"></div>
            <div className="h-10 bg-[var(--color-bg-alt)] rounded w-32"></div>
          </div>
        </div>
        
        {/* Price skeleton */}
        <div className="flex flex-col items-end space-y-4">
          <div className="h-8 bg-[var(--color-bg-alt)] rounded w-24"></div>
          <div className="h-10 bg-[var(--color-bg-alt)] rounded w-10"></div>
        </div>
      </div>
    </div>
  );
}

// Skeleton loader for order summary
export function OrderSummarySkeleton() {
  return (
    <div className="bg-[var(--color-bg-card)] rounded-xl shadow-sm border border-[var(--color-sky-blue)]/20 p-6 animate-pulse">
      <div className="h-6 bg-[var(--color-bg-alt)] rounded mb-6 w-1/2"></div>
      
      {/* Items skeleton */}
      <div className="space-y-4 mb-6">
        {[1, 2, 3].map((i) => (
          <div key={i} className="flex justify-between">
            <div className="flex-1">
              <div className="h-4 bg-[var(--color-bg-alt)] rounded mb-1 w-3/4"></div>
              <div className="h-3 bg-[var(--color-bg-alt)] rounded w-1/2"></div>
            </div>
            <div className="h-4 bg-[var(--color-bg-alt)] rounded w-16"></div>
          </div>
        ))}
      </div>
      
      {/* Summary skeleton */}
      <div className="space-y-3 py-4 border-t border-[var(--color-sky-blue)]/30">
        <div className="flex justify-between">
          <div className="h-4 bg-[var(--color-bg-alt)] rounded w-20"></div>
          <div className="h-4 bg-[var(--color-bg-alt)] rounded w-16"></div>
        </div>
        <div className="flex justify-between">
          <div className="h-4 bg-[var(--color-bg-alt)] rounded w-16"></div>
          <div className="h-4 bg-[var(--color-bg-alt)] rounded w-12"></div>
        </div>
        <div className="flex justify-between pt-3 border-t border-[var(--color-sky-blue)]/30">
          <div className="h-5 bg-[var(--color-bg-alt)] rounded w-12"></div>
          <div className="h-5 bg-[var(--color-bg-alt)] rounded w-20"></div>
        </div>
      </div>
      
      {/* Button skeleton */}
      <div className="h-12 bg-[var(--color-bg-alt)] rounded-xl mt-6"></div>
    </div>
  );
}
