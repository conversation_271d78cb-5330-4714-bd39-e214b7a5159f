'use client';

import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { MouseEvent, ReactNode } from 'react';

interface SafeLinkProps {
  href: string;
  children: ReactNode;
  className?: string;
  onClick?: () => void;
  [key: string]: any;
}

/**
 * SafeLink component that handles navigation gracefully in static exports
 * Falls back to window.location if Next.js navigation fails
 */
export default function SafeLink({ href, children, className, onClick, ...props }: SafeLinkProps) {
  const router = useRouter();

  const handleClick = (e: MouseEvent<HTMLAnchorElement>) => {
    // Call the original onClick if provided
    if (onClick) {
      onClick();
    }

    // For external links or anchors, use default behavior
    if (href.startsWith('http') || href.startsWith('#') || href.startsWith('mailto:') || href.startsWith('tel:')) {
      return;
    }

    // For internal links, try Next.js navigation first, fall back to window.location
    e.preventDefault();
    
    try {
      router.push(href);
    } catch (error) {
      console.warn('Next.js navigation failed, using window.location:', error);
      window.location.href = href;
    }
  };

  return (
    <Link 
      href={href} 
      className={className} 
      onClick={handleClick}
      {...props}
    >
      {children}
    </Link>
  );
}
