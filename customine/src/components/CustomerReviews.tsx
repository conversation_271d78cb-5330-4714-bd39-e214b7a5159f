"use client";

import React, { useState, useEffect } from 'react';
import { Star, ThumbsUp, Flag } from 'lucide-react';
import { fetchProductReviews, getProductReviewStats, markReviewHelpful, reportReview, type Review, type ReviewStats } from '@/utils/reviewAPI';
import WriteReviewModal from './WriteReviewModal';

// Star Rating Component
interface StarRatingProps {
  rating: number;
  size?: 'sm' | 'md' | 'lg';
  showNumber?: boolean;
}

export const StarRating: React.FC<StarRatingProps> = ({ rating, size = 'md', showNumber = false }) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  };

  return (
    <div className="flex items-center">
      {[1, 2, 3, 4, 5].map((star) => (
        <Star
          key={star}
          className={`${sizeClasses[size]} ${
            star <= rating ? 'text-[var(--color-gold)] fill-current' : 'text-gray-300'
          } transition-colors duration-200`}
        />
      ))}
      {showNumber && (
        <span className="ml-2 text-sm text-[var(--color-text-secondary)] font-medium">{rating.toFixed(1)}</span>
      )}
    </div>
  );
};

interface CustomerReviewsProps {
  productId: string;
  productName: string;
}

const CustomerReviews: React.FC<CustomerReviewsProps> = ({ productId, productName }) => {
  const [reviews, setReviews] = useState<Review[]>([]);
  const [stats, setStats] = useState<ReviewStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [isWriteReviewOpen, setIsWriteReviewOpen] = useState(false);

  const loadReviews = async () => {
    try {
      setLoading(true);
      const [reviewsData, statsData] = await Promise.all([
        fetchProductReviews(productId, 20),
        getProductReviewStats(productId)
      ]);
      
      setReviews(reviewsData);
      setStats(statsData);
    } catch (error) {
      console.error('Error loading reviews:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadReviews();
  }, [productId]);

  const handleReviewSubmitted = () => {
    // Reload reviews after new review is submitted
    loadReviews();
  };

  const handleHelpful = async (reviewId: string) => {
    try {
      await markReviewHelpful(reviewId);
      // Reload reviews to show updated helpful count
      loadReviews();
    } catch (error) {
      console.error('Error marking review as helpful:', error);
    }
  };

  const handleReport = async (reviewId: string) => {
    try {
      await reportReview(reviewId);
      alert('Review reported. Thank you for your feedback.');
    } catch (error) {
      console.error('Error reporting review:', error);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return '1 day ago';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`;
    if (diffDays < 365) return `${Math.ceil(diffDays / 30)} months ago`;
    return `${Math.ceil(diffDays / 365)} years ago`;
  };

  const renderStars = (rating: number, size: 'sm' | 'md' | 'lg' = 'md') => {
    return <StarRating rating={rating} size={size} />;
  };

  if (loading) {
    return (
      <div className="py-8">
        <div className="animate-pulse">
          <div className="h-10 bg-gradient-to-r from-[var(--color-bg-main)] to-[var(--color-bg-alt)] rounded-xl w-1/3 mb-8"></div>
          <div className="space-y-6">
            {[1, 2, 3].map((i) => (
              <div key={i} className="border border-[var(--color-sky-blue)] rounded-2xl p-8">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="h-6 bg-gradient-to-r from-[var(--color-bg-main)] to-[var(--color-bg-alt)] rounded w-24"></div>
                  <div className="h-6 bg-gradient-to-r from-[var(--color-bg-main)] to-[var(--color-bg-alt)] rounded w-48"></div>
                </div>
                <div className="h-4 bg-gradient-to-r from-[var(--color-bg-main)] to-[var(--color-bg-alt)] rounded w-3/4 mb-3"></div>
                <div className="h-20 bg-gradient-to-r from-[var(--color-bg-main)] to-[var(--color-bg-alt)] rounded-xl"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="py-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-8 gap-4">
        <div className="space-y-3">
          <h3 className="text-3xl font-bold text-[var(--color-text-primary)]">Customer Reviews</h3>
          {stats && (
            <div className="flex flex-col sm:flex-row sm:items-center gap-3">
              <div className="flex items-center space-x-3">
                {renderStars(Math.round(stats.averageRating))}
                <span className="text-2xl font-bold text-[var(--color-text-primary)]">
                  {stats.averageRating.toFixed(1)}
                </span>
              </div>
              <span className="text-[var(--color-text-secondary)] font-medium">
                Based on {stats.totalReviews} review{stats.totalReviews !== 1 ? 's' : ''}
              </span>
            </div>
          )}
        </div>
        <button
          onClick={() => setIsWriteReviewOpen(true)}
          className="bg-gradient-to-r from-[var(--color-navy)] to-[var(--color-teal)] text-white px-8 py-3 rounded-xl font-semibold hover:from-[var(--color-teal)] hover:to-[var(--color-navy)] transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 flex items-center space-x-2"
        >
          <Star className="w-5 h-5" />
          <span>Write a Review</span>
        </button>
      </div>

      {/* Rating Distribution */}
      {stats && stats.totalReviews > 0 && (
        <div className="bg-gradient-to-br from-[var(--color-bg-main)] to-[var(--color-bg-alt)] rounded-2xl p-8 mb-8 border border-[var(--color-sky-blue)]">
          <h4 className="text-xl font-bold text-[var(--color-text-primary)] mb-6">Rating Breakdown</h4>
          <div className="space-y-4">
            {[5, 4, 3, 2, 1].map((rating) => {
              const count = stats.ratingDistribution[rating as keyof typeof stats.ratingDistribution];
              const percentage = stats.totalReviews > 0 ? (count / stats.totalReviews) * 100 : 0;

              return (
                <div key={rating} className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2 w-16">
                    <span className="text-sm font-medium text-[var(--color-text-primary)] w-3">{rating}</span>
                    <Star className="w-4 h-4 text-[var(--color-gold)] fill-current" />
                  </div>
                  <div className="flex-1 bg-white/60 rounded-full h-3 shadow-inner">
                    <div
                      className="bg-gradient-to-r from-[var(--color-gold)] to-yellow-400 h-3 rounded-full transition-all duration-500 ease-out shadow-sm"
                      style={{ width: `${percentage}%` }}
                    ></div>
                  </div>
                  <span className="text-sm font-semibold text-[var(--color-text-primary)] w-8 text-right">{count}</span>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Reviews List */}
      {reviews.length > 0 ? (
        <div className="space-y-8">
          {reviews.map((review) => (
            <div key={review.id} className="bg-white border border-[var(--color-sky-blue)] rounded-2xl p-8 shadow-sm hover:shadow-lg transition-all duration-300 animate-slide-up">
              {/* Review Header */}
              <div className="flex flex-col sm:flex-row sm:items-start justify-between mb-6 gap-4">
                <div className="flex-1">
                  <div className="flex items-center mb-3 space-x-3">
                    {renderStars(review.rating)}
                    <h4 className="text-xl font-bold text-[var(--color-text-primary)] leading-tight">{review.title}</h4>
                  </div>
                  <div className="flex flex-wrap items-center gap-3 text-sm">
                    <span className="font-semibold text-[var(--color-text-primary)]">{review.reviewerName}</span>
                    {review.isVerifiedPurchase && (
                      <span className="bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 px-3 py-1 rounded-full text-xs font-semibold border border-green-200">
                        ✓ Verified Purchase
                      </span>
                    )}
                    <span className="text-[var(--color-text-secondary)] font-medium">{formatDate(review.createdAt)}</span>
                  </div>
                </div>
              </div>

              {/* Review Content */}
              <div className="mb-6">
                <p className="text-[var(--color-text-primary)] leading-relaxed text-lg font-medium">{review.content}</p>
              </div>

              {/* Review Actions */}
              <div className="flex items-center justify-between pt-6 border-t border-[var(--color-sky-blue)]">
                <div className="flex items-center space-x-6">
                  <button
                    onClick={() => handleHelpful(review.id)}
                    className="flex items-center space-x-2 text-sm text-[var(--color-text-secondary)] hover:text-[var(--color-navy)] transition-colors font-medium group"
                  >
                    <ThumbsUp className="w-4 h-4 group-hover:scale-110 transition-transform" />
                    <span>Helpful {review.isHelpful ? `(${review.isHelpful})` : ''}</span>
                  </button>
                  <button
                    onClick={() => handleReport(review.id)}
                    className="flex items-center space-x-2 text-sm text-[var(--color-text-secondary)] hover:text-red-600 transition-colors font-medium group"
                  >
                    <Flag className="w-4 h-4 group-hover:scale-110 transition-transform" />
                    <span>Report</span>
                  </button>
                </div>
                <div className="text-xs text-[var(--color-text-secondary)] font-medium">
                  Review #{review.id.slice(-8)}
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-16">
          <div className="w-24 h-24 bg-gradient-to-br from-[var(--color-bg-main)] to-[var(--color-bg-alt)] rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
            <Star className="w-12 h-12 text-[var(--color-gold)]" />
          </div>
          <h4 className="text-2xl font-bold text-[var(--color-text-primary)] mb-3">No Reviews Yet</h4>
          <p className="text-[var(--color-text-secondary)] mb-8 text-lg max-w-md mx-auto">Be the first to share your experience with this product and help other customers make informed decisions!</p>
          <button
            onClick={() => setIsWriteReviewOpen(true)}
            className="bg-gradient-to-r from-[var(--color-navy)] to-[var(--color-teal)] text-white px-8 py-4 rounded-xl font-semibold hover:from-[var(--color-teal)] hover:to-[var(--color-navy)] transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 flex items-center space-x-2 mx-auto"
          >
            <Star className="w-5 h-5" />
            <span>Write the First Review</span>
          </button>
        </div>
      )}

      {/* Write Review Modal */}
      <WriteReviewModal
        isOpen={isWriteReviewOpen}
        onClose={() => setIsWriteReviewOpen(false)}
        productId={productId}
        productName={productName}
        onReviewSubmitted={handleReviewSubmitted}
      />
    </div>
  );
};

export default CustomerReviews;
