'use client';

import { useEffect } from 'react';

interface StaticExportWrapperProps {
  children: React.ReactNode;
}

/**
 * Simplified wrapper component to handle static export navigation issues
 * Reduced functionality to prevent infinite loops
 */
export default function StaticExportWrapper({ children }: StaticExportWrapperProps) {

  useEffect(() => {
    // Simplified error handling to prevent infinite loops
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      if (event.reason?.message?.includes('RSC payload') ||
          event.reason?.message?.includes('e[o] is not a function')) {
        // Silently prevent the error from bubbling up
        event.preventDefault();
        return true;
      }
      return false;
    };

    // Only add promise rejection handler (less intrusive)
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, []);

  return <>{children}</>;
}
