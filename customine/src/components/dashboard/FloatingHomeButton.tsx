'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { Home, X } from 'lucide-react';

export default function FloatingHomeButton() {
  const [isVisible, setIsVisible] = useState(true);

  if (!isVisible) {
    return null;
  }

  return (
    <div className="fixed bottom-6 right-6 z-50">
      <div className="relative">
        {/* Dismiss button */}
        <button
          onClick={() => setIsVisible(false)}
          className="absolute -top-2 -right-2 w-6 h-6 bg-gray-500 text-white rounded-full flex items-center justify-center hover:bg-gray-600 transition-colors z-10"
          title="Hide button"
        >
          <X className="w-3 h-3" />
        </button>
        
        {/* Main button */}
        <Link
          href="/"
          target="_blank"
          rel="noopener noreferrer"
          className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-3 rounded-full shadow-lg hover:bg-blue-700 hover:shadow-xl transition-all duration-200 group"
          title="Go to Main Website"
        >
          <Home className="w-5 h-5 group-hover:scale-110 transition-transform" />
          <span className="font-medium">Visit Site</span>
        </Link>
      </div>
    </div>
  );
}
