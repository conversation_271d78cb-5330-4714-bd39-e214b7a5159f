'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { <PERSON>ertCircle, CheckCircle, AlertTriangle, RefreshCw } from 'lucide-react';
import { generateClient } from 'aws-amplify/api';
import { listProducts } from '../../graphql/queries';

interface ApiCheck {
  name: string;
  service: string;
  status: 'checking' | 'success' | 'error';
  response?: string;
  error?: string;
}

const client = generateClient();

export default function ApiStatusChecker() {
  const [checks, setChecks] = useState<ApiCheck[]>([
    { name: 'AWS Amplify GraphQL', service: 'graphql', status: 'checking' },
    { name: 'AWS Cognito Auth', service: 'auth', status: 'checking' },
    { name: 'Product API', service: 'products', status: 'checking' },
    { name: 'DynamoDB Connection', service: 'dynamodb', status: 'checking' },
  ]);

  const checkApi = async (check: ApiCheck): Promise<ApiCheck> => {
    try {
      switch (check.service) {
        case 'graphql':
          try {
            const result = await client.graphql({
              query: listProducts,
              variables: { limit: 1 }
            });
            return {
              ...check,
              status: 'success',
              response: 'GraphQL API connected successfully'
            };
          } catch (error) {
            return {
              ...check,
              status: 'error',
              error: `GraphQL Error: ${error instanceof Error ? error.message : 'Unknown error'}`
            };
          }

        case 'auth':
          try {
            const { getCurrentUser } = await import('aws-amplify/auth');
            await getCurrentUser();
            return {
              ...check,
              status: 'success',
              response: 'AWS Cognito authentication working'
            };
          } catch (error) {
            return {
              ...check,
              status: 'success',
              response: 'AWS Cognito configured (no user logged in)'
            };
          }

        case 'products':
          try {
            const result = await client.graphql({
              query: listProducts
            });
            const products = (result.data as any)?.listProducts?.items || [];
            return {
              ...check,
              status: 'success',
              response: `Product API working - ${products.length} products found`
            };
          } catch (error) {
            return {
              ...check,
              status: 'error',
              error: `Product API Error: ${error instanceof Error ? error.message : 'Unknown error'}`
            };
          }

        case 'dynamodb':
          try {
            // Test DynamoDB connection by trying to query products
            const result = await client.graphql({
              query: listProducts,
              variables: { limit: 1 }
            });
            return {
              ...check,
              status: 'success',
              response: 'DynamoDB connection successful'
            };
          } catch (error) {
            return {
              ...check,
              status: 'error',
              error: `DynamoDB Error: ${error instanceof Error ? error.message : 'Unknown error'}`
            };
          }

        default:
          return {
            ...check,
            status: 'error',
            error: 'Unknown service type'
          };
      }
    } catch (error) {
      return {
        ...check,
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  };

  const runChecks = useCallback(async () => {
    setChecks(prev => prev.map(check => ({ ...check, status: 'checking' })));

    for (let i = 0; i < checks.length; i++) {
      const result = await checkApi(checks[i]);
      setChecks(prev => prev.map((check, index) =>
        index === i ? result : check
      ));
    }
  }, [checks]);

  useEffect(() => {
    runChecks();
  }, [runChecks]);

  const getStatusIcon = (status: ApiCheck['status']) => {
    switch (status) {
      case 'checking':
        return <RefreshCw className="w-4 h-4 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'html':
        return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
    }
  };

  const getStatusColor = (status: ApiCheck['status']) => {
    switch (status) {
      case 'checking':
        return 'border-blue-200 bg-blue-50';
      case 'success':
        return 'border-green-200 bg-green-50';
      case 'html':
        return 'border-yellow-200 bg-yellow-50';
      case 'error':
        return 'border-red-200 bg-red-50';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold text-gray-900">API Status Check</h2>
        <button
          onClick={runChecks}
          className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 transition-colors"
        >
          Refresh
        </button>
      </div>
      
      <div className="space-y-4">
        {checks.map((check, index) => (
          <div key={index} className={`border rounded-lg p-4 ${getStatusColor(check.status)}`}>
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-2">
                {getStatusIcon(check.status)}
                <span className="font-medium">{check.name}</span>
              </div>
              <code className="text-xs text-gray-500">{check.url}</code>
            </div>
            
            {check.error && (
              <div className="text-sm text-red-700 mb-2">
                <strong>Error:</strong> {check.error}
              </div>
            )}
            
            {check.response && (
              <details className="text-sm">
                <summary className="cursor-pointer text-gray-600 hover:text-gray-800">
                  View Response
                </summary>
                <pre className="mt-2 bg-white p-2 rounded border text-xs overflow-auto max-h-32">
                  {check.response}
                </pre>
              </details>
            )}
            
            {check.status === 'html' && (
              <div className="mt-2 text-sm text-yellow-700">
                <strong>Issue:</strong> This endpoint is returning HTML instead of JSON.
                This usually means there&apos;s a PHP error or the file doesn&apos;t exist.
              </div>
            )}
          </div>
        ))}
      </div>
      
      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <h3 className="font-semibold text-gray-900 mb-2">Common Solutions:</h3>
        <ul className="text-sm text-gray-700 space-y-1">
          <li>• <strong>HTML Response:</strong> Check if PHP files exist and have no syntax errors</li>
          <li>• <strong>404 Errors:</strong> Verify file paths and server configuration</li>
          <li>• <strong>Database Errors:</strong> Update credentials in api/config/database.php</li>
          <li>• <strong>Permission Issues:</strong> Ensure PHP files have proper permissions</li>
        </ul>
      </div>
    </div>
  );
}
