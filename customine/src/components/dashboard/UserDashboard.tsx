'use client';

import React, { useState, useEffect } from 'react';

// Temporary UserInquiriesView component until file system issue is resolved
export function UserInquiriesView() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">My Inquiries</h1>
          <p className="text-gray-600">Track your inquiries and responses</p>
        </div>
        <div className="text-sm text-gray-500">
          Access Level: <span className="font-medium text-blue-600">User</span>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-sm border p-12 text-center">
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          User Inquiries View
        </h3>
        <p className="text-gray-600">
          This page will show user&apos;s own inquiries only. Implementation in progress.
        </p>
      </div>
    </div>
  );
}
import { useAuth } from '@/context/AuthContext';
import { useRoleAccess } from '@/hooks/useRoleAccess';
import { inquiryAPI } from '@/utils/inquiryAPI';
import {
  User,
  ShoppingCart,
  Package,
  Calendar,
  MapPin,
  Phone,
  Mail,
  Edit,
  ExternalLink,
  MessageCircle,
  Clock,
  CheckCircle
} from 'lucide-react';
import Link from 'next/link';

export default function UserDashboard() {
  const { user } = useAuth();
  const { userRole, isAdmin } = useRoleAccess();
  const [inquiries, setInquiries] = useState<any[]>([]);
  const [loadingInquiries, setLoadingInquiries] = useState(true);

  // Load user inquiries
  useEffect(() => {
    const loadUserInquiries = async () => {
      if (!user?.id) return;

      setLoadingInquiries(true);
      try {
        const result = await inquiryAPI.getInquiriesForUser(user.id, isAdmin());
        if (result.success) {
          setInquiries(result.data);
        }
      } catch (error) {
        console.error('Error loading inquiries:', error);
      } finally {
        setLoadingInquiries(false);
      }
    };

    loadUserInquiries();
  }, [user?.id, isAdmin]);

  // Calculate stats from real data
  const userStats = {
    totalOrders: 3, // TODO: Replace with real order data
    activeOrders: 1, // TODO: Replace with real order data
    completedOrders: 2, // TODO: Replace with real order data
    wishlistItems: 5, // TODO: Replace with real wishlist data
    totalInquiries: inquiries.length,
    pendingInquiries: inquiries.filter(i => i.status === 'NEW' || i.status === 'PENDING').length,
    respondedInquiries: inquiries.filter(i => i.status === 'RESPONDED' || i.isReplied).length,
    loyaltyPoints: user?.profile?.loyaltyPoints || 0,
    totalSpent: user?.profile?.totalSpent || 0
  };

  const recentOrders = [
    {
      id: '1',
      orderNumber: 'ORD-2024-001',
      status: 'delivered',
      total: 2500,
      date: '2024-01-15',
      items: 1
    },
    {
      id: '2',
      orderNumber: 'ORD-2024-002',
      status: 'processing',
      total: 1800,
      date: '2024-01-20',
      items: 2
    }
  ];

  // Get recent inquiries from real data
  const recentInquiries = inquiries
    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
    .slice(0, 3)
    .map(inquiry => ({
      id: inquiry.id,
      inquiryNumber: `#${inquiry.id.slice(-8)}`,
      subject: inquiry.subject,
      status: inquiry.status,
      type: inquiry.type,
      date: new Date(inquiry.createdAt).toLocaleDateString(),
      priority: inquiry.priority || 'medium'
    }));

  const getStatusColor = (status: string) => {
    switch (status.toUpperCase()) {
      case 'NEW': return 'bg-blue-100 text-blue-800';
      case 'PENDING': return 'bg-yellow-100 text-yellow-800';
      case 'IN_PROGRESS': return 'bg-purple-100 text-purple-800';
      case 'RESPONDED': return 'bg-green-100 text-green-800';
      case 'CLOSED': return 'bg-gray-100 text-gray-800';
      case 'CANCELLED': return 'bg-red-100 text-red-800';
      // Order statuses
      case 'DELIVERED': return 'bg-green-100 text-green-800';
      case 'PROCESSING': return 'bg-blue-100 text-blue-800';
      case 'SHIPPED': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusDisplayName = (status: string) => {
    switch (status.toUpperCase()) {
      case 'NEW': return 'New';
      case 'PENDING': return 'Pending';
      case 'IN_PROGRESS': return 'In Progress';
      case 'RESPONDED': return 'Responded';
      case 'CLOSED': return 'Closed';
      case 'CANCELLED': return 'Cancelled';
      default: return status;
    }
  };

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">
              Welcome back, {user?.profile?.name || user?.name || 'User'}! 👋
            </h1>
            <p className="text-blue-100 mt-1">
              {isAdmin() ? 'Administrator Dashboard' : 'Your Personal Dashboard'}
            </p>
          </div>
          <div className="text-right">
            <div className="text-sm text-blue-100">Access Level</div>
            <div className="text-lg font-semibold capitalize">
              {userRole === 'admin' ? '🔑 Admin' : '👤 User'}
            </div>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="p-3 bg-blue-100 rounded-lg">
              <ShoppingCart className="w-6 h-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Orders</p>
              <p className="text-2xl font-bold text-gray-900">{userStats.totalOrders}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="p-3 bg-green-100 rounded-lg">
              <Package className="w-6 h-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active Orders</p>
              <p className="text-2xl font-bold text-gray-900">{userStats.activeOrders}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="p-3 bg-orange-100 rounded-lg">
              <MessageCircle className="w-6 h-6 text-orange-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">My Inquiries</p>
              <p className="text-2xl font-bold text-gray-900">{userStats.totalInquiries}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="p-3 bg-yellow-100 rounded-lg">
              <Calendar className="w-6 h-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Spent</p>
              <p className="text-2xl font-bold text-gray-900">₹{userStats.totalSpent.toLocaleString()}</p>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Orders */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900">Recent Orders</h2>
              <Link 
                href="/dashboard/orders"
                className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center space-x-1"
              >
                <span>View All</span>
                <ExternalLink className="w-4 h-4" />
              </Link>
            </div>
          </div>
          <div className="p-6">
            {recentOrders.length === 0 ? (
              <div className="text-center py-8">
                <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">No orders yet</p>
                <p className="text-sm text-gray-500">Start shopping to see your orders here</p>
              </div>
            ) : (
              <div className="space-y-4">
                {recentOrders.map((order) => (
                  <div key={order.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="p-2 bg-white rounded-lg">
                        <Package className="w-5 h-5 text-gray-600" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">{order.orderNumber}</p>
                        <p className="text-sm text-gray-600">{order.items} item(s) • {order.date}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium text-gray-900">₹{order.total.toLocaleString()}</p>
                      <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(order.status)}`}>
                        {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Recent Inquiries */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900">Recent Inquiries</h2>
              <Link
                href="/dashboard/inquiries"
                className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center space-x-1"
              >
                <span>View All</span>
                <ExternalLink className="w-4 h-4" />
              </Link>
            </div>
          </div>
          <div className="p-6">
            {recentInquiries.length === 0 ? (
              <div className="text-center py-8">
                <MessageCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">No inquiries yet</p>
                <p className="text-sm text-gray-500">Submit an inquiry to track it here</p>
              </div>
            ) : (
              <div className="space-y-4">
                {recentInquiries.map((inquiry) => (
                  <div key={inquiry.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="p-2 bg-white rounded-lg">
                        <MessageCircle className="w-5 h-5 text-gray-600" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">{inquiry.inquiryNumber}</p>
                        <p className="text-sm text-gray-600 truncate max-w-48">{inquiry.subject}</p>
                        <p className="text-xs text-gray-500">{inquiry.type} • {inquiry.date}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <span className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(inquiry.status)}`}>
                        {(inquiry.status === 'PENDING' || inquiry.status === 'NEW') && <Clock className="w-3 h-3 mr-1" />}
                        {inquiry.status === 'RESPONDED' && <CheckCircle className="w-3 h-3 mr-1" />}
                        {getStatusDisplayName(inquiry.status)}
                      </span>
                      <p className={`text-xs mt-1 ${inquiry.priority === 'high' ? 'text-red-600' : inquiry.priority === 'medium' ? 'text-yellow-600' : 'text-green-600'}`}>
                        {inquiry.priority.toUpperCase()}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-1 gap-6">
        {/* Profile Summary */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900">Profile</h2>
              <Link 
                href="/dashboard/profile"
                className="text-blue-600 hover:text-blue-800"
              >
                <Edit className="w-4 h-4" />
              </Link>
            </div>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <User className="w-5 h-5 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-600">Name</p>
                  <p className="font-medium text-gray-900">
                    {user?.profile?.name || user?.name || 'Not set'}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <Mail className="w-5 h-5 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-600">Email</p>
                  <p className="font-medium text-gray-900">
                    {user?.profile?.email || user?.email || 'Not set'}
                  </p>
                </div>
              </div>
              
              {user?.profile?.phone && (
                <div className="flex items-center space-x-3">
                  <Phone className="w-5 h-5 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-600">Phone</p>
                    <p className="font-medium text-gray-900">{user.profile.phone}</p>
                  </div>
                </div>
              )}
              
              {user?.profile?.city && (
                <div className="flex items-center space-x-3">
                  <MapPin className="w-5 h-5 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-600">Location</p>
                    <p className="font-medium text-gray-900">
                      {user.profile.city}{user.profile.state && `, ${user.profile.state}`}
                    </p>
                  </div>
                </div>
              )}
            </div>
            
            <div className="mt-6 pt-6 border-t border-gray-200">
              <Link
                href="/dashboard/profile"
                className="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-center block"
              >
                Update Profile
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Link
            href="/dashboard/orders"
            className="flex items-center space-x-3 p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors"
          >
            <ShoppingCart className="w-6 h-6 text-blue-600" />
            <div>
              <p className="font-medium text-blue-900">View Orders</p>
              <p className="text-sm text-blue-600">Track your purchases</p>
            </div>
          </Link>

          <Link
            href="/dashboard/inquiries"
            className="flex items-center space-x-3 p-4 bg-orange-50 rounded-lg hover:bg-orange-100 transition-colors"
          >
            <MessageCircle className="w-6 h-6 text-orange-600" />
            <div>
              <p className="font-medium text-orange-900">My Inquiries</p>
              <p className="text-sm text-orange-600">Track inquiry status</p>
            </div>
          </Link>

          <Link
            href="/dashboard/profile"
            className="flex items-center space-x-3 p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors"
          >
            <User className="w-6 h-6 text-green-600" />
            <div>
              <p className="font-medium text-green-900">Edit Profile</p>
              <p className="text-sm text-green-600">Update your information</p>
            </div>
          </Link>

          <a
            href="/"
            className="flex items-center space-x-3 p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors"
          >
            <ExternalLink className="w-6 h-6 text-purple-600" />
            <div>
              <p className="font-medium text-purple-900">Shop Now</p>
              <p className="text-sm text-purple-600">Browse our products</p>
            </div>
          </a>
        </div>
      </div>
    </div>
  );
}
