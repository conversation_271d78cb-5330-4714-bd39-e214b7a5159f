"use client";

import React, { useState, useEffect } from "react";
import { AlertTriangle, X, ExternalLink } from "lucide-react";

export default function ApiNotification() {
  const [showNotification, setShowNotification] = useState(false);
  const [apiWorking, setApiWorking] = useState(true);

  useEffect(() => {
    const checkApi = async () => {
      try {
        // Test AWS Amplify GraphQL connection
        const { generateClient } = await import('aws-amplify/api');
        const { listProducts } = await import('../../graphql/queries');

        const client = generateClient();
        const result = await client.graphql({
          query: listProducts,
          variables: { limit: 1 }
        });

        // If we get here without error, the API is working
        setApiWorking(true);
        setShowNotification(false);
      } catch (error) {
        console.error('AWS Amplify API check failed:', error);
        setApiWorking(false);
        setShowNotification(true);
      }
    };

    checkApi();
  }, []);

  if (!showNotification || apiWorking) {
    return null;
  }

  return (
    <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <AlertTriangle className="h-5 w-5 text-yellow-400" />
        </div>
        <div className="ml-3 flex-1">
          <p className="text-sm text-yellow-700">
            <strong>API Connection Issue:</strong> The admin dashboard is currently running in offline mode with sample data. 
            Some features may not work as expected.
          </p>
          <div className="mt-2 flex space-x-2">
            <a
              href="/dashboard/debug-simple"
              className="inline-flex items-center text-sm text-yellow-700 hover:text-yellow-600 underline"
            >
              <ExternalLink className="w-3 h-3 mr-1" />
              Debug API Issues
            </a>
            <a
              href="/api-test.html"
              target="_blank"
              className="inline-flex items-center text-sm text-yellow-700 hover:text-yellow-600 underline"
            >
              <ExternalLink className="w-3 h-3 mr-1" />
              Test API Directly
            </a>
          </div>
        </div>
        <div className="ml-auto pl-3">
          <div className="-mx-1.5 -my-1.5">
            <button
              onClick={() => setShowNotification(false)}
              className="inline-flex rounded-md p-1.5 text-yellow-500 hover:bg-yellow-100 focus:outline-none"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
