'use client';

import React, { useState, useEffect } from 'react';
import { dashboardAPI, productAPI } from '../../utils/api';

interface DashboardStats {
  totalProducts: number;
  activeProducts: number;
  inactiveProducts: number;
  totalCartItems: number;
  totalCartValue: number;
  averageCartValue: number;
  recentActivity: {
    products: any[];
    cartItems: any[];
  };
}

interface ProductAnalytics {
  categoryStats: Record<string, number>;
  priceRanges: {
    under1000: number;
    range1000to5000: number;
    range5000to10000: number;
    above10000: number;
  };
  totalProducts: number;
  averagePrice: number;
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [productAnalytics, setProductAnalytics] = useState<ProductAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    setLoading(true);
    setError(null);

    try {
      // Load dashboard statistics
      const statsResult = await dashboardAPI.getStats();
      if (statsResult.success) {
        setStats(statsResult.data);
      } else {
        setError(statsResult.error || 'Failed to load dashboard stats');
      }

      // Load product analytics
      const analyticsResult = await dashboardAPI.getProductAnalytics();
      if (analyticsResult.success) {
        setProductAnalytics(analyticsResult.data);
      } else {
        console.error('Failed to load product analytics:', analyticsResult.error);
      }
    } catch (err) {
      console.error('Error loading dashboard data:', err);
      setError('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-7xl mx-auto">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">Admin Dashboard</h1>
          <div className="animate-pulse">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="bg-white p-6 rounded-lg shadow">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-7xl mx-auto">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">Admin Dashboard</h1>
          <div className="bg-red-50 border border-red-200 rounded-lg p-6">
            <h2 className="text-lg font-semibold text-red-800 mb-2">Error Loading Dashboard</h2>
            <p className="text-red-600">{error}</p>
            <button
              onClick={loadDashboardData}
              className="mt-4 bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
          <button
            onClick={loadDashboardData}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Refresh
          </button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-sm font-medium text-gray-500">Total Products</h3>
            <p className="text-2xl font-bold text-gray-900">{stats?.totalProducts || 0}</p>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-sm font-medium text-gray-500">Active Products</h3>
            <p className="text-2xl font-bold text-green-600">{stats?.activeProducts || 0}</p>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-sm font-medium text-gray-500">Cart Items</h3>
            <p className="text-2xl font-bold text-blue-600">{stats?.totalCartItems || 0}</p>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-sm font-medium text-gray-500">Cart Value</h3>
            <p className="text-2xl font-bold text-purple-600">
              ₹{stats?.totalCartValue?.toLocaleString() || 0}
            </p>
          </div>
        </div>

        {/* Product Analytics */}
        {productAnalytics && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <div className="bg-white p-6 rounded-lg shadow">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Products by Category</h3>
              <div className="space-y-2">
                {Object.entries(productAnalytics.categoryStats).map(([category, count]) => (
                  <div key={category} className="flex justify-between">
                    <span className="text-gray-600 capitalize">{category}</span>
                    <span className="font-semibold">{count}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Price Distribution</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-600">Under ₹1,000</span>
                  <span className="font-semibold">{productAnalytics.priceRanges.under1000}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">₹1,000 - ₹5,000</span>
                  <span className="font-semibold">{productAnalytics.priceRanges.range1000to5000}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">₹5,000 - ₹10,000</span>
                  <span className="font-semibold">{productAnalytics.priceRanges.range5000to10000}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Above ₹10,000</span>
                  <span className="font-semibold">{productAnalytics.priceRanges.above10000}</span>
                </div>
              </div>
              <div className="mt-4 pt-4 border-t">
                <div className="flex justify-between">
                  <span className="text-gray-600">Average Price</span>
                  <span className="font-semibold">₹{productAnalytics.averagePrice.toLocaleString()}</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Recent Activity */}
        <div className="bg-white rounded-lg shadow">
          <div className="p-6 border-b">
            <h3 className="text-lg font-semibold text-gray-900">Recent Activity</h3>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Recent Products</h4>
                <div className="space-y-2">
                  {stats?.recentActivity.products.slice(0, 5).map((product: any) => (
                    <div key={product.id} className="flex justify-between items-center">
                      <span className="text-sm text-gray-600 truncate">{product.name}</span>
                      <span className="text-xs text-gray-400">
                        {new Date(product.createdAt).toLocaleDateString()}
                      </span>
                    </div>
                  ))}
                  {(!stats?.recentActivity.products || stats.recentActivity.products.length === 0) && (
                    <p className="text-sm text-gray-500">No recent products</p>
                  )}
                </div>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Recent Cart Activity</h4>
                <div className="space-y-2">
                  {stats?.recentActivity.cartItems.slice(0, 5).map((item: any) => (
                    <div key={item.id} className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">
                        {item.quantity}x item added
                      </span>
                      <span className="text-xs text-gray-400">
                        {new Date(item.createdAt).toLocaleDateString()}
                      </span>
                    </div>
                  ))}
                  {(!stats?.recentActivity.cartItems || stats.recentActivity.cartItems.length === 0) && (
                    <p className="text-sm text-gray-500">No recent cart activity</p>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
