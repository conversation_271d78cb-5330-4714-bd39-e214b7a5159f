'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  LayoutDashboard,
  Package,
  ShoppingCart,
  Users,
  MessageSquare,
  Image,
  List,
  Home,
  Crown,
  Plus,
  Upload,
  Play,
  User,
  UserCog
} from 'lucide-react';

const menuItems = [
  {
    title: 'Dashboard',
    href: '/dashboard',
    icon: LayoutDashboard,
  },
  {
    title: 'Products',
    icon: Package,
    submenu: [
      { title: 'All Products', href: '/dashboard/products', icon: List },
      { title: 'Add Product', href: '/dashboard/products/add', icon: Plus },
      { title: 'Test Import', href: '/dashboard/test-import', icon: Play },
      { title: 'Bulk Import', href: '/dashboard/bulk-import', icon: Upload }
    ]
  },
  {
    title: 'Gallery Management',
    icon: Image,
    submenu: [
      { title: 'Home Gallery', href: '/dashboard/gallery/home', icon: Home },
      { title: 'Luxury Cards', href: '/dashboard/gallery/luxury', icon: Crown },
    ]
  },
  {
    title: 'User Management',
    icon: UserCog,
    submenu: [
      { title: 'All Users', href: '/dashboard/users', icon: Users },
      { title: 'My Profile', href: '/dashboard/profile', icon: User },
    ]
  },
  {
    title: 'Inquiries',
    href: '/dashboard/inquiries',
    icon: MessageSquare,
  },
  {
    title: 'Orders',
    href: '/dashboard/orders',
    icon: ShoppingCart,
  }
];

export default function AdminSidebar() {
  const pathname = usePathname();

  const isActive = (href: string) => {
    if (href === '/dashboard') {
      return pathname === href;
    }
    return pathname.startsWith(href);
  };

  return (
    <aside className="w-64 bg-white shadow-sm border-r border-gray-200 h-full overflow-y-auto">
      <nav className="p-4">

        <ul className="space-y-2">
          {menuItems.map((item, index) => (
            <li key={index}>
              {item.submenu ? (
                <div>
                  <div className="flex items-center space-x-3 px-3 py-2 text-gray-700 font-medium">
                    <item.icon className="w-5 h-5" />
                    <span>{item.title}</span>
                  </div>
                  <ul className="ml-8 mt-1 space-y-1">
                    {item.submenu.map((subItem, subIndex) => (
                      <li key={subIndex}>
                        <Link
                          href={subItem.href}
                          className={`flex items-center space-x-3 px-3 py-2 rounded-lg text-sm transition-colors ${
                            isActive(subItem.href)
                              ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                              : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                          }`}
                        >
                          <subItem.icon className="w-4 h-4" />
                          <span>{subItem.title}</span>
                        </Link>
                      </li>
                    ))}
                  </ul>
                </div>
              ) : (
                <Link
                  href={item.href!}
                  className={`flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors ${
                    isActive(item.href!)
                      ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  }`}
                >
                  <item.icon className="w-5 h-5" />
                  <span>{item.title}</span>
                </Link>
              )}
            </li>
          ))}
        </ul>
      </nav>
    </aside>
  );
}
