'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  LayoutDashboard,
  Package,
  Image,
  UserCog,
  MessageSquare,
  ShoppingCart,
  List,
  Home,
  Plus,
  Upload,
  Play,
  Crown,
  Users,
  User
} from 'lucide-react';
import { useAuth } from '@/context/AuthContext';

interface MenuItem {
  title: string;
  href?: string;
  icon: React.ComponentType<{ className?: string }>;
  submenu?: SubMenuItem[];
  roles: string[]; // Roles that can access this menu item
}

interface SubMenuItem {
  title: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  roles: string[]; // Roles that can access this submenu item
}

// Define menu items with role-based access
const menuItems: MenuItem[] = [
  {
    title: 'Dashboard',
    href: '/dashboard',
    icon: LayoutDashboard,
    roles: ['admin', 'user'], // Both admin and user can access dashboard
  },
  {
    title: 'Products',
    icon: Package,
    roles: ['admin'], // Only admin can access products
    submenu: [
      { title: 'All Products', href: '/dashboard/products', icon: List, roles: ['admin'] },
    //   { title: 'Test Single Product', href: '/dashboard/test-single-product', icon: Play, roles: ['admin'] },
    //   { title: 'Test Import', href: '/dashboard/test-import', icon: Play, roles: ['admin'] },
    //   { title: 'Bulk Import', href: '/dashboard/bulk-import', icon: Upload, roles: ['admin'] }
     ]
  },
  {
    title: 'Gallery Management',
    icon: Image,
    roles: ['admin'], // Only admin can access gallery management
    submenu: [
      { title: 'Home Gallery', href: '/dashboard/gallery/home', icon: Home, roles: ['admin'] },
      { title: 'Luxury Cards', href: '/dashboard/gallery/luxury', icon: Crown, roles: ['admin'] },
    ]
  },
  {
    title: 'User Management',
    icon: UserCog,
    roles: ['admin', 'user'], // Both can access, but different submenu items
    submenu: [
      { title: 'All Users', href: '/dashboard/users', icon: Users, roles: ['admin'] }, // Only admin can see all users
      { title: 'My Profile', href: '/dashboard/profile', icon: User, roles: ['admin', 'user'] }, // Both can access their profile
    ]
  },
  {
    title: 'Orders',
    href: '/dashboard/admin-orders',
    icon: ShoppingCart,
    roles: ['admin'],
  },
  {
    title: 'Orders',
    href: '/dashboard/my-orders',
    icon: ShoppingCart,
    roles: ['user'], 
  },
  {
    title: 'Inquiries',
    icon: MessageSquare,
    roles: ['admin'],
    href: '/dashboard/admin-inquiries'
  },
  {
    title: 'My Inquiries',
    icon: MessageSquare,
    roles: ['user'],
    href: '/dashboard/inquiries'
  },{
    title: 'Review Management',
    icon: MessageSquare,
    roles: ['admin'],
    href: '/dashboard/reviews'
  }
  
];

export default function RoleBasedSidebar() {
  const pathname = usePathname();
  const { user } = useAuth();

  // Get user role from profile, default to 'user' if not set
  const userRole = user?.profile?.role || 'user';

  const isActive = (href: string) => {
    if (href === '/dashboard') {
      return pathname === href;
    }
    return pathname.startsWith(href);
  };

  // Filter menu items based on user role
  const getAccessibleMenuItems = () => {
    return menuItems.filter(item => item.roles.includes(userRole));
  };

  // Filter submenu items based on user role
  const getAccessibleSubMenuItems = (submenu: SubMenuItem[]) => {
    return submenu.filter(subItem => subItem.roles.includes(userRole));
  };

  const accessibleMenuItems = getAccessibleMenuItems();

  return (
    <aside className="w-64 bg-white shadow-sm border-r border-gray-200 h-full overflow-y-auto">
      <nav className="p-4">
        {/* Role indicator */}
        <div className="mb-4 p-3 bg-gray-50 rounded-lg">
          <div className="text-xs text-gray-500 uppercase tracking-wide">Access Level</div>
          <div className="text-sm font-medium text-gray-900 capitalize">
            {userRole === 'admin' ? '🔑 Administrator' : '👤 User Dashboard'}
          </div>
        </div>

        <ul className="space-y-2">
          {accessibleMenuItems.map((item, index) => (
            <li key={index}>
              {item.submenu ? (
                <div>
                  <div className="flex items-center space-x-3 px-3 py-2 text-gray-700 font-medium">
                    <item.icon className="w-5 h-5" />
                    <span>{item.title}</span>
                  </div>
                  <ul className="ml-8 mt-1 space-y-1">
                    {getAccessibleSubMenuItems(item.submenu).map((subItem, subIndex) => (
                      <li key={subIndex}>
                        <Link
                          href={subItem.href}
                          className={`flex items-center space-x-3 px-3 py-2 rounded-lg text-sm transition-colors ${
                            isActive(subItem.href)
                              ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                              : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                          }`}
                        >
                          <subItem.icon className="w-4 h-4" />
                          <span>{subItem.title}</span>
                        </Link>
                      </li>
                    ))}
                  </ul>
                </div>
              ) : (
                <Link
                  href={item.href!}
                  className={`flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors ${
                    isActive(item.href!)
                      ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  }`}
                >
                  <item.icon className="w-5 h-5" />
                  <span>{item.title}</span>
                </Link>
              )}
            </li>
          ))}
        </ul>

        {/* Help section for users */}
        {userRole === 'user' && (
          <div className="mt-8 p-3 bg-blue-50 rounded-lg">
            <div className="text-xs text-blue-600 uppercase tracking-wide mb-1">Need Help?</div>
            <div className="text-sm text-blue-800">
              Contact support for assistance with your orders or account.
            </div>
          </div>
        )}
      </nav>
    </aside>
  );
}
