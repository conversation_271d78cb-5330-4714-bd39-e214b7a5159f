'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { ChevronRight, Home } from 'lucide-react';

export default function AdminBreadcrumb() {
  const pathname = usePathname();
  
  // Generate breadcrumb items from pathname
  const generateBreadcrumbs = () => {
    const paths = pathname.split('/').filter(Boolean);
    const breadcrumbs = [];
    
    // Always start with Home
    breadcrumbs.push({
      label: 'Home',
      href: '/',
      icon: Home,
      isExternal: true,
    });
    
    // Add Dashboard
    breadcrumbs.push({
      label: 'Dashboard',
      href: '/dashboard',
      icon: null,
      isExternal: false,
    });
    
    // Add current path segments
    let currentPath = '';
    for (let i = 1; i < paths.length; i++) { // Skip 'admin' as we already added it
      currentPath += `/${paths[i]}`;
      const fullPath = `/dashboard${currentPath}`;
      
      // Convert path segment to readable label
      const label = paths[i]
        .split('-')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
      
      breadcrumbs.push({
        label,
        href: fullPath,
        icon: null,
        isExternal: false,
      });
    }
    
    return breadcrumbs;
  };
  
  const breadcrumbs = generateBreadcrumbs();
  
  return (
    <nav className="flex items-center space-x-2 text-sm text-gray-600 mb-4">
      {breadcrumbs.map((crumb, index) => (
        <React.Fragment key={crumb.href}>
          {index > 0 && (
            <ChevronRight className="w-4 h-4 text-gray-400" />
          )}
          
          {index === breadcrumbs.length - 1 ? (
            // Current page - not clickable
            <span className="flex items-center space-x-1 text-gray-900 font-medium">
              {crumb.icon && <crumb.icon className="w-4 h-4" />}
              <span>{crumb.label}</span>
            </span>
          ) : (
            // Clickable breadcrumb
            <Link
              href={crumb.href}
              className={`flex items-center space-x-1 hover:text-gray-900 transition-colors ${
                crumb.isExternal ? 'text-blue-600 hover:text-blue-800' : ''
              }`}
              {...(crumb.isExternal ? { target: '_blank', rel: 'noopener noreferrer' } : {})}
            >
              {crumb.icon && <crumb.icon className="w-4 h-4" />}
              <span>{crumb.label}</span>
            </Link>
          )}
        </React.Fragment>
      ))}
    </nav>
  );
}
