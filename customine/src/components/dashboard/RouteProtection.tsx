'use client';

import React from 'react';
import { usePathname, useRouter } from 'next/navigation';
import { useRoleAccess } from '@/hooks/useRoleAccess';
import { Shield, ArrowLeft, Home } from 'lucide-react';
import Link from 'next/link';

interface RouteProtectionProps {
  children: React.ReactNode;
}

export default function RouteProtection({ children }: RouteProtectionProps) {
  const pathname = usePathname();
  const router = useRouter();
  const { canAccessRoute, userRole } = useRoleAccess();

  // Check if user can access the current route
  const hasAccess = canAccessRoute(pathname);

  if (!hasAccess) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="max-w-md w-full mx-4">
          <div className="bg-white rounded-lg shadow-lg p-8 text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Shield className="w-8 h-8 text-red-600" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Access Restricted</h2>
            <p className="text-gray-600 mb-6">
              You don&apos;t have permission to access this page. This section is restricted to administrators only.
            </p>
            <div className="text-sm text-gray-500 mb-6">
              Current access level: <span className="font-medium capitalize">{userRole}</span>
            </div>
            <div className="space-y-3">
              <Link
                href="/dashboard"
                className="w-full bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors inline-flex items-center justify-center space-x-2"
              >
                <Home className="w-4 h-4" />
                <span>Go to Dashboard</span>
              </Link>
              <button
                onClick={() => router.back()}
                className="w-full bg-gray-100 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-200 transition-colors inline-flex items-center justify-center space-x-2"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>Go Back</span>
              </button>
            </div>
            <div className="mt-6 p-4 bg-yellow-50 rounded-lg">
              <p className="text-sm text-yellow-800">
                <strong>Need access?</strong> Contact your administrator to request the necessary permissions.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
