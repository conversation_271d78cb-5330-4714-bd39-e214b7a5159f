"use client";

import React, { useState, useEffect } from "react";
import { AlertCircle, CheckCircle, Wifi } from "lucide-react";

export default function ApiStatus() {
  const [apiStatus, setApiStatus] = useState<'checking' | 'online' | 'offline'>('checking');
  const [lastChecked, setLastChecked] = useState<Date | null>(null);

  const checkApiStatus = async () => {
    try {
      // Test AWS Amplify GraphQL connection
      const { generateClient } = await import('aws-amplify/api');
      const { listProducts } = await import('../../graphql/queries');

      const client = generateClient();
      const result = await client.graphql({
        query: listProducts,
        variables: { limit: 1 }
      });

      // If we get here without error, the API is working
      setApiStatus('online');
    } catch (error) {
      console.error('AWS Amplify API check failed:', error);
      setApiStatus('offline');
    }

    setLastChecked(new Date());
  };

  useEffect(() => {
    checkApiStatus();
    
    // Check every 30 seconds
    const interval = setInterval(checkApiStatus, 30000);
    
    return () => clearInterval(interval);
  }, []);

  const getStatusColor = () => {
    switch (apiStatus) {
      case 'online': return 'text-green-600';
      case 'offline': return 'text-red-600';
      default: return 'text-yellow-600';
    }
  };

  const getStatusIcon = () => {
    switch (apiStatus) {
      case 'online': return <CheckCircle className="w-4 h-4" />;
      case 'offline': return <AlertCircle className="w-4 h-4" />;
      default: return <Wifi className="w-4 h-4 animate-pulse" />;
    }
  };

  const getStatusText = () => {
    switch (apiStatus) {
      case 'online': return 'API Online';
      case 'offline': return 'API Offline';
      default: return 'Checking...';
    }
  };

  return (
    <div className={`flex items-center space-x-2 text-sm ${getStatusColor()}`}>
      {getStatusIcon()}
      <span>{getStatusText()}</span>
      {lastChecked && (
        <span className="text-xs text-gray-500">
          ({lastChecked.toLocaleTimeString()})
        </span>
      )}
      {apiStatus === 'offline' && (
        <button
          onClick={checkApiStatus}
          className="text-xs bg-red-100 text-red-700 px-2 py-1 rounded hover:bg-red-200"
        >
          Retry
        </button>
      )}
    </div>
  );
}
