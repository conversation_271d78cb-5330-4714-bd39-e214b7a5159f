'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Package, Calendar, IndianRupee, Eye, Search, Filter, Edit, X } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import { useRoleAccess } from '@/hooks/useRoleAccess';
import toast from 'react-hot-toast';

interface Order {
  id: string;
  orderNumber: string;
  userId: string;
  customerName: string;
  customerEmail: string;
  customerPhone?: string;
  status: string;
  priority?: string;
  totalAmount: number;
  shippingAddress?: string;
  trackingNumber?: string;
  adminNotes?: string;
  internalNotes?: string;
  assignedTo?: string;
  estimatedDelivery?: string;
  createdAt: string;
  updatedAt?: string;
  items: OrderItem[];
}

interface OrderItem {
  id: string;
  productName: string;
  quantity: number;
  price: number;
  giftType?: string;
}

export default function UserOrdersView() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editForm, setEditForm] = useState({
    status: '',
    priority: '',
    trackingNumber: '',
    adminNotes: '',
    internalNotes: '',
    assignedTo: '',
    estimatedDelivery: '',
    shippingAddress: ''
  });
  
  const { user } = useAuth();
  const { isAdmin, userRole } = useRoleAccess();

  const handleOrderSelect = (order: Order) => {
    setSelectedOrder(order);
    setIsEditing(false);
    // Initialize edit form with current values
    setEditForm({
      status: order.status || 'pending',
      priority: order.priority || 'medium',
      trackingNumber: order.trackingNumber || '',
      adminNotes: order.adminNotes || '',
      internalNotes: order.internalNotes || '',
      assignedTo: order.assignedTo || '',
      estimatedDelivery: order.estimatedDelivery || '',
      shippingAddress: order.shippingAddress || ''
    });
  };

  const handleSaveEdit = async () => {
    if (!selectedOrder || !isAdmin()) return;

    try {
      // TODO: Replace with actual API call
      console.log('Updating order:', selectedOrder.id, editForm);

      // Update the order in the local state for now
      const updatedOrders = orders.map(order =>
        order.id === selectedOrder.id
          ? {
              ...order,
              ...editForm,
              updatedAt: new Date().toISOString()
            }
          : order
      );
      setOrders(updatedOrders);

      // Update selected order
      setSelectedOrder({
        ...selectedOrder,
        ...editForm,
        updatedAt: new Date().toISOString()
      });

      setIsEditing(false);
      toast.success('Order updated successfully');
    } catch (error) {
      console.error('Error updating order:', error);
      toast.error('Failed to update order');
    }
  };

  const handleQuickStatusUpdate = async (orderId: string, newStatus: string) => {
    try {
      // TODO: Replace with actual API call
      console.log('Updating order status:', orderId, newStatus);

      // Update the order in the local state for now
      const updatedOrders = orders.map(order =>
        order.id === orderId
          ? { ...order, status: newStatus, updatedAt: new Date().toISOString() }
          : order
      );
      setOrders(updatedOrders);

      toast.success('Status updated successfully');
    } catch (error) {
      console.error('Error updating status:', error);
      toast.error('Failed to update status');
    }
  };

  const loadOrders = useCallback(async () => {
    setLoading(true);
    try {
      // Mock data for now - replace with actual API call
      const mockOrders: Order[] = [
        {
          id: '1',
          orderNumber: 'ORD-2024-001',
          userId: user?.id || '',
          customerName: user?.name || 'Current User',
          customerEmail: user?.email || '',
          customerPhone: user?.phone || '+91-90426-71801',
          status: 'delivered',
          priority: 'medium',
          totalAmount: 2500,
          shippingAddress: 'Madambakkam, Chennai-600126',
          trackingNumber: 'TRK123456789',
          adminNotes: 'Customer requested express delivery',
          internalNotes: 'VIP customer - handle with care',
          assignedTo: '<EMAIL>',
          estimatedDelivery: '2024-01-20',
          createdAt: '2024-01-15T10:30:00Z',
          updatedAt: '2024-01-18T14:20:00Z',
          items: [
            { id: '1', productName: 'Luxury Gift Box', quantity: 1, price: 2500, giftType: 'luxury' }
          ]
        },
        {
          id: '2',
          orderNumber: 'ORD-2024-002',
          userId: user?.id || '',
          customerName: user?.name || 'Current User',
          customerEmail: user?.email || '',
          status: 'processing',
          totalAmount: 1800,
          createdAt: '2024-01-20T14:15:00Z',
          items: [
            { id: '2', productName: 'Custom Wedding Box', quantity: 2, price: 900, giftType: 'custom' }
          ]
        }
      ];

      // Filter orders based on user role
      if (isAdmin()) {
        // Admin can see all orders
        setOrders(mockOrders);
      } else {
        // Regular users can only see their own orders
        const userOrders = mockOrders.filter(order => order.userId === user?.id);
        setOrders(userOrders);
      }
    } catch (error) {
      console.error('Error loading orders:', error);
    } finally {
      setLoading(false);
    }
  }, [user?.id, user?.name, user?.email, user?.phone, isAdmin]);

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'processing': return 'bg-blue-100 text-blue-800';
      case 'shipped': return 'bg-purple-100 text-purple-800';
      case 'delivered': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'urgent': return 'text-red-600';
      case 'high': return 'text-orange-600';
      case 'medium': return 'text-yellow-600';
      case 'low': return 'text-green-600';
      default: return 'text-gray-600';
    }
  };

  const filteredOrders = orders.filter(order => {
    const matchesSearch = order.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.customerName.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = !statusFilter || order.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  // useEffect hooks after function definitions
  useEffect(() => {
    loadOrders();
  }, []); // Removed loadOrders to prevent infinite loop

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading orders...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            {isAdmin() ? 'All Orders' : 'My Orders'}
          </h1>
          <p className="text-gray-600">
            {isAdmin() 
              ? 'Manage and track all customer orders' 
              : 'Track your order history and current status'
            }
          </p>
        </div>
        <div className="text-sm text-gray-500">
          Access Level: <span className="font-medium capitalize">{userRole}</span>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search by order number or customer name..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
          <div className="sm:w-48">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">All Statuses</option>
              <option value="pending">Pending</option>
              <option value="processing">Processing</option>
              <option value="shipped">Shipped</option>
              <option value="delivered">Delivered</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>
        </div>
      </div>

      {/* Orders List */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        {filteredOrders.length === 0 ? (
          <div className="text-center py-12">
            <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No orders found</h3>
            <p className="text-gray-600">
              {searchTerm || statusFilter 
                ? 'Try adjusting your search criteria' 
                : isAdmin() 
                  ? 'No orders have been placed yet' 
                  : 'You haven\'t placed any orders yet'
              }
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Order
                  </th>
                  {isAdmin() && (
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Customer
                    </th>
                  )}
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Amount
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredOrders.map((order) => (
                  <tr key={order.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{order.orderNumber}</div>
                        <div className="text-sm text-gray-500">{order.items.length} item(s)</div>
                      </div>
                    </td>
                    {isAdmin() && (
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{order.customerName}</div>
                          <div className="text-sm text-gray-500">{order.customerEmail}</div>
                        </div>
                      </td>
                    )}
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(order.status)}`}>
                        {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center text-sm font-medium text-gray-900">
                        <IndianRupee className="w-4 h-4 mr-1" />
                        {order.totalAmount.toLocaleString()}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center text-sm text-gray-900">
                        <Calendar className="w-4 h-4 mr-2 text-gray-400" />
                        {new Date(order.createdAt).toLocaleDateString()}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center space-x-2">
                        {isAdmin() && (
                          <select
                            value={order.status}
                            onChange={(e) => handleQuickStatusUpdate(order.id, e.target.value)}
                            className="text-xs px-2 py-1 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <option value="pending">Pending</option>
                            <option value="processing">Processing</option>
                            <option value="shipped">Shipped</option>
                            <option value="delivered">Delivered</option>
                            <option value="cancelled">Cancelled</option>
                          </select>
                        )}
                        <button
                          onClick={() => handleOrderSelect(order)}
                          className="text-blue-600 hover:text-blue-900 flex items-center space-x-1"
                        >
                          <Eye className="w-4 h-4" />
                          <span>View</span>
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Order Details Modal */}
      {selectedOrder && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">Order Details</h3>
                <div className="flex items-center space-x-2">
                  {isAdmin() && (
                    <>
                      {isEditing ? (
                        <>
                          <button
                            onClick={handleSaveEdit}
                            className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors text-sm"
                          >
                            Save Changes
                          </button>
                          <button
                            onClick={() => setIsEditing(false)}
                            className="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors text-sm"
                          >
                            Cancel
                          </button>
                        </>
                      ) : (
                        <button
                          onClick={() => setIsEditing(true)}
                          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm flex items-center space-x-1"
                        >
                          <Edit className="w-4 h-4" />
                          <span>Edit Order</span>
                        </button>
                      )}
                    </>
                  )}
                  <button
                    onClick={() => setSelectedOrder(null)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>
              </div>
              
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Order Number</label>
                    <p className="text-sm text-gray-900">{selectedOrder.orderNumber}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Status</label>
                    {isAdmin() && isEditing ? (
                      <select
                        value={editForm.status}
                        onChange={(e) => setEditForm({...editForm, status: e.target.value})}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      >
                        <option value="pending">Pending</option>
                        <option value="processing">Processing</option>
                        <option value="shipped">Shipped</option>
                        <option value="delivered">Delivered</option>
                        <option value="cancelled">Cancelled</option>
                      </select>
                    ) : (
                      <p className="text-sm">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(selectedOrder.status)}`}>
                          {selectedOrder.status.charAt(0).toUpperCase() + selectedOrder.status.slice(1)}
                        </span>
                      </p>
                    )}
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Priority</label>
                    {isAdmin() && isEditing ? (
                      <select
                        value={editForm.priority}
                        onChange={(e) => setEditForm({...editForm, priority: e.target.value})}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      >
                        <option value="low">Low</option>
                        <option value="medium">Medium</option>
                        <option value="high">High</option>
                        <option value="urgent">Urgent</option>
                      </select>
                    ) : (
                      <p className={`text-sm font-medium ${getPriorityColor(selectedOrder.priority || 'medium')}`}>
                        {(selectedOrder.priority || 'medium').toUpperCase()}
                      </p>
                    )}
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Tracking Number</label>
                    {isAdmin() && isEditing ? (
                      <input
                        type="text"
                        value={editForm.trackingNumber}
                        onChange={(e) => setEditForm({...editForm, trackingNumber: e.target.value})}
                        placeholder="Enter tracking number"
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      />
                    ) : (
                      <p className="text-sm text-gray-900">{selectedOrder.trackingNumber || 'Not assigned'}</p>
                    )}
                  </div>
                </div>
                
                {isAdmin() && (
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Customer</label>
                      <p className="text-sm text-gray-900">{selectedOrder.customerName}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Email</label>
                      <p className="text-sm text-gray-900">{selectedOrder.customerEmail}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Phone</label>
                      <p className="text-sm text-gray-900">{selectedOrder.customerPhone || 'Not provided'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Assigned To</label>
                      {isEditing ? (
                        <input
                          type="text"
                          value={editForm.assignedTo}
                          onChange={(e) => setEditForm({...editForm, assignedTo: e.target.value})}
                          placeholder="Admin name or email"
                          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        />
                      ) : (
                        <p className="text-sm text-gray-900">{selectedOrder.assignedTo || 'Unassigned'}</p>
                      )}
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Estimated Delivery</label>
                      {isEditing ? (
                        <input
                          type="date"
                          value={editForm.estimatedDelivery}
                          onChange={(e) => setEditForm({...editForm, estimatedDelivery: e.target.value})}
                          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        />
                      ) : (
                        <p className="text-sm text-gray-900">
                          {selectedOrder.estimatedDelivery ? new Date(selectedOrder.estimatedDelivery).toLocaleDateString() : 'Not set'}
                        </p>
                      )}
                    </div>
                  </div>
                )}

                {/* Shipping Address */}
                <div>
                  <label className="text-sm font-medium text-gray-500">Shipping Address</label>
                  {isAdmin() && isEditing ? (
                    <textarea
                      value={editForm.shippingAddress}
                      onChange={(e) => setEditForm({...editForm, shippingAddress: e.target.value})}
                      placeholder="Enter shipping address"
                      rows={2}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    />
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">{selectedOrder.shippingAddress || 'Not provided'}</p>
                  )}
                </div>
                
                <div>
                  <label className="text-sm font-medium text-gray-500">Order Items</label>
                  <div className="mt-2 space-y-2">
                    {selectedOrder.items.map((item) => (
                      <div key={item.id} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                        <div>
                          <p className="text-sm font-medium text-gray-900">{item.productName}</p>
                          <p className="text-xs text-gray-500">Quantity: {item.quantity}</p>
                          {item.giftType && (
                            <p className="text-xs text-blue-600 capitalize">{item.giftType} Gift</p>
                          )}
                        </div>
                        <div className="text-sm font-medium text-gray-900">
                          ₹{(item.price * item.quantity).toLocaleString()}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                
                <div className="border-t pt-4">
                  <div className="flex justify-between items-center">
                    <span className="text-lg font-medium text-gray-900">Total Amount</span>
                    <span className="text-lg font-bold text-gray-900">₹{selectedOrder.totalAmount.toLocaleString()}</span>
                  </div>
                </div>

                {/* Admin Notes Section */}
                {isAdmin() && (
                  <div className="border-t pt-4">
                    <h4 className="text-lg font-medium text-gray-900 mb-3">Admin Notes</h4>
                    <div className="space-y-3">
                      <div className="bg-yellow-50 p-4 rounded-lg border-l-4 border-yellow-400">
                        <h5 className="font-medium text-gray-900 mb-2">Admin Notes</h5>
                        {isEditing ? (
                          <textarea
                            value={editForm.adminNotes}
                            onChange={(e) => setEditForm({...editForm, adminNotes: e.target.value})}
                            placeholder="Add admin notes..."
                            rows={3}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                          />
                        ) : (
                          <p className="text-gray-700 whitespace-pre-wrap">{selectedOrder.adminNotes || 'No admin notes'}</p>
                        )}
                      </div>
                      <div className="bg-red-50 p-4 rounded-lg border-l-4 border-red-400">
                        <h5 className="font-medium text-gray-900 mb-2">Internal Notes</h5>
                        {isEditing ? (
                          <textarea
                            value={editForm.internalNotes}
                            onChange={(e) => setEditForm({...editForm, internalNotes: e.target.value})}
                            placeholder="Add internal notes (not visible to customer)..."
                            rows={3}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                          />
                        ) : (
                          <p className="text-gray-700 whitespace-pre-wrap">{selectedOrder.internalNotes || 'No internal notes'}</p>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
