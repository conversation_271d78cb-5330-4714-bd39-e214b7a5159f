'use client';

import React, { Component, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

/**
 * Error boundary specifically for navigation and RSC payload errors
 * This helps handle the "e[o] is not a function" and RSC payload errors gracefully
 */
export default class NavigationErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // Check if this is a navigation-related error
    const isNavigationError = 
      error.message?.includes('RSC payload') ||
      error.message?.includes('e[o] is not a function') ||
      error.message?.includes('Failed to fetch') ||
      error.stack?.includes('router') ||
      error.stack?.includes('navigation');

    if (isNavigationError) {
      console.warn('Navigation error caught by boundary:', error);
      return { hasError: true, error };
    }

    // For non-navigation errors, let them bubble up
    throw error;
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.warn('Navigation error boundary caught:', error, errorInfo);
    
    // Try to recover by reloading the page after a short delay
    setTimeout(() => {
      if (this.state.hasError) {
        console.log('Attempting to recover from navigation error...');
        window.location.reload();
      }
    }, 2000);
  }

  render() {
    if (this.state.hasError) {
      // Show fallback UI or auto-recover
      return (
        this.props.fallback || (
          <div className="flex items-center justify-center min-h-[200px] p-4">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Loading page...</p>
              <p className="text-sm text-gray-400 mt-2">If this takes too long, the page will refresh automatically.</p>
            </div>
          </div>
        )
      );
    }

    return this.props.children;
  }
}
