import Head from 'next/head';

interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: string;
  structuredData?: object;
  noIndex?: boolean;
}

export default function SEO({
  title = "Customine | Curated & Custom Gift Boxes in India",
  description = "Thoughtfully designed gift boxes for weddings, corporate events, client appreciation, festive celebrations, and personal milestones. Pan-India shipping available.",
  keywords = "gift boxes India, custom gift boxes, corporate gifts, wedding gifts, festive gifts, curated gifts, personalized gifts, luxury gift boxes",
  image = "https://customine.in/og-image.jpg",
  url = "https://customine.in",
  type = "website",
  structuredData,
  noIndex = false,
}: SEOProps) {
  const fullTitle = title.includes('Customine') ? title : `${title} | Customine`;
  
  return (
    <Head>
      <title>{fullTitle}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      
      {/* Robots */}
      <meta name="robots" content={noIndex ? "noindex, nofollow" : "index, follow"} />
      
      {/* Open Graph */}
      <meta property="og:type" content={type} />
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={image} />
      <meta property="og:url" content={url} />
      <meta property="og:site_name" content="Customine" />
      <meta property="og:locale" content="en_IN" />
      
      {/* Twitter */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:site" content="@customine" />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={image} />
      
      {/* Canonical URL */}
      <link rel="canonical" href={url} />
      
      {/* Structured Data */}
      {structuredData && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
        />
      )}
    </Head>
  );
}
