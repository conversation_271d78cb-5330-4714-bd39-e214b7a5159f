'use client';

import { usePathname } from 'next/navigation';
import { useEffect, useState } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

interface ConditionalLayoutProps {
  children: React.ReactNode;
}

export default function ConditionalLayout({ children }: ConditionalLayoutProps) {
  const pathname = usePathname();
  const [isClient, setIsClient] = useState(false);

  // Ensure we're on the client side to prevent hydration mismatch
  useEffect(() => {
    setIsClient(true);
  }, []);

  // During SSR or before hydration, render a basic layout
  if (!isClient) {
    return (
      <div suppressHydrationWarning>
        <Header />
        <main>{children}</main>
        <Footer />
      </div>
    );
  }

  // Check if current route is an admin route
  const isAdminRoute = pathname.startsWith('/dashboard');

  // For admin routes, don't show header and footer
  if (isAdminRoute) {
    return <>{children}</>;
  }

  // For all other routes, show header and footer
  return (
    <>
      <Header />
      {children}
      <Footer />
    </>
  );
}
