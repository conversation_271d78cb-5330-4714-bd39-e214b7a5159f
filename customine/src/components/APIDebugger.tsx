'use client';

import React, { useState, useEffect } from 'react';
import { testAPIConnection, fetchProducts } from '../utils/productAPI';

interface APIStatus {
  connected: boolean;
  hasProducts: boolean;
  error?: string;
  productCount?: number;
}

export default function APIDebugger() {
  const [apiStatus, setApiStatus] = useState<APIStatus | null>(null);
  const [loading, setLoading] = useState(false);
  const [lastChecked, setLastChecked] = useState<Date | null>(null);

  const checkAPI = async () => {
    setLoading(true);
    try {
      const testResult = await testAPIConnection();
      let productCount = 0;
      
      if (testResult.connected && testResult.hasProducts) {
        try {
          const products = await fetchProducts();
          productCount = products.items.length;
        } catch (error) {
          console.error('Error fetching product count:', error);
        }
      }
      
      setApiStatus({
        ...testResult,
        productCount
      });
      setLastChecked(new Date());
    } catch (error) {
      setApiStatus({
        connected: false,
        hasProducts: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    checkAPI();
  }, []);

  const getStatusColor = () => {
    if (!apiStatus) return 'bg-gray-100 text-gray-800';
    if (!apiStatus.connected) return 'bg-red-100 text-red-800';
    if (!apiStatus.hasProducts) return 'bg-yellow-100 text-yellow-800';
    return 'bg-green-100 text-green-800';
  };

  const getStatusText = () => {
    if (!apiStatus) return 'Checking...';
    if (!apiStatus.connected) return 'API Disconnected';
    if (!apiStatus.hasProducts) return 'Database Empty';
    return `API Connected (${apiStatus.productCount} products)`;
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-4">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-lg font-semibold text-gray-900">API Status</h3>
        <button
          onClick={checkAPI}
          disabled={loading}
          className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
        >
          {loading ? 'Checking...' : 'Refresh'}
        </button>
      </div>
      
      <div className="space-y-2">
        <div className="flex items-center space-x-2">
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor()}`}>
            {getStatusText()}
          </span>
          {lastChecked && (
            <span className="text-xs text-gray-500">
              Last checked: {lastChecked.toLocaleTimeString()}
            </span>
          )}
        </div>
        
        {apiStatus?.error && (
          <div className="text-sm text-red-600 bg-red-50 p-2 rounded">
            <strong>Error:</strong> {apiStatus.error}
          </div>
        )}
        
        {apiStatus && (
          <div className="text-xs text-gray-600 space-y-1">
            <div>Connected: {apiStatus.connected ? '✅' : '❌'}</div>
            <div>Has Products: {apiStatus.hasProducts ? '✅' : '❌'}</div>
            {apiStatus.productCount !== undefined && (
              <div>Product Count: {apiStatus.productCount}</div>
            )}
          </div>
        )}
        
        {apiStatus && !apiStatus.hasProducts && apiStatus.connected && (
          <div className="text-sm text-blue-600 bg-blue-50 p-2 rounded">
            <strong>Tip:</strong> The database is empty. Go to{' '}
            <a href="/dashboard/bulk-import" className="underline">
              Admin → Bulk Import
            </a>{' '}
            to populate it with sample products.
          </div>
        )}
      </div>
    </div>
  );
}
