'use client';

import React, { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import SafeLink from './SafeLink';
import { Search, User, ShoppingCart, Menu, X, LogOut } from './Icons';
import LoginModal from './LoginModal';
import SignupModal from './SignupModal';
import { useCart } from '../context/CartContext';
import { useAuth } from '../context/AuthContext';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [loginOpen, setLoginOpen] = useState(false);
  const [signupOpen, setSignupOpen] = useState(false);
  const [userMenuOpen, setUserMenuOpen] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const [mobileReadyToShipOpen, setMobileReadyToShipOpen] = useState(false);
  const [mobileCustomGiftsOpen, setMobileCustomGiftsOpen] = useState(false);
  const [mobileSpecialOccasionsOpen, setMobileSpecialOccasionsOpen] = useState(false);
  const [mobileSeasonalItemsOpen, setMobileSeasonalItemsOpen] = useState(false);
  const [mobileInHouseProductsOpen, setMobileInHouseProductsOpen] = useState(false);
  const [mobileLuxuryCardsOpen, setMobileLuxuryCardsOpen] = useState(false);


  const { items } = useCart();
  const { user, isLoggedIn, logout } = useAuth();
  const cartCount = isClient ? items.reduce((sum, item) => sum + item.quantity, 0) : 0;

  const userMenuRef = useRef<HTMLDivElement>(null);

  // Ensure we're on the client side
  useEffect(() => {
    setIsClient(true);
  }, []);

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);
  const toggleSearch = () => setIsSearchOpen(!isSearchOpen);

  // Close user menu when clicking outside or pressing Escape
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setUserMenuOpen(false);
      }
    };

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setUserMenuOpen(false);
      }
    };

    if (userMenuOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [userMenuOpen]);

  return (
    <header className="bg-white shadow-sm">
  
      {/* Main Header Section */}
      <div className="bg-white border-b border-gray-200 py-4 relative">
        {/* Contact Us - Absolute Positioned Top Right */}
        <Link href="/pages/contact-us" className="absolute top-2 right-4 flex items-center space-x-2 text-pink-500 hover:text-pink-600 transition-colors z-10 mr-22">
          <span className="text-pink-500">📞</span>
          <span className="text-sm font-medium hidden sm:inline">Contact Us</span>
          <span className="text-sm font-medium sm:hidden">Call</span>
        </Link>
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between w-full">

            {/* Logo Section - Left */}
            <div className="flex-shrink-0">
              <Link href="/" className="inline-block hover:opacity-80 transition-opacity">
               {/* eslint-disable-next-line @next/next/no-img-element */}
                <img
                  src="/logo.png"
                  alt="Customine - Curated Gift Boxes"
                  width={500}
                  height={150}
                  className="h-16 sm:h-20 lg:h-24 xl:h-28 w-auto"
                  loading="eager"
                  style={{ display: 'block' }}
                />
              </Link>
            </div>

            {/* Navigation Menu - Center */}
            <div className="hidden lg:flex items-center justify-center flex-1 space-x-3 xl:space-x-4 whitespace-nowrap px-6">
              {/* 1. Ready to Ship Gifts */}
              <div className="relative dropdown-parent">
                <Link href="/collections/all" className="text-gray-700 hover:text-gray-900 text-sm font-medium py-2 whitespace-nowrap">
                  Ready to Ship Gifts
                </Link>
                <div className="absolute left-0 mt-0 w-64 bg-white shadow-lg rounded-md z-[9999] dropdown-menu">
                  <div className="py-2">
                    <Link href="/collections/all?filter=luxury" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      Luxury
                    </Link>
                    <Link href="/collections/all?filter=budget" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      Budget
                    </Link>
                  </div>
                </div>
              </div>

              {/* 2. Custom Gifts */}
              <div className="relative dropdown-parent">
                <span className="text-gray-700 hover:text-gray-900 text-sm font-medium py-2 whitespace-nowrap cursor-pointer">
                  Custom Gifts
                </span>
                <div className="absolute left-0 mt-0 w-96 bg-white shadow-lg rounded-md z-[9999] dropdown-menu">
                  <div className="py-2">
                    <Link href="/pages/add-your-own-logo" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 leading-relaxed">
                      Ready-to-ship gifts + your branding<br />
                      <span className="text-xs text-gray-500">(ADD-YOUR-OWN-LOGO)</span>
                    </Link>
                    <Link href="/pages/full-custom" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      Full Custom + your branding
                    </Link>
                  </div>
                </div>
              </div>

              {/* 3. Special Occasions */}
              <div className="relative dropdown-parent">
                <span className="text-gray-700 hover:text-gray-900 text-sm font-medium py-2 whitespace-nowrap cursor-pointer">
                  Special Occasions
                </span>
                <div className="absolute left-0 mt-0 w-80 bg-white shadow-lg rounded-md z-[9999] dropdown-menu">
                  <div className="py-2">
                    <Link href="/pages/corporate-gifting" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      Corporate Gifting Solutions
                    </Link>
                    <Link href="/pages/wedding-welcome-gifts" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      Wedding Guest Welcome Gifts
                    </Link>
                    <Link href="/pages/event-conference-gifting" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      Event & Conference Gifting
                    </Link>
                    <Link href="/pages/client-thank-you" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      Premium Client Thank You Gifts
                    </Link>
                    <Link href="/pages/bridal-party-gifts" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      Bridal Party & Groomsmen Keepsakes
                    </Link>
                    <Link href="/pages/milestone-gifts" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      Milestone Celebration Gifts
                    </Link>
                    <Link href="/pages/travel-retreat-gifting" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      Travel & Retreat Gifting
                    </Link>
                  </div>
                </div>
              </div>

              {/* 4. Seasonal Items */}
              <div className="relative dropdown-parent">
                <span className="text-gray-700 hover:text-gray-900 text-sm font-medium py-2 whitespace-nowrap cursor-pointer">
                  Seasonal Items
                </span>
                <div className="absolute left-0 mt-0 w-64 bg-white shadow-lg rounded-md z-[9999] dropdown-menu">
                  <div className="py-2">
                    <Link href="/pages/diwali-gifting" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      Diwali Gifting
                    </Link>
                    <Link href="/pages/christmas-gifting" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      Christmas Gifting
                    </Link>
                    <Link href="/pages/new-year-gifting" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      New Year Gifting
                    </Link>
                    <Link href="/pages/valentine-gifting" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      Valentine&apos;s Day Gifting
                    </Link>
                  </div>
                </div>
              </div>

              {/* 5. Everyday Occasions */}
              <Link href="/pages/everyday-occasions" className="text-gray-700 hover:text-gray-900 text-sm font-medium whitespace-nowrap">
                Everyday Occasions
              </Link>

              {/* 6. In-House Products */}
              <div className="relative dropdown-parent">
                <Link href="/pages/in-house-products" className="text-gray-700 hover:text-gray-900 text-sm font-medium py-2 whitespace-nowrap">
                  In-House Products
                </Link>
                <div className="absolute left-0 mt-0 w-80 bg-white shadow-lg rounded-md z-[9999] dropdown-menu">
                  <div className="py-2">
                    <div className="px-4 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide border-b">
                      Exclusively Crafted, Only at Customine
                    </div>
                    <Link href="/pages/in-house-products" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      Room & Car Fresheners (sprays, diffusers)
                    </Link>
                    <div className="px-4 py-2 text-xs text-gray-500 italic">
                      Unique, handmade gift sets unavailable online
                    </div>
                  </div>
                </div>
              </div>

              {/* 7. Luxury Cards */}
              <div className="relative dropdown-parent">
                <Link href="/pages/luxury-cards" className="text-gray-700 hover:text-gray-900 text-sm font-medium py-2 whitespace-nowrap">
                  Luxury Cards
                </Link>
                <div className="absolute left-0 mt-0 w-80 bg-white shadow-lg rounded-md z-[9999] dropdown-menu">
                  <div className="py-2">
                    <div className="px-4 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide border-b">
                      Where Words Meet Elegance
                    </div>
                    <Link href="/pages/luxury-cards" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      Wedding Thank-You Notes
                    </Link>
                    <Link href="/pages/luxury-cards" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      Proposal or Bridesmaid Invites
                    </Link>
                    <Link href="/pages/luxury-cards" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      Milestone Birthdays
                    </Link>
                    <Link href="/pages/luxury-cards" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      Baby Announcements
                    </Link>
                    <Link href="/pages/luxury-cards" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      Luxury Corporate Gifting
                    </Link>
                    <Link href="/pages/luxury-cards" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      Personalized Event Message Cards
                    </Link>
                    <div className="border-t mx-4 mt-2 pt-2">
                      <div className="px-4 py-1 text-xs text-gray-500 italic">
                        Add-on option for Customine Gift Boxes
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* 10. About Customine */}
              <Link href="/pages/about-us" className="text-gray-700 hover:text-gray-900 text-sm font-medium whitespace-nowrap">
                About Us
              </Link>
            </div>

            {/* Right Section - Right Corner */}
            <div className="flex items-center space-x-2 flex-shrink-0 ml-4">
              {/* Search Icon */}
              <button
                onClick={toggleSearch}
                className="p-2 text-gray-600 hover:text-gray-900"
              >
                <Search size={20} />
              </button>

              {/* User Icon with Dropdown */}
              <div className="relative" ref={userMenuRef}>
                <button
                  className="p-2 text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md transition-colors"
                  onClick={() => {
                    if (isLoggedIn) {
                      setUserMenuOpen(!userMenuOpen);
                    } else {
                      setLoginOpen(true);
                    }
                  }}
                  aria-expanded={userMenuOpen}
                  aria-haspopup="true"
                >
                  <User size={20} />
                </button>

                {/* User Dropdown Menu - Only show when logged in */}
                {isLoggedIn && userMenuOpen && (
                  <div className="absolute right-0 mt-2 w-64 sm:w-72 bg-white shadow-xl rounded-lg border border-gray-200 z-[9999] overflow-hidden user-dropdown-animate max-w-[calc(100vw-2rem)] sm:max-w-none">
                    {/* User Info Header */}
                    <div className="px-4 py-3 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                          <span className="text-blue-600 font-semibold text-sm">
                            {user?.name?.charAt(0).toUpperCase() || 'U'}
                          </span>
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="font-medium text-gray-900 text-sm truncate">
                            {user?.name || 'User'}
                          </div>
                          <div className="text-gray-500 text-xs truncate">
                            {user?.email || '<EMAIL>'}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Menu Items */}
                    <div className="py-2">
                      <Link
                        href="/dashboard"
                        className="flex items-center px-4 py-2.5 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-150"
                        onClick={() => setUserMenuOpen(false)}
                      >
                        <div className="w-5 h-5 mr-3 flex items-center justify-center">
                          <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                          </svg>
                        </div>
                        <span>My Account</span>
                      </Link>

                      <Link
                        href="/dashboard/my-orders"
                        className="flex items-center px-4 py-2.5 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-150"
                        onClick={() => setUserMenuOpen(false)}
                      >
                        <div className="w-5 h-5 mr-3 flex items-center justify-center">
                          <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                          </svg>
                        </div>
                        <span>My Orders</span>
                      </Link>

                      <Link
                        href="/dashboard"
                        className="flex items-center px-4 py-2.5 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-150"
                        onClick={() => setUserMenuOpen(false)}
                      >
                        <div className="w-5 h-5 mr-3 flex items-center justify-center">
                          <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                          </svg>
                        </div>
                        <span>Wishlist</span>
                      </Link>

                      {/* Divider */}
                      <div className="border-t border-gray-100 my-2"></div>

                      {/* Logout Button */}
                      <button
                        className="flex items-center w-full px-4 py-2.5 text-sm text-red-600 hover:bg-red-50 transition-colors duration-150"
                        onClick={() => {
                          logout();
                          setUserMenuOpen(false);
                        }}
                      >
                        <div className="w-5 h-5 mr-3 flex items-center justify-center">
                          <LogOut className="w-4 h-4" />
                        </div>
                        <span>Logout</span>
                      </button>
                    </div>
                  </div>
                )}
              </div>

              {/* Cart Icon */}
              <SafeLink href="/cart" className="p-2 text-gray-600 hover:text-gray-900 relative">
                <ShoppingCart size={20} />
                {isClient && cartCount > 0 && (
                  <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                    {cartCount}
                  </span>
                )}
              </SafeLink>

              {/* Mobile menu button */}
              <button
                onClick={toggleMenu}
                className="lg:hidden p-2 rounded-md text-gray-600 hover:text-gray-900"
              >
                {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
              </button>
            </div>
          </div>
        </div>
      </div>


      {/* Search Bar */}
      {isSearchOpen && (
        <div className="bg-gray-50 border-b border-gray-200 py-4">
          <div className="container mx-auto px-4">
            <div className="max-w-md mx-auto">
              <input
                type="text"
                placeholder="Search products..."
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                autoFocus
              />
            </div>
          </div>
        </div>
      )}

      {/* Mobile menu */}
      {isMenuOpen && (
        <div className="lg:hidden bg-white border-t border-gray-200">
          <div className="container mx-auto px-4 py-4">
            <div className="space-y-3">
              <button
                type="button"
                className="block w-full text-left py-2 text-gray-700 hover:text-gray-900 font-medium focus:outline-none"
                onClick={() => setMobileReadyToShipOpen((open) => !open)}
                aria-expanded={mobileReadyToShipOpen}
              >
                Ready to Ship Gifts
                <span className="float-right inline-block align-middle ml-2">
                  {mobileReadyToShipOpen ? (
                    <svg width="18" height="18" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6 12l4-4 4 4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg>
                  ) : (
                    <svg width="18" height="18" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6 8l4 4 4-4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg>
                  )}
                </span>
              </button>
              {mobileReadyToShipOpen && (
                <div className="pl-4 space-y-1">
                  <Link
                    href="/collections/all?filter=luxury"
                    className="block py-2 text-gray-600 hover:text-gray-900 text-sm"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Luxury
                  </Link>
                  <Link
                    href="/collections/all?filter=budget"
                    className="block py-2 text-gray-600 hover:text-gray-900 text-sm"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Budget
                  </Link>
                </div>
              )}
              <button
                type="button"
                className="block w-full text-left py-2 text-gray-700 hover:text-gray-900 font-medium focus:outline-none"
                onClick={() => setMobileCustomGiftsOpen((open) => !open)}
                aria-expanded={mobileCustomGiftsOpen}
              >
                Custom Gifts
                <span className="float-right inline-block align-middle ml-2">
                  {mobileCustomGiftsOpen ? (
                    <svg width="18" height="18" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6 12l4-4 4 4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg>
                  ) : (
                    <svg width="18" height="18" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6 8l4 4 4-4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg>
                  )}
                </span>
              </button>
              {mobileCustomGiftsOpen && (
                <div className="pl-4 space-y-1">
                  <Link
                    href="/pages/add-your-own-logo"
                    className="block py-2 text-gray-600 hover:text-gray-900 text-sm"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Ready-to-ship gifts + your branding<br />
                    <span className="text-xs text-gray-400">(ADD-YOUR-OWN-LOGO)</span>
                  </Link>
                  <Link
                    href="/pages/full-custom"
                    className="block py-2 text-gray-600 hover:text-gray-900 text-sm"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Full Custom + your branding
                  </Link>
                </div>
              )}
              <button
                type="button"
                className="block w-full text-left py-2 text-gray-700 hover:text-gray-900 font-medium focus:outline-none"
                onClick={() => setMobileSpecialOccasionsOpen((open) => !open)}
                aria-expanded={mobileSpecialOccasionsOpen}
              >
                Special Occasions
                <span className="float-right inline-block align-middle ml-2">
                  {mobileSpecialOccasionsOpen ? (
                    <svg width="18" height="18" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6 12l4-4 4 4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg>
                  ) : (
                    <svg width="18" height="18" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6 8l4 4 4-4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg>
                  )}
                </span>
              </button>
              {mobileSpecialOccasionsOpen && (
                <div className="pl-4 space-y-1">
                  <Link
                    href="/pages/corporate-gifting"
                    className="block py-2 text-gray-600 hover:text-gray-900 text-sm"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Corporate Gifting Solutions
                  </Link>
                  <Link
                    href="/pages/wedding-welcome-gifts"
                    className="block py-2 text-gray-600 hover:text-gray-900 text-sm"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Wedding Guest Welcome Gifts
                  </Link>
                  <Link
                    href="/pages/event-conference-gifting"
                    className="block py-2 text-gray-600 hover:text-gray-900 text-sm"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Event & Conference Gifting
                  </Link>
                  <Link
                    href="/pages/client-thank-you"
                    className="block py-2 text-gray-600 hover:text-gray-900 text-sm"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Premium Client Thank You Gifts
                  </Link>
                  <Link
                    href="/pages/bridal-party-gifts"
                    className="block py-2 text-gray-600 hover:text-gray-900 text-sm"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Bridal Party & Groomsmen Keepsakes
                  </Link>
                  <Link
                    href="/pages/milestone-gifts"
                    className="block py-2 text-gray-600 hover:text-gray-900 text-sm"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Milestone Celebration Gifts
                  </Link>
                  <Link
                    href="/pages/travel-retreat-gifting"
                    className="block py-2 text-gray-600 hover:text-gray-900 text-sm"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Travel & Retreat Gifting
                  </Link>
                </div>
              )}
              <button
                type="button"
                className="block w-full text-left py-2 text-gray-700 hover:text-gray-900 font-medium focus:outline-none"
                onClick={() => setMobileSeasonalItemsOpen((open) => !open)}
                aria-expanded={mobileSeasonalItemsOpen}
              >
                Seasonal Items
                <span className="float-right inline-block align-middle ml-2">
                  {mobileSeasonalItemsOpen ? (
                    <svg width="18" height="18" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6 12l4-4 4 4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg>
                  ) : (
                    <svg width="18" height="18" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6 8l4 4 4-4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg>
                  )}
                </span>
              </button>
              {mobileSeasonalItemsOpen && (
                <div className="pl-4 space-y-1">
                  <Link
                    href="/pages/diwali-gifting"
                    className="block py-2 text-gray-600 hover:text-gray-900 text-sm"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Diwali Gifting
                  </Link>
                  <Link
                    href="/pages/christmas-gifting"
                    className="block py-2 text-gray-600 hover:text-gray-900 text-sm"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Christmas Gifting
                  </Link>
                  <Link
                    href="/pages/new-year-gifting"
                    className="block py-2 text-gray-600 hover:text-gray-900 text-sm"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    New Year Gifting
                  </Link>
                  <Link
                    href="/pages/valentine-gifting"
                    className="block py-2 text-gray-600 hover:text-gray-900 text-sm"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Valentine's Day Gifting
                  </Link>
                </div>
              )}
              <Link
                href="/collections/everyday"
                className="block py-2 text-gray-700 hover:text-gray-900 font-medium"
                onClick={() => setIsMenuOpen(false)}
              >
                Everyday Occasions
              </Link>
              <button
                type="button"
                className="block w-full text-left py-2 text-gray-700 hover:text-gray-900 font-medium focus:outline-none"
                onClick={() => setMobileInHouseProductsOpen((open) => !open)}
                aria-expanded={mobileInHouseProductsOpen}
              >
                In-House Products
                <span className="float-right inline-block align-middle ml-2">
                  {mobileInHouseProductsOpen ? (
                    <svg width="18" height="18" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6 12l4-4 4 4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg>
                  ) : (
                    <svg width="18" height="18" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6 8l4 4 4-4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg>
                  )}
                </span>
              </button>
              {mobileInHouseProductsOpen && (
                <div className="pl-4 space-y-1">
                  <div className="block py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide">
                    Exclusively Crafted, Only at Customine
                  </div>
                  <Link
                    href="/pages/in-house-products"
                    className="block py-2 text-gray-600 hover:text-gray-900 text-sm"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Room & Car Fresheners (sprays, diffusers)
                  </Link>
                  <div className="block py-2 text-xs text-gray-500 italic">
                    Unique, handmade gift sets unavailable online
                  </div>
                </div>
              )}
              <button
                type="button"
                className="block w-full text-left py-2 text-gray-700 hover:text-gray-900 font-medium focus:outline-none"
                onClick={() => setMobileLuxuryCardsOpen((open) => !open)}
                aria-expanded={mobileLuxuryCardsOpen}
              >
                Luxury Cards
                <span className="float-right inline-block align-middle ml-2">
                  {mobileLuxuryCardsOpen ? (
                    <svg width="18" height="18" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6 12l4-4 4 4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg>
                  ) : (
                    <svg width="18" height="18" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6 8l4 4 4-4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg>
                  )}
                </span>
              </button>
              {mobileLuxuryCardsOpen && (
                <div className="pl-4 space-y-1">
                  <div className="block py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide">
                    Where Words Meet Elegance
                  </div>
                  <Link
                    href="/pages/luxury-cards"
                    className="block py-2 text-gray-600 hover:text-gray-900 text-sm"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Wedding Thank-You Notes
                  </Link>
                  <Link
                    href="/pages/luxury-cards"
                    className="block py-2 text-gray-600 hover:text-gray-900 text-sm"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Proposal or Bridesmaid Invites
                  </Link>
                  <Link
                    href="/pages/luxury-cards"
                    className="block py-2 text-gray-600 hover:text-gray-900 text-sm"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Milestone Birthdays
                  </Link>
                  <Link
                    href="/pages/luxury-cards"
                    className="block py-2 text-gray-600 hover:text-gray-900 text-sm"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Baby Announcements
                  </Link>
                  <Link
                    href="/pages/luxury-cards"
                    className="block py-2 text-gray-600 hover:text-gray-900 text-sm"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Luxury Corporate Gifting
                  </Link>
                  <Link
                    href="/pages/luxury-cards"
                    className="block py-2 text-gray-600 hover:text-gray-900 text-sm"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Personalized Event Message Cards
                  </Link>
                  <div className="block border-t mx-4 mt-2 pt-2 px-4 py-1 text-xs text-gray-500 italic">
                    Add-on option for Customine Gift Boxes
                  </div>
                </div>
              )}
              <Link
                href="/pages/about-us"
                className="block py-2 text-gray-700 hover:text-gray-900 font-medium"
                onClick={() => setIsMenuOpen(false)}
              >
                ABOUT CUSTOMINE
              </Link>


            </div>
          </div>
        </div>
      )}
      <LoginModal
        open={loginOpen}
        onClose={() => setLoginOpen(false)}
        onLogin={() => {
          setLoginOpen(false);
          // User state will be updated by AuthContext
        }}
        onSignup={() => {
          setLoginOpen(false);
          setSignupOpen(true);
        }}
      />
      <SignupModal
        isOpen={signupOpen}
        onClose={() => setSignupOpen(false)}
        onSignup={() => {
          setSignupOpen(false);
          // User state will be updated by AuthContext
        }}
        onLogin={() => {
          setSignupOpen(false);
          setLoginOpen(true);
        }}
      />
    </header>
  );
};

export default Header;
