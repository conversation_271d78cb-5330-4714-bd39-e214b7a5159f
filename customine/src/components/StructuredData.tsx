import Script from 'next/script';

interface StructuredDataProps {
  type?: 'website' | 'product' | 'organization' | 'local-business';
  data?: any;
}

export default function StructuredData({ type = 'website', data }: StructuredDataProps) {
  const getStructuredData = () => {
    const baseData = {
      '@context': 'https://schema.org',
    };

    switch (type) {
      case 'organization':
        return {
          ...baseData,
          '@type': 'Organization',
          name: 'Customine',
          description: 'Curated & Custom Gift Boxes in India for weddings, corporate events, client appreciation, festive celebrations, and personal milestones with pan-India shipping and custom options available.',
          url: 'https://customine.in',
          logo: 'https://customine.in/logo.png',
          contactPoint: {
            '@type': 'ContactPoint',
            telephone: '+91-90426-71801',
            contactType: 'customer service',
            email: '<EMAIL>',
            availableLanguage: ['English', 'Hindi']
          },
          address: {
            '@type': 'PostalAddress',
            addressLocality: 'Madambakkam',
            addressRegion: 'Chennai',
            postalCode: '600126',
            addressCountry: 'IN'
          },
          sameAs: [
            'https://instagram.com/customine.in'
          ],
          ...data
        };

      case 'local-business':
        return {
          ...baseData,
          '@type': 'LocalBusiness',
          name: 'Customine',
          description: 'Curated & Custom Gift Boxes in India',
          url: 'https://customine.in',
          telephone: '+91-90426-71801',
          email: '<EMAIL>',
          address: {
            '@type': 'PostalAddress',
            streetAddress: 'Madambakkam',
            addressLocality: 'Chennai',
            addressRegion: 'Tamil Nadu',
            postalCode: '600126',
            addressCountry: 'IN'
          },
          geo: {
            '@type': 'GeoCoordinates',
            latitude: '12.8503',
            longitude: '80.0504'
          },
          openingHours: 'Mo-Sa 09:00-18:00',
          priceRange: '₹₹',
          servesCuisine: 'Gift Services',
          serviceArea: {
            '@type': 'Country',
            name: 'India'
          },
          ...data
        };

      case 'product':
        return {
          ...baseData,
          '@type': 'Product',
          ...data
        };

      case 'website':
      default:
        return {
          ...baseData,
          '@type': 'WebSite',
          name: 'Customine',
          description: 'Curated & Custom Gift Boxes in India',
          url: 'https://customine.in',
          potentialAction: {
            '@type': 'SearchAction',
            target: 'https://customine.in/collections/all?search={search_term_string}',
            'query-input': 'required name=search_term_string'
          },
          ...data
        };
    }
  };

  return (
    <Script
      id={`structured-data-${type}`}
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(getStructuredData()),
      }}
    />
  );
}
