'use client';

import React, { useState } from 'react';
import { usePayment } from '../hooks/usePayment';
import { useCart } from '../context/CartContext';
import { useAuth } from '../context/AuthContext';
import { Loader2, CreditCard, Truck, Shield, ArrowLeft, User, Mail, Phone, MapPin, Lock, RotateCcw, CheckCircle } from 'lucide-react';
import toast from 'react-hot-toast';
import Link from 'next/link';
import LoadingSpinner from './LoadingSpinner';

interface ShippingAddress {
  fullName: string;
  addressLine1: string;
  addressLine2: string;
  city: string;
  state: string;
  pincode: string;
  country: string;
  phone: string;
  email: string;
}

export default function CheckoutFormNew() {
  const { items, getTotalPrice, initialLoading, hasDataLoaded } = useCart();
  const { user } = useAuth();
  const { paymentState, initiatePayment, resetPayment } = usePayment();
  
  const [shippingAddress, setShippingAddress] = useState<ShippingAddress>({
    fullName: user?.name || '',
    addressLine1: '',
    addressLine2: '',
    city: '',
    state: '',
    pincode: '',
    country: 'India',
    phone: user?.phone || '',
    email: user?.email || ''
  });

  const [notes, setNotes] = useState('');
  const [agreeToTerms, setAgreeToTerms] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState<'online' | 'cod'>('cod');

  const subtotal = getTotalPrice();
  const shipping = 0; // Free shipping
  const tax = Math.round(subtotal * 0.18); // 18% GST
  const total = subtotal + shipping + tax;

  const handleInputChange = (field: keyof ShippingAddress, value: string) => {
    setShippingAddress(prev => ({ ...prev, [field]: value }));
  };

  const validateForm = (): boolean => {
    if (!shippingAddress.fullName.trim()) {
      toast.error('Please enter your full name');
      return false;
    }
    if (!shippingAddress.email.trim() || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(shippingAddress.email)) {
      toast.error('Please enter a valid email address');
      return false;
    }
    if (!shippingAddress.addressLine1.trim()) {
      toast.error('Please enter your address');
      return false;
    }
    if (!shippingAddress.city.trim()) {
      toast.error('Please enter your city');
      return false;
    }
    if (!shippingAddress.state.trim()) {
      toast.error('Please enter your state');
      return false;
    }
    if (!shippingAddress.pincode.trim() || !/^\d{6}$/.test(shippingAddress.pincode)) {
      toast.error('Please enter a valid 6-digit pincode');
      return false;
    }
    if (!shippingAddress.phone.trim() || !/^\d{10}$/.test(shippingAddress.phone)) {
      toast.error('Please enter a valid 10-digit phone number');
      return false;
    }
    if (!agreeToTerms) {
      toast.error('Please agree to the terms and conditions');
      return false;
    }
    return true;
  };

  const handlePlaceOrder = async () => {
    if (!validateForm()) return;

    try {
      const orderData = {
        items: items.map(item => ({
          productId: item.id,
          quantity: item.quantity,
          price: item.price,
          giftType: 'luxury',
          notes: notes
        })),
        subtotal,
        tax,
        shipping,
        total,
        shippingAddress,
        paymentMethod: 'cod',
        notes
      };

      await initiatePayment(orderData);
      toast.success('Order placed successfully!');
    } catch (error: any) {
      console.error('Order placement failed:', error);
      toast.error('Failed to place order. Please try again.');
    }
  };

  // Show initial loading spinner
  if (initialLoading) {
    return <LoadingSpinner type="checkout" />;
  }

  // Only show empty cart after data has been loaded and cart is actually empty
  if (!initialLoading && hasDataLoaded && items.length === 0) {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{ background: 'var(--color-bg-main)' }}>
        <div className="text-center">
          <h2 className="text-2xl font-bold text-[var(--color-navy)] mb-4">Your cart is empty</h2>
          <p className="text-[var(--color-text-secondary)] mb-6">Add some items to your cart before proceeding to checkout.</p>
          <Link href="/collections/all" className="bg-[var(--color-navy)] text-white px-6 py-3 rounded-xl hover:bg-[var(--color-teal)] transition-all duration-300 shadow-lg hover:shadow-xl">
            Continue Shopping
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen" style={{ background: 'var(--color-bg-main)' }}>
      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <Link href="/cart" className="flex items-center text-[var(--color-text-secondary)] hover:text-[var(--color-navy)] transition-colors">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Cart
          </Link>
          <h1 className="text-2xl font-bold text-[var(--color-navy)]">Checkout</h1>
          <div></div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Shipping Information */}
          <div className="lg:col-span-2 space-y-6">
            {/* Shipping Information Card */}
            <div className="bg-[var(--color-bg-card)] rounded-xl shadow-sm border border-[var(--color-sky-blue)]/20 p-6">
              <div className="flex items-center mb-6">
                <div className="w-8 h-8 bg-[var(--color-sky-blue)]/30 rounded-full flex items-center justify-center mr-3">
                  <Truck className="w-4 h-4 text-[var(--color-navy)]" />
                </div>
                <h2 className="text-lg font-semibold text-[var(--color-navy)]">Shipping Information</h2>
              </div>

              {/* Personal Information */}
              <div className="mb-6">
                <div className="flex items-center mb-4">
                  <User className="w-4 h-4 text-[var(--color-text-secondary)] mr-2" />
                  <h3 className="text-sm font-medium text-[var(--color-navy)]">Personal Information</h3>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div className="relative">
                    <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-[var(--color-text-secondary)]" />
                    <input
                      type="text"
                      value={shippingAddress.fullName}
                      onChange={(e) => handleInputChange('fullName', e.target.value)}
                      className="w-full pl-10 pr-4 py-3 bg-[var(--color-bg-alt)] border border-[var(--color-sky-blue)]/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-teal)] focus:bg-[var(--color-bg-card)] transition-colors text-[var(--color-navy)]"
                      placeholder="Full Name"
                    />
                  </div>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-[var(--color-text-secondary)]" />
                    <input
                      type="email"
                      value={shippingAddress.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      className="w-full pl-10 pr-4 py-3 bg-[var(--color-bg-alt)] border border-[var(--color-sky-blue)]/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-teal)] focus:bg-[var(--color-bg-card)] transition-colors text-[var(--color-navy)]"
                      placeholder="Email Address"
                    />
                  </div>
                </div>

                <div className="relative">
                  <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-[var(--color-text-secondary)]" />
                  <input
                    type="tel"
                    value={shippingAddress.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    className="w-full pl-10 pr-4 py-3 bg-[var(--color-bg-alt)] border border-[var(--color-sky-blue)]/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-teal)] focus:bg-[var(--color-bg-card)] transition-colors text-[var(--color-navy)]"
                    placeholder="Phone Number"
                  />
                </div>
              </div>

              {/* Delivery Address */}
              <div>
                <div className="flex items-center mb-4">
                  <MapPin className="w-4 h-4 text-[var(--color-text-secondary)] mr-2" />
                  <h3 className="text-sm font-medium text-[var(--color-navy)]">Delivery Address</h3>
                </div>

                <div className="space-y-4">
                  <div className="relative">
                    <MapPin className="absolute left-3 top-4 w-4 h-4 text-[var(--color-text-secondary)]" />
                    <textarea
                      value={shippingAddress.addressLine1}
                      onChange={(e) => handleInputChange('addressLine1', e.target.value)}
                      className="w-full pl-10 pr-4 py-3 bg-[var(--color-bg-alt)] border border-[var(--color-sky-blue)]/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-teal)] focus:bg-[var(--color-bg-card)] transition-colors resize-none text-[var(--color-navy)]"
                      placeholder="Street Address"
                      rows={3}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <input
                      type="text"
                      value={shippingAddress.city}
                      onChange={(e) => handleInputChange('city', e.target.value)}
                      className="w-full px-4 py-3 bg-[var(--color-bg-alt)] border border-[var(--color-sky-blue)]/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-teal)] focus:bg-[var(--color-bg-card)] transition-colors text-[var(--color-navy)]"
                      placeholder="City"
                    />
                    <input
                      type="text"
                      value={shippingAddress.state}
                      onChange={(e) => handleInputChange('state', e.target.value)}
                      className="w-full px-4 py-3 bg-[var(--color-bg-alt)] border border-[var(--color-sky-blue)]/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-teal)] focus:bg-[var(--color-bg-card)] transition-colors text-[var(--color-navy)]"
                      placeholder="State"
                    />
                  </div>

                  <input
                    type="text"
                    value={shippingAddress.pincode}
                    onChange={(e) => handleInputChange('pincode', e.target.value)}
                    className="w-full px-4 py-3 bg-[var(--color-bg-alt)] border border-[var(--color-sky-blue)]/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-teal)] focus:bg-[var(--color-bg-card)] transition-colors text-[var(--color-navy)]"
                    placeholder="PIN Code"
                  />
                </div>
              </div>
            </div>

            {/* Payment Method Card */}
            <div className="bg-[var(--color-bg-card)] rounded-xl shadow-sm border border-[var(--color-sky-blue)]/20 p-6">
              <div className="flex items-center mb-6">
                <div className="w-8 h-8 bg-[var(--color-sky-blue)]/30 rounded-full flex items-center justify-center mr-3">
                  <CreditCard className="w-4 h-4 text-[var(--color-navy)]" />
                </div>
                <h2 className="text-lg font-semibold text-[var(--color-navy)]">Payment Method</h2>
              </div>

              <div className="space-y-4">
                {/* Pay Online Option */}
                <div className={`border-2 rounded-lg p-4 cursor-not-allowed transition-all border-[var(--color-sky-blue)]/30 bg-[var(--color-bg-alt)]`}>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <input
                        type="radio"
                        id="pay-online"
                        name="payment-method"
                        value="online"
                        checked={false}
                        disabled
                        className="w-4 h-4 text-[var(--color-text-secondary)] border-[var(--color-sky-blue)]/30"
                      />
                      <div className="ml-3">
                        <div className="flex items-center">
                          <CreditCard className="w-4 h-4 text-[var(--color-text-secondary)] mr-2" />
                          <label htmlFor="pay-online" className="text-sm font-medium text-[var(--color-text-secondary)]">
                            Pay Online
                          </label>
                        </div>
                        <p className="text-xs text-[var(--color-text-secondary)] mt-1">Payment gateway not configured. Please use Cash on Delivery.</p>
                      </div>
                    </div>
                    <span className="text-xs text-red-500 font-medium">Not Available</span>
                  </div>
                  <div className="mt-2 text-xs text-[var(--color-text-secondary)]">
                    <div className="flex items-center">
                      <Shield className="w-3 h-3 mr-1" />
                      Secured by Razorpay
                    </div>
                  </div>
                </div>

                {/* Cash on Delivery Option */}
                <div className={`border-2 rounded-lg p-4 cursor-pointer transition-all border-[var(--color-teal)] bg-[var(--color-sky-blue)]/20`}>
                  <div className="flex items-center">
                    <input
                      type="radio"
                      id="cash-on-delivery"
                      name="payment-method"
                      value="cod"
                      checked={true}
                      onChange={(e) => setPaymentMethod(e.target.value as 'online' | 'cod')}
                      className="w-4 h-4 text-[var(--color-teal)] border-[var(--color-sky-blue)]/30 focus:ring-[var(--color-teal)]"
                    />
                    <div className="ml-3">
                      <div className="flex items-center">
                        <Truck className="w-4 h-4 text-[var(--color-navy)] mr-2" />
                        <label htmlFor="cash-on-delivery" className="text-sm font-medium text-[var(--color-navy)] cursor-pointer">
                          Cash on Delivery
                        </label>
                      </div>
                      <p className="text-xs text-[var(--color-text-secondary)] mt-1">Pay when your order is delivered</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Place Order Button */}
              <button
                onClick={handlePlaceOrder}
                disabled={paymentState.loading || !validateForm()}
                className="w-full mt-6 px-6 py-4 bg-[var(--color-navy)] text-white rounded-xl font-bold hover:bg-[var(--color-teal)] transition-all duration-300 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
              >
                {paymentState.loading ? (
                  <>
                    <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                    Processing Order...
                  </>
                ) : (
                  <>
                    <Shield className="w-4 h-4 mr-2" />
                    Place Order (COD) • ₹{total.toLocaleString()}
                  </>
                )}
              </button>
            </div>
          </div>

          {/* Right Column - Order Summary */}
          <div className="lg:col-span-1">
            <div className="bg-[var(--color-bg-card)] rounded-xl shadow-sm border border-[var(--color-sky-blue)]/20 p-6 sticky top-8">
              <div className="flex items-center mb-6">
                <div className="w-8 h-8 bg-[var(--color-sky-blue)]/30 rounded-full flex items-center justify-center mr-3">
                  <CreditCard className="w-4 h-4 text-[var(--color-navy)]" />
                </div>
                <h2 className="text-lg font-semibold text-[var(--color-navy)]">Order Summary</h2>
              </div>

              {/* Order Items */}
              <div className="space-y-4 mb-6">
                {items.map((item) => (
                  <div key={item.id} className="flex items-center space-x-4 p-3 bg-[var(--color-bg-alt)] rounded-lg border border-[var(--color-sky-blue)]/20">
                    <div className="w-12 h-12 bg-[var(--color-sky-blue)]/30 rounded-lg flex items-center justify-center">
                      <span className="text-[var(--color-navy)] font-semibold">
                        {item.quantity}x
                      </span>
                    </div>
                    <div className="flex-1">
                      <h3 className="text-sm font-medium text-[var(--color-navy)]">{item.name}</h3>
                      <p className="text-xs text-[var(--color-text-secondary)]">Luxury Gift</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-semibold text-[var(--color-navy)]">₹{(item.price * item.quantity).toLocaleString()}</p>
                    </div>
                  </div>
                ))}
              </div>

              {/* Pricing Summary */}
              <div className="space-y-3 py-4 border-t border-[var(--color-sky-blue)]/30">
                <div className="flex justify-between text-sm">
                  <span className="text-[var(--color-text-secondary)]">Subtotal ({items.length} items)</span>
                  <span className="font-medium text-[var(--color-navy)]">₹{subtotal.toLocaleString()}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-[var(--color-text-secondary)]">Shipping</span>
                  <span className="font-medium text-green-600">Free</span>
                </div>
                <div className="flex justify-between text-lg font-semibold pt-3 border-t border-[var(--color-sky-blue)]/30">
                  <span className="text-[var(--color-navy)]">Total</span>
                  <span className="text-[var(--color-navy)]">₹{total.toLocaleString()}</span>
                </div>
              </div>

              {/* Security Features */}
              <div className="space-y-2 pt-4 border-t border-[var(--color-sky-blue)]/30">
                <div className="flex items-center text-xs text-[var(--color-text-secondary)]">
                  <Lock className="w-3 h-3 mr-2 text-green-500" />
                  Secure SSL encryption
                </div>
                <div className="flex items-center text-xs text-[var(--color-text-secondary)]">
                  <RotateCcw className="w-3 h-3 mr-2 text-green-500" />
                  Free returns within 7 days
                </div>
                <div className="flex items-center text-xs text-[var(--color-text-secondary)]">
                  <CheckCircle className="w-3 h-3 mr-2 text-green-500" />
                  100% satisfaction guarantee
                </div>
              </div>

              {/* Terms and Conditions */}
              <div className="pt-4 border-t border-[var(--color-sky-blue)]/30">
                <label className="flex items-start space-x-2">
                  <input
                    type="checkbox"
                    checked={agreeToTerms}
                    onChange={(e) => setAgreeToTerms(e.target.checked)}
                    className="mt-1 rounded border-[var(--color-sky-blue)]/30 text-[var(--color-teal)] focus:ring-[var(--color-teal)]"
                  />
                  <span className="text-xs text-[var(--color-text-secondary)]">
                    I agree to the <a href="/terms" className="text-[var(--color-teal)] hover:underline">Terms & Conditions</a> and <a href="/privacy" className="text-[var(--color-teal)] hover:underline">Privacy Policy</a>
                  </span>
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
