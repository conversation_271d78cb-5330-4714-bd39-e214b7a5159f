"use client";

import React, { useState } from 'react';
import { X, Star } from 'lucide-react';
import { submitReview, type ReviewSubmission } from '@/utils/reviewAPI';

interface WriteReviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  productId: string;
  productName: string;
  onReviewSubmitted?: () => void;
}

const WriteReviewModal: React.FC<WriteReviewModalProps> = ({
  isOpen,
  onClose,
  productId,
  productName,
  onReviewSubmitted,
}) => {
  const [formData, setFormData] = useState({
    rating: 0,
    title: '',
    content: '',
    reviewerName: '',
    reviewerEmail: '',
  });
  const [hoveredRating, setHoveredRating] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (formData.rating === 0) {
      setError('Please select a rating');
      return;
    }

    if (!formData.title.trim() || !formData.content.trim()) {
      setError('Please fill in all required fields');
      return;
    }

    if (!formData.reviewerName.trim() || !formData.reviewerEmail.trim()) {
      setError('Please provide your name and email');
      return;
    }

    try {
      setIsSubmitting(true);
      setError(null);

      const reviewData: ReviewSubmission = {
        productId,
        rating: formData.rating,
        title: formData.title.trim(),
        content: formData.content.trim(),
        reviewerName: formData.reviewerName.trim(),
        reviewerEmail: formData.reviewerEmail.trim(),
      };

      await submitReview(reviewData);

      setSubmitSuccess(true);

      // Reset form
      setFormData({
        rating: 0,
        title: '',
        content: '',
        reviewerName: '',
        reviewerEmail: '',
      });

      // Show success message briefly, then close modal
      setTimeout(() => {
        setSubmitSuccess(false);
        onClose();
      }, 1500);

      // Call callback after a delay to ensure modal closes properly
      if (onReviewSubmitted) {
        setTimeout(() => {
          onReviewSubmitted();
        }, 1600);
      }

    } catch (err) {
      console.error('Error submitting review:', err);
      setError('Failed to submit review. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRatingClick = (rating: number) => {
    setFormData(prev => ({ ...prev, rating }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4 animate-fade-in">
      <div className="bg-white rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto shadow-2xl animate-slide-up border border-[var(--color-sky-blue)]">
        {/* Header */}
        <div className="flex items-center justify-between p-8 border-b border-[var(--color-sky-blue)] bg-gradient-to-r from-[var(--color-bg-main)] to-[var(--color-bg-alt)]">
          <div>
            <h2 className="text-3xl font-bold text-[var(--color-text-primary)] mb-2">Write a Review</h2>
            <p className="text-[var(--color-text-secondary)] font-medium text-lg">{productName}</p>
          </div>
          <button
            onClick={onClose}
            className="p-3 hover:bg-white/60 rounded-full transition-all duration-200 group"
          >
            <X size={24} className="text-[var(--color-text-secondary)] group-hover:text-[var(--color-text-primary)]" />
          </button>
        </div>

        {/* Success Message */}
        {submitSuccess && (
          <div className="p-8 bg-gradient-to-r from-green-50 to-emerald-50 border-b border-green-200">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-gradient-to-r from-green-100 to-emerald-100 rounded-full flex items-center justify-center mr-4 shadow-lg">
                <span className="text-green-600 text-2xl font-bold">✓</span>
              </div>
              <div>
                <h3 className="text-green-800 font-bold text-xl mb-1">Review Submitted Successfully!</h3>
                <p className="text-green-700 font-medium">Thank you for your feedback. Your review will be published after approval by our team.</p>
              </div>
            </div>
          </div>
        )}

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-8 space-y-8">
          {/* Rating */}
          <div>
            <label className="block text-lg font-bold text-[var(--color-text-primary)] mb-4">
              Rating <span className="text-red-500">*</span>
            </label>
            <div className="flex flex-col sm:flex-row sm:items-center space-y-3 sm:space-y-0 sm:space-x-4">
              <div className="flex items-center space-x-2">
                {[1, 2, 3, 4, 5].map((star) => (
                  <button
                    key={star}
                    type="button"
                    onClick={() => handleRatingClick(star)}
                    onMouseEnter={() => setHoveredRating(star)}
                    onMouseLeave={() => setHoveredRating(0)}
                    className="p-2 transition-all duration-200 hover:scale-110 rounded-lg hover:bg-[var(--color-bg-main)]"
                  >
                    <Star
                      size={36}
                      className={`${
                        star <= (hoveredRating || formData.rating)
                          ? 'text-[var(--color-gold)] fill-current'
                          : 'text-gray-300'
                      } transition-all duration-200`}
                    />
                  </button>
                ))}
              </div>
              {formData.rating > 0 && (
                <div className="flex items-center space-x-2">
                  <span className="text-lg font-semibold text-[var(--color-text-primary)]">
                    {formData.rating} star{formData.rating !== 1 ? 's' : ''}
                  </span>
                  <span className="text-[var(--color-text-secondary)] font-medium">
                    {formData.rating === 5 ? 'Excellent!' :
                     formData.rating === 4 ? 'Very Good!' :
                     formData.rating === 3 ? 'Good!' :
                     formData.rating === 2 ? 'Fair' : 'Poor'}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Review Title */}
          <div>
            <label htmlFor="title" className="block text-lg font-bold text-[var(--color-text-primary)] mb-3">
              Review Title <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="title"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              placeholder="Summarize your experience in a few words"
              className="w-full border-2 border-[var(--color-sky-blue)] rounded-xl px-4 py-3 text-[var(--color-text-primary)] bg-white focus:ring-2 focus:ring-[var(--color-teal)] focus:border-[var(--color-teal)] transition-all duration-200 font-medium"
              maxLength={100}
            />
            <p className="text-sm text-[var(--color-text-secondary)] mt-2 font-medium">{formData.title.length}/100 characters</p>
          </div>

          {/* Review Content */}
          <div>
            <label htmlFor="content" className="block text-lg font-bold text-[var(--color-text-primary)] mb-3">
              Your Review <span className="text-red-500">*</span>
            </label>
            <textarea
              id="content"
              value={formData.content}
              onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
              placeholder="Share your detailed thoughts about this product. What did you like? How was the quality? Would you recommend it to others?"
              rows={6}
              className="w-full border-2 border-[var(--color-sky-blue)] rounded-xl px-4 py-3 text-[var(--color-text-primary)] bg-white focus:ring-2 focus:ring-[var(--color-teal)] focus:border-[var(--color-teal)] transition-all duration-200 resize-none font-medium leading-relaxed"
              maxLength={1000}
            />
            <p className="text-sm text-[var(--color-text-secondary)] mt-2 font-medium">{formData.content.length}/1000 characters</p>
          </div>

          {/* Reviewer Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label htmlFor="reviewerName" className="block text-lg font-bold text-[var(--color-text-primary)] mb-3">
                Your Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="reviewerName"
                value={formData.reviewerName}
                onChange={(e) => setFormData(prev => ({ ...prev, reviewerName: e.target.value }))}
                placeholder="Enter your full name"
                className="w-full border-2 border-[var(--color-sky-blue)] rounded-xl px-4 py-3 text-[var(--color-text-primary)] bg-white focus:ring-2 focus:ring-[var(--color-teal)] focus:border-[var(--color-teal)] transition-all duration-200 font-medium"
              />
            </div>
            <div>
              <label htmlFor="reviewerEmail" className="block text-lg font-bold text-[var(--color-text-primary)] mb-3">
                Your Email <span className="text-red-500">*</span>
              </label>
              <input
                type="email"
                id="reviewerEmail"
                value={formData.reviewerEmail}
                onChange={(e) => setFormData(prev => ({ ...prev, reviewerEmail: e.target.value }))}
                placeholder="Enter your email address"
                className="w-full border-2 border-[var(--color-sky-blue)] rounded-xl px-4 py-3 text-[var(--color-text-primary)] bg-white focus:ring-2 focus:ring-[var(--color-teal)] focus:border-[var(--color-teal)] transition-all duration-200 font-medium"
              />
            </div>
          </div>

          {/* Error Message */}
          {error && (
            <div className="p-4 bg-gradient-to-r from-red-50 to-pink-50 border-2 border-red-200 rounded-xl">
              <p className="text-red-700 font-semibold">{error}</p>
            </div>
          )}

          {/* Submit Button */}
          <div className="flex flex-col sm:flex-row items-center justify-end space-y-3 sm:space-y-0 sm:space-x-4 pt-6 border-t border-[var(--color-sky-blue)]">
            <button
              type="button"
              onClick={onClose}
              className="w-full sm:w-auto px-8 py-3 border-2 border-[var(--color-sky-blue)] text-[var(--color-text-secondary)] rounded-xl hover:bg-[var(--color-bg-main)] transition-all duration-200 font-semibold"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting || submitSuccess}
              className="w-full sm:w-auto px-8 py-3 bg-gradient-to-r from-[var(--color-navy)] to-[var(--color-teal)] text-white rounded-xl hover:from-[var(--color-teal)] hover:to-[var(--color-navy)] disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 font-bold flex items-center justify-center space-x-2"
            >
              {isSubmitting ? (
                <>
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>Submitting...</span>
                </>
              ) : (
                <>
                  <Star className="w-5 h-5" />
                  <span>Submit Review</span>
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default WriteReviewModal;
