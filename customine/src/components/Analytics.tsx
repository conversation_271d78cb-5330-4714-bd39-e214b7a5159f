'use client';

import { useEffect, Suspense } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';

// Google Analytics
declare global {
  interface Window {
    gtag: (...args: unknown[]) => void;
    dataLayer: unknown[];
  }
}

function GoogleAnalyticsTracker({ GA_MEASUREMENT_ID }: { GA_MEASUREMENT_ID: string }) {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  useEffect(() => {
    const url = pathname + searchParams.toString();

    if (typeof window.gtag !== 'undefined') {
      window.gtag('config', GA_MEASUREMENT_ID, {
        page_path: url,
      });
    }
  }, [pathname, searchParams, GA_MEASUREMENT_ID]);

  return null;
}

export function GoogleAnalytics({ GA_MEASUREMENT_ID }: { GA_MEASUREMENT_ID: string }) {

  return (
    <>
      <script
        async
        src={`https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`}
      />
      <script
        dangerouslySetInnerHTML={{
          __html: `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '${GA_MEASUREMENT_ID}', {
              page_path: window.location.pathname,
            });
          `,
        }}
      />
      <Suspense fallback={null}>
        <GoogleAnalyticsTracker GA_MEASUREMENT_ID={GA_MEASUREMENT_ID} />
      </Suspense>
    </>
  );
}

// Facebook Pixel (optional) - Simplified version
export function FacebookPixel({ PIXEL_ID }: { PIXEL_ID: string }) {
  useEffect(() => {
    // Simplified Facebook Pixel implementation
    console.log('Facebook Pixel would be initialized with ID:', PIXEL_ID);
  }, [PIXEL_ID]);

  return null;
}

// Microsoft Clarity (optional) - Simplified
export function MicrosoftClarity({ CLARITY_ID }: { CLARITY_ID: string }) {
  useEffect(() => {
    console.log('Microsoft Clarity would be initialized with ID:', CLARITY_ID);
  }, [CLARITY_ID]);

  return null;
}

// Hotjar (optional) - Simplified
export function Hotjar({ HOTJAR_ID }: { HOTJAR_ID: number }) {
  useEffect(() => {
    console.log('Hotjar would be initialized with ID:', HOTJAR_ID);
  }, [HOTJAR_ID]);

  return null;
}

// Combined Analytics Component
interface AnalyticsProps {
  googleAnalyticsId?: string;
  facebookPixelId?: string;
  clarityId?: string;
  hotjarId?: number;
}

export default function Analytics({
  googleAnalyticsId,
  facebookPixelId,
  clarityId,
  hotjarId,
}: AnalyticsProps) {
  return (
    <>
      {googleAnalyticsId && <GoogleAnalytics GA_MEASUREMENT_ID={googleAnalyticsId} />}
      {facebookPixelId && <FacebookPixel PIXEL_ID={facebookPixelId} />}
      {clarityId && <MicrosoftClarity CLARITY_ID={clarityId} />}
      {hotjarId && <Hotjar HOTJAR_ID={hotjarId} />}
    </>
  );
}

// Event tracking utilities
export const trackEvent = (eventName: string, parameters?: Record<string, unknown>) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', eventName, parameters);
  }
};

export const trackPurchase = (transactionId: string, value: number, currency = 'INR') => {
  trackEvent('purchase', {
    transaction_id: transactionId,
    value: value,
    currency: currency,
  });
};

export const trackAddToCart = (itemId: string, itemName: string, value: number) => {
  trackEvent('add_to_cart', {
    currency: 'INR',
    value: value,
    items: [
      {
        item_id: itemId,
        item_name: itemName,
        currency: 'INR',
        value: value,
      },
    ],
  });
};

export const trackViewItem = (itemId: string, itemName: string, category: string, value: number) => {
  trackEvent('view_item', {
    currency: 'INR',
    value: value,
    items: [
      {
        item_id: itemId,
        item_name: itemName,
        item_category: category,
        currency: 'INR',
        value: value,
      },
    ],
  });
};
