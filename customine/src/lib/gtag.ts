// Google Analytics configuration
export const GA_TRACKING_ID = process.env.NEXT_PUBLIC_GA_ID || 'G-9S261HRYZW';

// https://developers.google.com/analytics/devguides/collection/gtagjs/pages
export const pageview = (url: string) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('config', GA_TRACKING_ID, {
      page_path: url,
    });
  }
};

// https://developers.google.com/analytics/devguides/collection/gtagjs/events
export const event = ({
  action,
  category,
  label,
  value,
}: {
  action: string;
  category: string;
  label?: string;
  value?: number;
}) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', action, {
      event_category: category,
      event_label: label,
      value: value,
    });
  }
};

// E-commerce tracking
export const purchase = ({
  transaction_id,
  value,
  currency = 'INR',
  items,
}: {
  transaction_id: string;
  value: number;
  currency?: string;
  items: Array<{
    item_id: string;
    item_name: string;
    category: string;
    quantity: number;
    price: number;
  }>;
}) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'purchase', {
      transaction_id,
      value,
      currency,
      items,
    });
  }
};

// Add to cart tracking
export const addToCart = ({
  currency = 'INR',
  value,
  items,
}: {
  currency?: string;
  value: number;
  items: Array<{
    item_id: string;
    item_name: string;
    category: string;
    quantity: number;
    price: number;
  }>;
}) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'add_to_cart', {
      currency,
      value,
      items,
    });
  }
};

// Begin checkout tracking
export const beginCheckout = ({
  currency = 'INR',
  value,
  items,
}: {
  currency?: string;
  value: number;
  items: Array<{
    item_id: string;
    item_name: string;
    category: string;
    quantity: number;
    price: number;
  }>;
}) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'begin_checkout', {
      currency,
      value,
      items,
    });
  }
};

// Contact form submission tracking
export const contactFormSubmit = (form_type: string) => {
  event({
    action: 'form_submit',
    category: 'Contact',
    label: form_type,
  });
};

// Product view tracking
export const viewItem = ({
  currency = 'INR',
  value,
  items,
}: {
  currency?: string;
  value: number;
  items: Array<{
    item_id: string;
    item_name: string;
    category: string;
    price: number;
  }>;
}) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'view_item', {
      currency,
      value,
      items,
    });
  }
};

// Search tracking
export const search = (search_term: string) => {
  event({
    action: 'search',
    category: 'Search',
    label: search_term,
  });
};
