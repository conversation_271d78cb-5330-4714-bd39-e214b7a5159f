"use client";
import React, { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react';
import { cartAPI } from '../utils/api';
import { useAuth } from './AuthContext';

export interface CartItem {
  id: string;
  productId: string;
  name: string;
  price: number;
  image: string;
  quantity: number;
  giftType?: string;
  customization?: string;
  product?: {
    id: string;
    name: string;
    images: string[];
    price: number;
    inStock: boolean;
    slug: string;
  };
}

interface CartContextType {
  items: CartItem[];
  loading: boolean;
  initialLoading: boolean;
  hasDataLoaded: boolean;
  addToCart: (item: Omit<CartItem, 'id'>) => Promise<void>;
  removeFromCart: (id: string) => Promise<void>;
  updateQuantity: (id: string, quantity: number) => Promise<void>;
  clearCart: () => Promise<void>;
  refreshCart: () => Promise<void>;
  syncGuestCartWithDatabase: () => Promise<void>;
  getTotalItems: () => number;
  getTotalPrice: () => number;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

export function useCart() {
  const ctx = useContext(CartContext);
  if (!ctx) throw new Error('useCart must be used within a CartProvider');
  return ctx;
}

export function CartProvider({ children }: { children: ReactNode }) {
  const [items, setItems] = useState<CartItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [hasLoadedOnce, setHasLoadedOnce] = useState(false);
  const [hasDataLoaded, setHasDataLoaded] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const [lastAuthState, setLastAuthState] = useState<boolean | null>(null);
  const { user, isLoggedIn } = useAuth();

  // Ensure we're on the client side
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Save to localStorage for guest users only when items change and user is guest
  useEffect(() => {
    if (!isClient || isLoggedIn) return; // Only save for guest users

    // Only save if we have items or need to clear localStorage
    if (items.length > 0) {
      console.log('Saving guest cart to localStorage:', items.length, 'items');
      localStorage.setItem('cart', JSON.stringify(items));
    } else {
      // Clear localStorage if cart is empty
      localStorage.removeItem('cart');
    }
  }, [items, isLoggedIn, isClient]);

  const refreshCart = useCallback(async () => {
    if (!user?.id) return;

    setLoading(true);
    try {
      const result = await cartAPI.getItems(user.id);
      const cartItems = result.data?.cartItemsByUserId?.items || [];

      // Transform the data to match our CartItem interface
      const transformedItems: CartItem[] = cartItems.map((item: any) => ({
        id: item.id,
        productId: item.productId,
        name: item.product?.name || '',
        price: item.price,
        image: item.product?.images?.[0] || '',
        quantity: item.quantity,
        giftType: item.giftType,
        customization: item.customization,
        product: item.product
      }));

      setItems(transformedItems);
      setHasDataLoaded(true);
    } catch (error) {
      console.error('Error refreshing cart:', error);
      console.error('Error details:', JSON.stringify(error, null, 2));
      // Set empty cart on error to prevent infinite loading
      setItems([]);
      setHasDataLoaded(true); // Even on error, we've attempted to load data
    } finally {
      setLoading(false);
    }
  }, [user?.id]);

  const syncGuestCartWithDatabase = useCallback(async () => {
    if (!user?.id) return;

    setLoading(true);
    try {
      // Get guest cart from localStorage
      const guestCartData = localStorage.getItem('cart');
      const guestItems: CartItem[] = guestCartData ? JSON.parse(guestCartData) : [];

      console.log('Syncing guest cart with database:', guestItems.length, 'items');

      // Get existing cart from database
      const result = await cartAPI.getItems(user.id);
      const existingCartItems = result.data?.cartItemsByUserId?.items || [];

      // Transform existing cart items
      const existingItems: CartItem[] = existingCartItems.map((item: any) => ({
        id: item.id,
        productId: item.productId,
        name: item.product?.name || '',
        price: item.price,
        image: item.product?.images?.[0] || '',
        quantity: item.quantity,
        giftType: item.giftType,
        customization: item.customization,
        product: item.product
      }));

      // Merge guest cart with existing cart
      const mergedItems = [...existingItems];

      for (const guestItem of guestItems) {
        // Check if item already exists in database cart
        const existingItem = existingItems.find(
          item => item.productId === guestItem.productId && item.giftType === guestItem.giftType
        );

        if (existingItem) {
          // Update quantity of existing item
          const newQuantity = existingItem.quantity + guestItem.quantity;
          await cartAPI.updateItem(existingItem.id, { quantity: newQuantity });

          // Update in merged items
          const mergedIndex = mergedItems.findIndex(item => item.id === existingItem.id);
          if (mergedIndex !== -1) {
            mergedItems[mergedIndex].quantity = newQuantity;
          }
        } else {
          // Add new item to database
          const newCartItem = await cartAPI.addItem({
            userId: user.id,
            productId: guestItem.productId,
            quantity: guestItem.quantity,
            price: guestItem.price,
            giftType: guestItem.giftType,
            customization: guestItem.customization
          });

          // Add to merged items (with temporary data until refresh)
          mergedItems.push({
            ...guestItem,
            id: newCartItem.data?.createCartItem?.id || `temp-${Date.now()}`
          });
        }
      }

      // Clear guest cart from localStorage
      localStorage.removeItem('cart');

      // Refresh cart to get latest data from database
      await refreshCart();

      console.log('Guest cart sync completed successfully');
    } catch (error) {
      console.error('Error syncing guest cart with database:', error);
      // If sync fails, still try to load existing cart
      await refreshCart();
    } finally {
      setLoading(false);
    }
  }, [user?.id, refreshCart]);

  // Handle authentication state changes and cart synchronization
  useEffect(() => {
    if (!isClient) return; // Only run on client side

    const handleAuthStateChange = async () => {
      // Don't set initialLoading here as it should already be true

      try {
        if (isLoggedIn && user?.id) {
          // User just logged in
          if (lastAuthState === false || lastAuthState === null) {
            console.log('User logged in, syncing guest cart with database');
            await syncGuestCartWithDatabase();
          } else {
            // User was already logged in, just refresh cart
            await refreshCart();
          }
        } else if (!isLoggedIn) {
          // User is guest or just logged out
          if (lastAuthState === true) {
            // User just logged out, clear cart and start fresh
            console.log('User logged out, clearing cart');
            setItems([]);
            localStorage.removeItem('cart');
          } else {
            // Load from localStorage for guest users
            try {
              const stored = localStorage.getItem('cart');
              if (stored) {
                const guestItems = JSON.parse(stored);
                console.log('Loading guest cart from localStorage:', guestItems.length, 'items');
                setItems(guestItems);
              } else {
                setItems([]);
              }
              setHasDataLoaded(true);
            } catch (error) {
              console.error('Error parsing cart from localStorage:', error);
              localStorage.removeItem('cart');
              setItems([]);
              setHasDataLoaded(true);
            }
          }
        }

        // Update last auth state
        setLastAuthState(isLoggedIn);
      } finally {
        setInitialLoading(false);
        setHasLoadedOnce(true);
      }
    };

    handleAuthStateChange();
  }, [isLoggedIn, user?.id, isClient, lastAuthState, syncGuestCartWithDatabase, refreshCart]);

  const addToCart = async (item: Omit<CartItem, 'id'>) => {
    if (!user?.id) {
      // For guest users, use localStorage
      setItems(prev => {
        const existing = prev.find(i => i.productId === item.productId && i.giftType === item.giftType);
        if (existing) {
          return prev.map(i =>
            i.productId === item.productId && i.giftType === item.giftType
              ? { ...i, quantity: i.quantity + item.quantity }
              : i
          );
        }
        return [...prev, { ...item, id: `temp-${Date.now()}` }];
      });
      return;
    }

    setLoading(true);
    try {
      // Check if item already exists in cart
      const existing = items.find(i => i.productId === item.productId && i.giftType === item.giftType);

      if (existing) {
        // Update existing item quantity
        await cartAPI.updateItem(existing.id, {
          quantity: existing.quantity + item.quantity
        });
      } else {
        // Add new item
        await cartAPI.addItem({
          userId: user.id,
          productId: item.productId,
          quantity: item.quantity,
          price: item.price,
          giftType: item.giftType,
          customization: item.customization
        });
      }

      await refreshCart();
    } catch (error) {
      console.error('Error adding to cart:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const removeFromCart = async (id: string) => {
    if (!user?.id) {
      // For guest users, use localStorage
      setItems(prev => prev.filter(i => i.id !== id));
      return;
    }

    setLoading(true);
    try {
      await cartAPI.removeItem(id);
      await refreshCart();
    } catch (error) {
      console.error('Error removing from cart:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const updateQuantity = async (id: string, quantity: number) => {
    if (quantity <= 0) {
      await removeFromCart(id);
      return;
    }

    if (!user?.id) {
      // For guest users, use localStorage
      setItems(prev => prev.map(i => i.id === id ? { ...i, quantity } : i));
      return;
    }

    setLoading(true);
    try {
      await cartAPI.updateItem(id, { quantity });
      await refreshCart();
    } catch (error) {
      console.error('Error updating cart quantity:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const clearCart = async () => {
    if (!user?.id) {
      // For guest users, clear localStorage
      setItems([]);
      localStorage.removeItem('cart');
      return;
    }

    setLoading(true);
    try {
      // Remove all items from cart
      await Promise.all(items.map(item => cartAPI.removeItem(item.id)));
      setItems([]);
    } catch (error) {
      console.error('Error clearing cart:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const getTotalItems = () => {
    return items.reduce((total, item) => total + item.quantity, 0);
  };

  const getTotalPrice = () => {
    return items.reduce((total, item) => total + (item.price * item.quantity), 0);
  };

  return (
    <CartContext.Provider value={{
      items,
      loading,
      initialLoading: initialLoading || !isClient || !hasLoadedOnce,
      hasDataLoaded,
      addToCart,
      removeFromCart,
      updateQuantity,
      clearCart,
      refreshCart,
      syncGuestCartWithDatabase,
      getTotalItems,
      getTotalPrice
    }}>
      {children}
    </CartContext.Provider>
  );
}