'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { authAPI, User } from '../utils/api';
// Hub will be imported dynamically when needed

interface AuthContextType {
  user: User | null;
  isLoggedIn: boolean;
  login: (userData: User) => void;
  logout: () => void;
  loading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  const isLoggedIn = !!user;

  // Check for existing login on mount
  useEffect(() => {
    checkAuthStatus();

    // Set up auth event listener
    const setupAuthListener = async () => {
      try {
        const { Hub } = await import('aws-amplify/utils');

        const hubListener = (data: { payload: { event: string } }) => {
          const { payload } = data;

          switch (payload.event) {
            case 'signedIn':
              console.log('User signed in');
              checkAuthStatus();
              break;
            case 'signedOut':
              console.log('User signed out');
              setUser(null);
              // Redirect to home page when user is signed out
              if (typeof window !== 'undefined') {
                window.location.href = '/';
              }
              break;
            case 'signUp':
              console.log('User signed up');
              break;
            case 'signIn_failure':
              console.log('User sign in failed');
              break;
            default:
              break;
          }
        };

        const unsubscribe = Hub.listen('auth', hubListener);

        return () => {
          unsubscribe();
        };
      } catch (error) {
        console.error('Error setting up auth listener:', error);
      }
    };

    setupAuthListener();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const result = await authAPI.getCurrentUser();

      if (result.success && result.data?.user) {
        setUser(result.data.user);
      } else {
        setUser(null);
      }
    } catch (error) {
      console.error('Auth check failed:', error);
      setUser(null);
    } finally {
      setLoading(false);
    }
  };

  const login = (userData: User) => {
    setUser(userData);
  };

  const logout = async () => {
    try {
      await authAPI.signOut();
      setUser(null);

      // Redirect to home page after logout
      if (typeof window !== 'undefined') {
        window.location.href = '/';
      }
    } catch (error) {
      console.error('Logout error:', error);
      // Still clear user state even if logout API fails
      setUser(null);

      // Redirect to home page even if logout API fails
      if (typeof window !== 'undefined') {
        window.location.href = '/';
      }
    }
  };

  const value = {
    user,
    isLoggedIn,
    login,
    logout,
    loading
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Higher-order component for protected routes
export function withAuth<P extends object>(Component: React.ComponentType<P>) {
  return function AuthenticatedComponent(props: P) {
    const { isLoggedIn, loading } = useAuth();

    if (loading) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
        </div>
      );
    }

    if (!isLoggedIn) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h2>
            <p className="text-gray-600">Please log in to access this page.</p>
          </div>
        </div>
      );
    }

    return <Component {...props} />;
  };
}

// Hook for GraphQL API calls with authentication
export function useAuthenticatedAPI() {
  const { logout } = useAuth();

  const authenticatedGraphQL = async (query: string, variables?: Record<string, unknown>) => {
    try {
      // Amplify automatically handles authentication for GraphQL calls
      const { API, graphqlOperation } = await import('aws-amplify');
      const result = await API.graphql(graphqlOperation(query, variables));
      return result;
    } catch (error: unknown) {
      console.error('GraphQL API error:', error);

      // Handle authentication errors
      if (error && typeof error === 'object' && 'errors' in error) {
        const errors = (error as { errors: Array<{ errorType: string }> }).errors;
        if (errors.some(err => err.errorType === 'Unauthorized')) {
          await logout();
        }
      }

      throw error;
    }
  };

  return authenticatedGraphQL;
}
