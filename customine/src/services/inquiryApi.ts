import { Amplify } from 'aws-amplify';
import { generateClient } from 'aws-amplify/api';
import amplifyconfig from '../amplifyconfiguration.json';
import { 
  createInquiry, 
  updateInquiry, 
  deleteInquiry,
  createInquiryResponse 
} from '../graphql/mutations';
import {
  listInquiries,
  getInquiry,
  inquiriesByType,
  inquiriesByEmail,
  inquiryResponsesByInquiryId
} from '../graphql/queries';

// Query that includes all fields including status
const safeListInquiries = `
  query ListInquiries($limit: Int) {
    listInquiries(limit: $limit) {
      items {
        id
        userId
        type
        name
        firstName
        lastName
        email
        phone
        company
        subject
        message
        category
        priority
        eventType
        giftingType
        eventDate
        eventLocation
        guestCount
        totalGifts
        totalBudget
        budget
        location
        eventDetails
        source
        referral
        status
        assignedTo
        isRead
        isReplied
        adminNotes
        internalNotes
        attachments
        relatedOrderId
        relatedProductId
        createdAt
        updatedAt
      }
      nextToken
    }
  }
`;

// Configure Amplify
Amplify.configure(amplifyconfig);
const client = generateClient();

export interface SpecialOccasionInquiry {
  id?: string;
  userId?: string;
  type: 'SPECIAL_OCCASION';
  name: string;
  firstName?: string;
  lastName?: string;
  email: string;
  phone?: string;
  company?: string;
  subject: string;
  message: string;
  category?: string;
  priority?: string;
  eventType?: string;
  giftingType?: string;
  eventDate?: string;
  eventLocation?: string;
  guestCount?: number;
  totalGifts?: string;
  totalBudget?: string;
  budget?: string;
  location?: string;
  eventDetails?: string;
  source?: string;
  referral?: string;
  status?: string;
  assignedTo?: string;
  isRead?: boolean;
  isReplied?: boolean;
  adminNotes?: string;
  internalNotes?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface InquiryResponse {
  id?: string;
  inquiryId: string;
  responderId: string;
  responderName: string;
  responderEmail: string;
  message: string;
  isInternal?: boolean;
  attachments?: string[];
  createdAt?: string;
  updatedAt?: string;
}

export interface ApiResponse {
  success: boolean;
  data?: any;
  error?: string;
  message?: string;
}

export const inquiryAPI = {
  /**
   * Create a new special occasion inquiry
   */
  createSpecialOccasionInquiry: async (inquiryData: SpecialOccasionInquiry): Promise<ApiResponse> => {
    try {
      // Get current user ID if user is authenticated
      let userId: string | undefined;
      try {
        const { getCurrentUser } = await import('aws-amplify/auth');
        const currentUser = await getCurrentUser();
        userId = currentUser.userId;
      } catch (authError) {
        // User not authenticated - inquiry will be anonymous
        console.log('User not authenticated, creating anonymous inquiry');
      }

      // Create input object with all required fields
      const input: any = {
        type: 'SPECIAL_OCCASION',
        name: inquiryData.name,
        email: inquiryData.email,
        subject: inquiryData.subject,
        message: inquiryData.message,
        userId, // Include userId if available
        // Required non-nullable fields with explicit enum values
        status: 'NEW',
        isRead: false,
        isReplied: false
      };

      // Add optional fields only if they have values (avoid sending empty strings or null)
      if (inquiryData.firstName?.trim()) input.firstName = inquiryData.firstName.trim();
      if (inquiryData.lastName?.trim()) input.lastName = inquiryData.lastName.trim();
      if (inquiryData.phone?.trim()) input.phone = inquiryData.phone.trim();
      if (inquiryData.company?.trim()) input.company = inquiryData.company.trim();
      if (inquiryData.eventType?.trim()) input.eventType = inquiryData.eventType.trim();
      if (inquiryData.giftingType?.trim()) input.giftingType = inquiryData.giftingType.trim();
      if (inquiryData.eventDate?.trim()) input.eventDate = inquiryData.eventDate.trim();
      if (inquiryData.eventLocation?.trim()) input.eventLocation = inquiryData.eventLocation.trim();
      if (inquiryData.totalGifts?.trim()) input.totalGifts = inquiryData.totalGifts.trim();
      if (inquiryData.totalBudget?.trim()) input.totalBudget = inquiryData.totalBudget.trim();
      if (inquiryData.eventDetails?.trim()) input.eventDetails = inquiryData.eventDetails.trim();
      if (inquiryData.source?.trim()) input.source = inquiryData.source.trim();
      if (inquiryData.referral?.trim()) input.referral = inquiryData.referral.trim();

      console.log('Submitting inquiry with input:', JSON.stringify(input, null, 2));
      console.log('Required fields check:');
      console.log('- status:', input.status, typeof input.status);
      console.log('- isRead:', input.isRead, typeof input.isRead);
      console.log('- isReplied:', input.isReplied, typeof input.isReplied);

      const result = await client.graphql({
        query: createInquiry,
        variables: { input }
      });

      console.log('Inquiry submission result:', JSON.stringify(result, null, 2));

      return {
        success: true,
        data: (result.data as any)?.createInquiry,
        message: 'Inquiry submitted successfully'
      };
    } catch (error) {
      console.error('Error creating inquiry:', error);
      return {
        success: false,
        error: 'Failed to submit inquiry'
      };
    }
  },

  /**
   * Get all inquiries (admin use)
   */
  getAllInquiries: async (limit: number = 50): Promise<ApiResponse> => {
    try {
      const result = await client.graphql({
        query: listInquiries,
        variables: { limit }
      });

      // Get inquiries - they should now have proper values
      const inquiries = (result.data as any)?.listInquiries?.items || [];

      return {
        success: true,
        data: inquiries
      };
    } catch (error) {
      console.error('Error fetching inquiries:', error);
      return {
        success: false,
        error: 'Failed to fetch inquiries'
      };
    }
  },

  /**
   * Fix existing inquiries with null values for required fields
   */
  fixExistingInquiries: async (): Promise<ApiResponse> => {
    try {
      console.log('Fetching inquiries to fix...');

      // Get all inquiries using safe query
      const result = await client.graphql({
        query: safeListInquiries,
        variables: { limit: 1000 }
      });

      const inquiries = (result.data as any)?.listInquiries?.items || [];
      console.log(`Found ${inquiries.length} inquiries`);

      let fixedCount = 0;
      const errors = [];

      // Update each inquiry to ensure required fields have values
      for (const inquiry of inquiries) {
        try {
          const updateInput = {
            id: inquiry.id,
            status: 'NEW', // Set default status
            isRead: false, // Set default isRead
            isReplied: false // Set default isReplied
          };

          await client.graphql({
            query: updateInquiry,
            variables: { input: updateInput }
          });

          fixedCount++;
          console.log(`✅ Fixed inquiry ${inquiry.id}`);
        } catch (error) {
          console.error(`❌ Failed to fix inquiry ${inquiry.id}:`, error);
          errors.push({ id: inquiry.id, error: error.message });
        }
      }

      return {
        success: true,
        data: {
          totalInquiries: inquiries.length,
          fixedCount,
          errors
        }
      };
    } catch (error) {
      console.error('Error fixing inquiries:', error);
      return {
        success: false,
        error: 'Failed to fix inquiries'
      };
    }
  },

  /**
   * Get inquiries by type
   */
  getInquiriesByType: async (type: string, limit: number = 50): Promise<ApiResponse> => {
    try {
      const result = await client.graphql({
        query: inquiriesByType,
        variables: { type, limit }
      });

      return {
        success: true,
        data: (result.data as any)?.inquiriesByType?.items || []
      };
    } catch (error) {
      console.error('Error fetching inquiries by type:', error);
      return {
        success: false,
        error: 'Failed to fetch inquiries'
      };
    }
  },

  /**
   * Get single inquiry by ID
   */
  getInquiry: async (id: string): Promise<ApiResponse> => {
    try {
      const result = await client.graphql({
        query: getInquiry,
        variables: { id }
      });

      return {
        success: true,
        data: (result.data as any)?.getInquiry
      };
    } catch (error) {
      console.error('Error fetching inquiry:', error);
      return {
        success: false,
        error: 'Failed to fetch inquiry'
      };
    }
  },

  /**
   * Update inquiry status and admin fields
   */
  updateInquiryStatus: async (id: string, updates: Partial<SpecialOccasionInquiry>): Promise<ApiResponse> => {
    try {
      const input = {
        id,
        ...updates
      };

      const result = await client.graphql({
        query: updateInquiry,
        variables: { input }
      });

      return {
        success: true,
        data: (result.data as any)?.updateInquiry,
        message: 'Inquiry updated successfully'
      };
    } catch (error) {
      console.error('Error updating inquiry:', error);
      return {
        success: false,
        error: 'Failed to update inquiry'
      };
    }
  },

  /**
   * Add response to inquiry
   */
  addInquiryResponse: async (responseData: InquiryResponse): Promise<ApiResponse> => {
    try {
      const input = {
        inquiryId: responseData.inquiryId,
        responderId: responseData.responderId,
        responderName: responseData.responderName,
        responderEmail: responseData.responderEmail,
        message: responseData.message,
        isInternal: responseData.isInternal || false,
        attachments: responseData.attachments || []
      };

      const result = await client.graphql({
        query: createInquiryResponse,
        variables: { input }
      });

      // Also update the inquiry to mark as replied
      await client.graphql({
        query: updateInquiry,
        variables: {
          input: {
            id: responseData.inquiryId,
            isReplied: true,
            status: 'IN_PROGRESS'
          }
        }
      });

      return {
        success: true,
        data: (result.data as any)?.createInquiryResponse,
        message: 'Response added successfully'
      };
    } catch (error) {
      console.error('Error adding response:', error);
      return {
        success: false,
        error: 'Failed to add response'
      };
    }
  },

  /**
   * Get responses for an inquiry
   */
  getInquiryResponses: async (inquiryId: string): Promise<ApiResponse> => {
    try {
      const result = await client.graphql({
        query: inquiryResponsesByInquiryId,
        variables: { inquiryId }
      });

      return {
        success: true,
        data: (result.data as any)?.inquiryResponsesByInquiryId?.items || []
      };
    } catch (error) {
      console.error('Error fetching responses:', error);
      return {
        success: false,
        error: 'Failed to fetch responses'
      };
    }
  },

  /**
   * Delete inquiry (admin only)
   */
  deleteInquiry: async (id: string): Promise<ApiResponse> => {
    try {
      const result = await client.graphql({
        query: deleteInquiry,
        variables: { input: { id } }
      });

      return {
        success: true,
        data: (result.data as any)?.deleteInquiry,
        message: 'Inquiry deleted successfully'
      };
    } catch (error) {
      console.error('Error deleting inquiry:', error);
      return {
        success: false,
        error: 'Failed to delete inquiry'
      };
    }
  },

  /**
   * Mark inquiry as read
   */
  markAsRead: async (id: string): Promise<ApiResponse> => {
    try {
      const result = await client.graphql({
        query: updateInquiry,
        variables: {
          input: {
            id,
            isRead: true
          }
        }
      });

      return {
        success: true,
        data: (result.data as any)?.updateInquiry
      };
    } catch (error) {
      console.error('Error marking as read:', error);
      return {
        success: false,
        error: 'Failed to mark as read'
      };
    }
  }
};

export default inquiryAPI;
