/**
 * Admin API Service for Customine Dashboard
 * Using AWS Amplify GraphQL instead of PHP
 *
 * Note: If using in Node.js, FormData and File types are not available by default. You may need to polyfill or adjust for server-side usage.
 */

import { generateClient } from 'aws-amplify/api';
import type { GraphQLResult } from '@aws-amplify/api';
import {
  getProduct,
  listProducts
} from '../graphql/queries';
import {
  createProduct,
  updateProduct,
  deleteProduct
} from '../graphql/mutations';

const client = generateClient();

// Product interface matching GraphQL schema
export interface Product {
  id: string;
  name: string;
  description: string;
  shortDescription?: string;
  price: number;
  originalPrice?: number;
  images: string[];
  category: string;
  subcategory?: string;
  tags?: string[];
  inStock: boolean;
  stockQuantity?: number;
  slug: string;
  sku?: string;
  weight?: number;
  dimensions?: string;

  // Gift box specific fields
  luxuryDescription?: string;
  budgetDescription?: string;
  narration?: string;

  // Product features and specifications
  features?: string[];
  luxuryFeatures?: string[];
  budgetFeatures?: string[];
  specifications?: Record<string, string>;
  materials?: string[];
  careInstructions?: string;
  warranty?: string;

  // Shipping and return information
  shippingInfo?: string;
  returnPolicy?: string;

  // Rating and review fields
  rating?: number;
  reviewCount?: number;

  // Additional product fields
  badge?: string;
  relatedProductIds?: string[];

  // SEO and admin fields
  metaTitle?: string;
  metaDescription?: string;
  isActive: boolean;
  isFeatured: boolean;
  sortOrder?: number;
  createdBy?: string;

  createdAt: string;
  updatedAt: string;
}

// Product input interface for creating/updating products
export interface ProductInput {
  name: string;
  description: string;
  shortDescription?: string;
  price: number;
  originalPrice?: number;
  images: string[];
  category: string;
  subcategory?: string;
  tags?: string[];
  inStock: boolean;
  stockQuantity?: number;
  slug: string;
  sku?: string;
  weight?: number;
  dimensions?: string;

  // Gift box specific fields
  luxuryDescription?: string;
  budgetDescription?: string;
  narration?: string;

  // Product features and specifications
  features?: string[];
  luxuryFeatures?: string[];
  budgetFeatures?: string[];
  specifications?: Record<string, string>;
  materials?: string[];
  careInstructions?: string;
  warranty?: string;

  // Shipping and return information
  shippingInfo?: string;
  returnPolicy?: string;

  // Rating and review fields
  rating?: number;
  reviewCount?: number;

  // Additional product fields
  badge?: string;
  relatedProductIds?: string[];

  // SEO and admin fields
  metaTitle?: string;
  metaDescription?: string;
  isActive?: boolean;
  isFeatured?: boolean;
  sortOrder?: number;
}

export interface GalleryItem {
  id: string;
  title: string;
  image: string;
  category: string;
  createdAt: string;
  description?: string;
  isActive?: boolean;
}

export interface Customer {
  id: string;
  name: string;
  email: string;
  phone?: string;
  createdAt: string;
  totalOrders?: number;
  totalSpent?: number;
}

export interface Order {
  id: string;
  orderNumber: string;
  customerId: string;
  customerName: string;
  customerEmail: string;
  status: string;
  total: number;
  createdAt: string;
  items?: OrderItem[];
}

export interface OrderItem {
  id: string;
  productId: string;
  productName: string;
  quantity: number;
  price: number;
  total: number;
}

interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

/**
 * Admin API using AWS Amplify GraphQL
 * Replaces the old PHP-based admin API
 */
export const adminApi = {
  // Dashboard Stats
  getDashboardStats: async (): Promise<ApiResponse> => {
    try {
      // Mock dashboard stats for now - would need to implement proper analytics
      const mockStats = {
        totalProducts: 25,
        totalOrders: 150,
        totalCustomers: 89,
        totalRevenue: 125000,
        recentOrders: [
          { id: '1', customer: 'John Doe', amount: 2500, status: 'completed' },
          { id: '2', customer: 'Jane Smith', amount: 1800, status: 'processing' },
          { id: '3', customer: 'Mike Johnson', amount: 3200, status: 'shipped' }
        ],
        topProducts: [
          { id: '1', name: 'Luxury Wedding Box', sales: 45 },
          { id: '2', name: 'Corporate Gift Set', sales: 32 },
          { id: '3', name: 'Festival Special Box', sales: 28 }
        ]
      };
      return {
        success: true,
        data: mockStats
      };
    } catch {
      return {
        success: false,
        error: 'Failed to fetch dashboard stats'
      };
    }
  },

  // Products
  getProducts: async (params?: {
    page?: number;
    limit?: number;
    category?: string;
    search?: string;
    isActive?: boolean;
  }): Promise<ApiResponse> => {
    try {
      const filter: Record<string, unknown> = {};

      // Add filters based on parameters
      if (params?.category) {
        filter.category = { eq: params.category };
      }
      if (params?.isActive !== undefined) {
        filter.isActive = { eq: params.isActive };
      }
      if (params?.search) {
        filter.or = [
          { name: { contains: params.search } },
          { description: { contains: params.search } },
          { tags: { contains: params.search } }
        ];
      }

      // NOTE: This implementation does not use nextToken for true pagination. To support real pagination, you should use nextToken from AWS Amplify GraphQL.
      const result = await client.graphql({
        query: listProducts,
        variables: {
          filter: Object.keys(filter).length > 0 ? filter : undefined,
          limit: params?.limit || 20
        }
      });

      console.log('GraphQL result:', result); // Debug log

      // Type guard for GraphQLResult
      const data = 'data' in result ? (result as GraphQLResult<{ listProducts?: { items?: Product[] } }> ).data : undefined;
      // Use type assertion for expected structure
      const products = data?.listProducts?.items || [];

      console.log('Extracted products:', products); // Debug log

      return {
        success: true,
        data: {
          products,
          pagination: {
            total: products.length,
            pages: Math.ceil(products.length / (params?.limit || 20)),
            currentPage: params?.page || 1
          }
        }
      };
    } catch (err) {
      console.error('Error fetching products:', err);
      return {
        success: false,
        error: 'Failed to fetch products'
      };
    }
  },

  getProduct: async (id: string): Promise<ApiResponse> => {
    try {
      // Use the already imported getProduct query
      const result = await client.graphql({
        query: getProduct,
        variables: { id }
      });
      // Type guard for GraphQLResult
      const data = 'data' in result ? (result as GraphQLResult<{ getProduct?: Product }> ).data : undefined;
      // Use type assertion for expected structure
      return {
        success: true,
        data: data?.getProduct
      };
    } catch (err) {
      console.error('Error fetching product:', err);
      return {
        success: false,
        error: 'Failed to fetch product'
      };
    }
  },

  createProduct: async (productData: ProductInput): Promise<ApiResponse> => {
    try {
      // Generate slug if not provided
      if (!productData.slug) {
        productData.slug = productData.name
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, '-')
          .replace(/(^-|-$)/g, '');
      }

      // Set default values
      const input = {
        ...productData,
        isActive: productData.isActive !== undefined ? productData.isActive : true,
        isFeatured: productData.isFeatured !== undefined ? productData.isFeatured : false,
        inStock: productData.inStock !== undefined ? productData.inStock : true,
        stockQuantity: productData.stockQuantity || 0,
        images: productData.images || []
      };

      const result = await client.graphql({
        query: createProduct,
        variables: { input }
      });

      return {
        success: true,
        data: (result.data as any)?.createProduct,
        message: 'Product created successfully'
      };
    } catch (error) {
      console.error('Error creating product:', error);
      return {
        success: false,
        error: 'Failed to create product: ' + (error instanceof Error ? error.message : 'Unknown error')
      };
    }
  },

  createProductWithFiles: async (formData: FormData): Promise<ApiResponse> => {
    try {
      // Extract form data
      const productData: Record<string, unknown> = {};
      const imageFiles: File[] = [];

      // Process form data
      for (const [key, value] of formData.entries()) {
        if (key.startsWith('images[')) {
          imageFiles.push(value as File);
        } else if (key === 'tags' || key === 'features' || key === 'luxuryFeatures' || key === 'budgetFeatures' || key === 'materials' || key === 'relatedProductIds') {
          try {
            productData[key] = JSON.parse(value as string);
          } catch {
            productData[key] = [];
          }
        } else if (key === 'specifications') {
          try {
            productData[key] = JSON.parse(value as string);
          } catch {
            productData[key] = {};
          }
        } else if (key === 'price' || key === 'originalPrice' || key === 'weight' || key === 'stockQuantity' || key === 'sortOrder') {
          const numValue = parseFloat(value as string);
          productData[key] = isNaN(numValue) ? undefined : numValue;
        } else if (key === 'rating') {
          const numValue = parseFloat(value as string);
          productData[key] = isNaN(numValue) ? undefined : numValue;
        } else if (key === 'reviewCount') {
          const numValue = parseInt(value as string);
          productData[key] = isNaN(numValue) ? undefined : numValue;
        } else if (key === 'isActive' || key === 'isFeatured' || key === 'inStock') {
          productData[key] = value === 'true';
        } else {
          productData[key] = value;
        }
      }

      // For now, we'll use placeholder image URLs since we don't have S3 upload implemented
      // In a real implementation, you would upload files to S3 and get URLs
      const imageUrls = imageFiles.map((_, index) =>
        `/images/products/placeholder-${Date.now()}-${index}.jpg`
      );

      productData.images = imageUrls;

      // FIX: Use adminApi.createProduct instead of this.createProduct
      return await adminApi.createProduct(productData as unknown as ProductInput);
    } catch (error) {
      console.error('Error creating product with files:', error);
      return {
        success: false,
        error: 'Failed to create product with files'
      };
    }
  },

  updateProduct: async (id: string, productData: Partial<ProductInput>): Promise<ApiResponse> => {
    try {
      const input = {
        id,
        ...productData
      };
      const result = await client.graphql({
        query: updateProduct,
        variables: { input }
      });
      // Type guard for GraphQLResult
      const data = 'data' in result ? (result as GraphQLResult<{ updateProduct?: Product }> ).data : undefined;
      // Use type assertion for expected structure
      return {
        success: true,
        data: data?.updateProduct,
        message: 'Product updated successfully'
      };
    } catch (err) {
      console.error('Error updating product:', err);
      return {
        success: false,
        error: 'Failed to update product: ' + (err instanceof Error ? err.message : 'Unknown error')
      };
    }
  },

  deleteProduct: async (id: string): Promise<ApiResponse> => {
    try {
      const result = await client.graphql({
        query: deleteProduct,
        variables: { input: { id } }
      });
      // Type guard for GraphQLResult
      const data = 'data' in result ? (result as GraphQLResult<{ deleteProduct?: Product }> ).data : undefined;
      // Use type assertion for expected structure
      return {
        success: true,
        data: data?.deleteProduct,
        message: 'Product deleted successfully'
      };
    } catch (err) {
      console.error('Error deleting product:', err);
      return {
        success: false,
        error: 'Failed to delete product: ' + (err instanceof Error ? err.message : 'Unknown error')
      };
    }
  },

  toggleProductStatus: async (id: string): Promise<ApiResponse> => {
    try {
      // First get the current product
      const productResponse = await adminApi.getProduct(id);
      if (!productResponse.success || !productResponse.data) {
        return {
          success: false,
          error: 'Product not found'
        };
      }
      const currentProduct = productResponse.data as Product | undefined;
      const newStatus = currentProduct ? !currentProduct.isActive : false;
      return await adminApi.updateProduct(id, { isActive: newStatus });
    } catch (err) {
      console.error('Error toggling product status:', err);
      return {
        success: false,
        error: 'Failed to toggle product status'
      };
    }
  },

  // Orders
  getOrders: async (): Promise<ApiResponse> => {
    try {
      // Mock orders for now - would need proper user context
      const mockOrders = [
        {
          id: '1',
          orderNumber: 'ORD-001',
          customerId: 'user1',
          customerName: 'John Doe',
          customerEmail: '<EMAIL>',
          status: 'completed',
          total: 2500,
          createdAt: new Date().toISOString()
        },
        {
          id: '2',
          orderNumber: 'ORD-002',
          customerId: 'user2',
          customerName: 'Jane Smith',
          customerEmail: '<EMAIL>',
          status: 'processing',
          total: 1800,
          createdAt: new Date().toISOString()
        }
      ];

      return {
        success: true,
        data: mockOrders
      };
    } catch {
      return {
        success: false,
        error: 'Failed to fetch orders'
      };
    }
  },

  updateOrderStatus: async (): Promise<ApiResponse> => {
    try {
      // Mock update for now
      return {
        success: true,
        message: 'Order status updated successfully'
      };
    } catch {
      return {
        success: false,
        error: 'Failed to update order status'
      };
    }
  },

  // Customers
  getCustomers: async (): Promise<ApiResponse> => {
    try {
      // Mock customers for now
      const mockCustomers = [
        {
          id: '1',
          name: 'John Doe',
          email: '<EMAIL>',
          phone: '+91-9876543210',
          createdAt: new Date().toISOString(),
          totalOrders: 3,
          totalSpent: 7500
        },
        {
          id: '2',
          name: 'Jane Smith',
          email: '<EMAIL>',
          phone: '+91-9876543211',
          createdAt: new Date().toISOString(),
          totalOrders: 2,
          totalSpent: 4200
        }
      ];

      return {
        success: true,
        data: mockCustomers
      };
    } catch {
      return {
        success: false,
        error: 'Failed to fetch customers'
      };
    }
  },

  deleteCustomer: async (): Promise<ApiResponse> => {
    try {
      // Mock delete for now
      return {
        success: true,
        message: 'Customer deleted successfully'
      };
    } catch {
      return {
        success: false,
        error: 'Failed to delete customer'
      };
    }
  },

  // Gallery
  getGalleryItems: async (category: string): Promise<ApiResponse> => {
    try {
      // Mock gallery items for now
      const mockGalleryItems: GalleryItem[] = [
        {
          id: '1',
          title: 'Wedding Collection',
          image: '/images/gallery/wedding-1.jpg',
          category: 'home',
          createdAt: new Date().toISOString(),
          description: 'Beautiful wedding gift boxes',
          isActive: true
        },
        {
          id: '2',
          title: 'Corporate Gifts',
          image: '/images/gallery/corporate-1.jpg',
          category: 'home',
          createdAt: new Date().toISOString(),
          description: 'Professional corporate gift sets',
          isActive: true
        }
      ];
      return {
        success: true,
        data: mockGalleryItems.filter(item => item.category === category)
      };
    } catch {
      return {
        success: false,
        error: 'Failed to fetch gallery items'
      };
    }
  },

  createGalleryItem: async (): Promise<ApiResponse> => {
    try {
      // Mock create for now
      return {
        success: true,
        message: 'Gallery item created successfully'
      };
    } catch {
      return {
        success: false,
        error: 'Failed to create gallery item'
      };
    }
  }
};
