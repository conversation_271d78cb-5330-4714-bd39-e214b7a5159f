/**
 * React Hook for Payment Processing with AWS Amplify
 * Handles Razorpay integration for Customine
 */

import { useState, useCallback } from 'react';
import { paymentAPI, PaymentOrderData, PaymentVerificationData } from '../utils/paymentAPI';
import { useAuth } from '../context/AuthContext';
import { useCart } from '../context/CartContext';
import toast from 'react-hot-toast';

interface PaymentState {
  loading: boolean;
  processing: boolean;
  error: string | null;
  orderId: string | null;
  razorpayOrderId: string | null;
}

interface PaymentHookReturn {
  paymentState: PaymentState;
  initiatePayment: (orderData: Omit<PaymentOrderData, 'userId'>) => Promise<void>;
  verifyPayment: (verificationData: Omit<PaymentVerificationData, 'orderId'>) => Promise<boolean>;
  resetPayment: () => void;
}

export function usePayment(): PaymentHookReturn {
  const { user } = useAuth();
  const { clearCart } = useCart();
  
  const [paymentState, setPaymentState] = useState<PaymentState>({
    loading: false,
    processing: false,
    error: null,
    orderId: null,
    razorpayOrderId: null
  });

  const resetPayment = useCallback(() => {
    setPaymentState({
      loading: false,
      processing: false,
      error: null,
      orderId: null,
      razorpayOrderId: null
    });
  }, []);

  const initiatePayment = useCallback(async (orderData: Omit<PaymentOrderData, 'userId'>) => {
    if (!user?.id) {
      throw new Error('User must be logged in to make payment');
    }

    setPaymentState(prev => ({ ...prev, loading: true, error: null }));

    try {
      // Create order in database
      const orderResult = await paymentAPI.createPaymentOrder({
        ...orderData,
        userId: user.id
      });

      if (!orderResult.success) {
        throw new Error(orderResult.error || 'Failed to create order');
      }

      const order = orderResult.data;
      
      // Create Razorpay order
      const razorpayOrder = paymentAPI.createRazorpayOrder(order);

      setPaymentState(prev => ({
        ...prev,
        loading: false,
        orderId: order.id,
        razorpayOrderId: razorpayOrder.id
      }));

      // Load Razorpay script and open checkout
      const scriptLoaded = await paymentAPI.loadRazorpayScript();
      if (!scriptLoaded) {
        throw new Error('Failed to load Razorpay script');
      }

      const razorpayConfig = paymentAPI.getRazorpayConfig();
      
      const options = {
        ...razorpayConfig,
        order_id: razorpayOrder.id,
        amount: razorpayOrder.amount,
        handler: async (response: any) => {
          setPaymentState(prev => ({ ...prev, processing: true }));
          
          try {
            const verified = await verifyPayment({
              razorpayOrderId: response.razorpay_order_id,
              razorpayPaymentId: response.razorpay_payment_id,
              razorpaySignature: response.razorpay_signature
            });

            if (verified) {
              toast.success('Payment successful! Order confirmed.');
              await clearCart(); // Clear cart after successful payment
            }
          } catch (error) {
            console.error('Payment verification failed:', error);
            toast.error('Payment verification failed. Please contact support.');
          } finally {
            setPaymentState(prev => ({ ...prev, processing: false }));
          }
        },
        modal: {
          ondismiss: () => {
            console.log('Payment modal dismissed');
            setPaymentState(prev => ({ ...prev, processing: false }));
          }
        },
        prefill: {
          name: orderData.shippingAddress.fullName,
          contact: orderData.shippingAddress.phone,
          email: user.email
        },
        notes: {
          order_id: order.id,
          order_number: order.orderNumber
        }
      };

      const razorpay = new window.Razorpay(options);
      razorpay.open();

    } catch (error: any) {
      console.error('Payment initiation failed:', error);
      setPaymentState(prev => ({
        ...prev,
        loading: false,
        error: error.message || 'Failed to initiate payment'
      }));
      
      // Handle payment failure in database
      if (paymentState.orderId) {
        await paymentAPI.handlePaymentFailure(paymentState.orderId, error.message);
      }
      
      toast.error('Failed to initiate payment. Please try again.');
      throw error;
    }
  }, [user, clearCart, paymentState.orderId]);

  const verifyPayment = useCallback(async (verificationData: Omit<PaymentVerificationData, 'orderId'>): Promise<boolean> => {
    if (!paymentState.orderId) {
      throw new Error('No order ID available for verification');
    }

    try {
      const result = await paymentAPI.verifyPayment({
        ...verificationData,
        orderId: paymentState.orderId
      });

      if (!result.success) {
        throw new Error(result.error || 'Payment verification failed');
      }

      console.log('Payment verified successfully');
      return true;

    } catch (error: any) {
      console.error('Payment verification error:', error);
      
      // Handle payment failure
      await paymentAPI.handlePaymentFailure(
        paymentState.orderId,
        error.message || 'Payment verification failed'
      );
      
      throw error;
    }
  }, [paymentState.orderId]);

  return {
    paymentState,
    initiatePayment,
    verifyPayment,
    resetPayment
  };
}

/**
 * Hook for order management
 */
export function useOrders() {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [orders, setOrders] = useState<any[]>([]);

  const fetchUserOrders = useCallback(async () => {
    if (!user?.id) return;

    setLoading(true);
    try {
      const result = await paymentAPI.getUserOrders(user.id);
      if (result.success) {
        setOrders(result.data);
      }
    } catch (error) {
      console.error('Error fetching orders:', error);
    } finally {
      setLoading(false);
    }
  }, [user?.id]);

  const getOrderDetails = useCallback(async (orderId: string) => {
    try {
      const result = await paymentAPI.getOrderDetails(orderId);
      return result.success ? result.data : null;
    } catch (error) {
      console.error('Error fetching order details:', error);
      return null;
    }
  }, []);

  return {
    orders,
    loading,
    fetchUserOrders,
    getOrderDetails
  };
}
