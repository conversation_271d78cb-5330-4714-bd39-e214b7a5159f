'use client';

import { useAuth } from '@/context/AuthContext';

export type UserRole = 'admin' | 'user' | 'manager';

export interface RolePermissions {
  canViewAllUsers: boolean;
  canManageProducts: boolean;
  canManageGallery: boolean;
  canViewAllInquiries: boolean;
  canViewAllOrders: boolean;
  canManageUserRoles: boolean;
  canAccessAnalytics: boolean;
  canManageSettings: boolean;
}

// Define permissions for each role
const rolePermissions: Record<UserRole, RolePermissions> = {
  admin: {
    canViewAllUsers: true,
    canManageProducts: true,
    canManageGallery: true,
    canViewAllInquiries: true,
    canViewAllOrders: true,
    canManageUserRoles: true,
    canAccessAnalytics: true,
    canManageSettings: true,
  },
  manager: {
    canViewAllUsers: true,
    canManageProducts: true,
    canManageGallery: false,
    canViewAllInquiries: true,
    canViewAllOrders: true,
    canManageUserRoles: false,
    canAccessAnalytics: true,
    canManageSettings: false,
  },
  user: {
    canViewAllUsers: false,
    canManageProducts: false,
    canManageGallery: false,
    canViewAllInquiries: false,
    canViewAllOrders: false, // Users can only see their own orders
    canManageUserRoles: false,
    canAccessAnalytics: false,
    canManageSettings: false,
  },
};

export function useRoleAccess() {
  const { user } = useAuth();

  // Get user role from profile, default to 'user' if not set or empty
  const userRole = (user?.profile?.role as UserRole) || 'user';

  // Ensure we always have a valid role (handle null, undefined, empty string)
  const normalizedRole = userRole && userRole.trim() !== '' ? userRole : 'user';
  
  // Get permissions for the current user role
  const permissions = rolePermissions[normalizedRole] || rolePermissions.user;

  // Helper functions for common permission checks
  const hasPermission = (permission: keyof RolePermissions): boolean => {
    return permissions[permission];
  };

  const isAdmin = (): boolean => {
    return normalizedRole === 'admin';
  };

  const isUser = (): boolean => {
    return normalizedRole === 'user';
  };

  const isManager = (): boolean => {
    return normalizedRole === 'manager';
  };

  const canAccess = (requiredRoles: UserRole[]): boolean => {
    return requiredRoles.includes(normalizedRole);
  };
  
  // Check if user can access a specific route
  const canAccessRoute = (route: string): boolean => {
    // Define route access rules
    const routeAccess: Record<string, UserRole[]> = {
      '/dashboard': ['admin', 'user', 'manager'],
      '/dashboard/profile': ['admin', 'user', 'manager'],
      '/dashboard/orders': ['admin', 'user', 'manager'],
      '/dashboard/products': ['admin', 'manager'],
      '/dashboard/gallery': ['admin'],
      '/dashboard/gallery/home': ['admin'],
      '/dashboard/gallery/luxury': ['admin'],
      '/dashboard/users': ['admin', 'manager'],
      '/dashboard/inquiries': ['admin', 'user', 'manager'],
      '/dashboard/admin-inquiries': ['admin', 'manager'],
      '/dashboard/settings': ['admin'],
    };
    
    // Check exact route first
    if (routeAccess[route]) {
      return routeAccess[route].includes(normalizedRole);
    }

    // Check parent routes for nested paths
    for (const [routePattern, allowedRoles] of Object.entries(routeAccess)) {
      if (route.startsWith(routePattern)) {
        return allowedRoles.includes(normalizedRole);
      }
    }
    
    // Default to user access for unknown routes
    return true;
  };
  
  return {
    userRole: normalizedRole, // Return the normalized role
    permissions,
    hasPermission,
    isAdmin,
    isUser,
    isManager,
    canAccess,
    canAccessRoute,
  };
}
