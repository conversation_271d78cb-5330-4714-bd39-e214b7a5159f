import { useState, useCallback } from 'react';
import { orderAPI } from '../utils/orderAPI';
import { Amplify } from 'aws-amplify';
import amplifyconfig from '../amplifyconfiguration.json';
import toast from 'react-hot-toast';

// Configure Amplify
Amplify.configure(amplifyconfig);

export interface OrderStatus {
  PENDING: 'PENDING';
  CONFIRMED: 'CONFIRMED';
  PROCESSING: 'PROCESSING';
  PACKED: 'PACKED';
  SHIPPED: 'SHIPPED';
  OUT_FOR_DELIVERY: 'OUT_FOR_DELIVERY';
  DELIVERED: 'DELIVERED';
  CANCELLED: 'CANCELLED';
  REFUNDED: 'REFUNDED';
  RETURNED: 'RETURNED';
}

export interface PaymentStatus {
  PENDING: 'PENDING';
  PROCESSING: 'PROCESSING';
  PAID: 'PAID';
  FAILED: 'FAILED';
  CANCELLED: 'CANCELLED';
  REFUNDED: 'REFUNDED';
  PARTIALLY_REFUNDED: 'PARTIALLY_REFUNDED';
}

export interface OrderTrackingData {
  id: string;
  orderNumber: string;
  status: keyof OrderStatus;
  paymentStatus: keyof PaymentStatus;
  trackingNumber?: string;
  estimatedDelivery?: string;
  deliveredAt?: string;
  createdAt: string;
  updatedAt: string;
  total: number;
  currency: string;
  shippingAddress: {
    name: string;
    addressLine1: string;
    city: string;
    state: string;
    pincode: string;
  };
  items: Array<{
    name: string;
    quantity: number;
    price: number;
  }>;
}

export const useOrderTracking = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [orderData, setOrderData] = useState<OrderTrackingData | null>(null);

  const trackOrder = useCallback(async (orderNumber: string) => {
    setLoading(true);
    setError(null);
    setOrderData(null);

    try {
      // Use orderAPI directly with AWS Amplify GraphQL
      const result = await orderAPI.trackOrder(orderNumber);

      if (result.success) {
        setOrderData(result.data);
        toast.success('Order found successfully!');
      } else {
        throw new Error(result.error || 'Order not found');
      }
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to track order';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const resetTracking = useCallback(() => {
    setOrderData(null);
    setError(null);
  }, []);

  return {
    loading,
    error,
    orderData,
    trackOrder,
    resetTracking,
  };
};

export const useUserOrders = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [orders, setOrders] = useState<OrderTrackingData[]>([]);

  const fetchUserOrders = useCallback(async (userId: string) => {
    setLoading(true);
    setError(null);

    try {
      // Use orderAPI directly with AWS Amplify GraphQL
      const result = await orderAPI.getUserOrders(userId);

      if (result.success) {
        setOrders(result.data || []);
      } else {
        throw new Error(result.error || 'Failed to fetch orders');
      }
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to fetch orders';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const refreshOrders = useCallback(async (userId: string) => {
    await fetchUserOrders(userId);
  }, [fetchUserOrders]);

  return {
    loading,
    error,
    orders,
    fetchUserOrders,
    refreshOrders,
  };
};

// Utility functions
export const getOrderStatusColor = (status: keyof OrderStatus): string => {
  const statusColors = {
    PENDING: 'text-yellow-600 bg-yellow-100',
    CONFIRMED: 'text-blue-600 bg-blue-100',
    PROCESSING: 'text-purple-600 bg-purple-100',
    PACKED: 'text-indigo-600 bg-indigo-100',
    SHIPPED: 'text-cyan-600 bg-cyan-100',
    OUT_FOR_DELIVERY: 'text-orange-600 bg-orange-100',
    DELIVERED: 'text-green-600 bg-green-100',
    CANCELLED: 'text-red-600 bg-red-100',
    REFUNDED: 'text-gray-600 bg-gray-100',
    RETURNED: 'text-pink-600 bg-pink-100',
  };
  return statusColors[status] || 'text-gray-600 bg-gray-100';
};

export const getPaymentStatusColor = (status: keyof PaymentStatus): string => {
  const statusColors = {
    PENDING: 'text-yellow-600 bg-yellow-100',
    PROCESSING: 'text-blue-600 bg-blue-100',
    PAID: 'text-green-600 bg-green-100',
    FAILED: 'text-red-600 bg-red-100',
    CANCELLED: 'text-gray-600 bg-gray-100',
    REFUNDED: 'text-purple-600 bg-purple-100',
    PARTIALLY_REFUNDED: 'text-orange-600 bg-orange-100',
  };
  return statusColors[status] || 'text-gray-600 bg-gray-100';
};

export const getOrderStatusText = (status: keyof OrderStatus): string => {
  const statusTexts = {
    PENDING: 'Order Placed',
    CONFIRMED: 'Order Confirmed',
    PROCESSING: 'Processing',
    PACKED: 'Packed',
    SHIPPED: 'Shipped',
    OUT_FOR_DELIVERY: 'Out for Delivery',
    DELIVERED: 'Delivered',
    CANCELLED: 'Cancelled',
    REFUNDED: 'Refunded',
    RETURNED: 'Returned',
  };
  return statusTexts[status] || status;
};
