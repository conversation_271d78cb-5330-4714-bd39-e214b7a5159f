'use client';

import React from 'react';
import Link from 'next/link';
import { 
  Package, 
  Truck, 
  CheckCircle, 
  Clock, 
  CreditCard,
  ArrowLeft,
  Copy,
  ExternalLink
} from 'lucide-react';
import toast from 'react-hot-toast';

export default function DemoPage() {



  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <Link
              href="/"
              className="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ArrowLeft className="w-5 h-5 mr-2" />
              Back to Home
            </Link>
            <h1 className="text-2xl font-bold text-gray-900">Demo - Order Tracking</h1>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Introduction */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <Package className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-blue-900 mb-2">AWS Amplify Order Tracking System</h3>
              <p className="text-blue-800 mb-4">
                Welcome to the Customine order tracking system powered by AWS Amplify! Create real orders through the checkout process and track them using our GraphQL-based order tracking functionality.
              </p>
              <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4">
                <Link
                  href="/collections"
                  className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <Package className="w-4 h-4 mr-2" />
                  Shop & Create Order
                </Link>
                <Link
                  href="/track-order"
                  className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  <ExternalLink className="w-4 h-4 mr-2" />
                  Track Order
                </Link>
                <Link
                  href="/dashboard/orders"
                  className="inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                >
                  <ExternalLink className="w-4 h-4 mr-2" />
                  Orders Dashboard
                </Link>
                <Link
                  href="/test-amplify"
                  className="inline-flex items-center px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
                >
                  <ExternalLink className="w-4 h-4 mr-2" />
                  Test AWS Amplify
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* How to Create Orders */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">How to Create & Track Orders</h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Step 1: Shop */}
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Package className="w-8 h-8 text-blue-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">1. Shop Products</h3>
              <p className="text-gray-600 mb-4">Browse our collections and add items to your cart</p>
              <Link
                href="/collections"
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Start Shopping
              </Link>
            </div>

            {/* Step 2: Checkout */}
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <CreditCard className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">2. Complete Checkout</h3>
              <p className="text-gray-600 mb-4">Fill in your details and complete the payment process</p>
              <Link
                href="/cart"
                className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                View Cart
              </Link>
            </div>

            {/* Step 3: Track */}
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Truck className="w-8 h-8 text-purple-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">3. Track Order</h3>
              <p className="text-gray-600 mb-4">Use your order number to track delivery status</p>
              <Link
                href="/track-order"
                className="inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
              >
                Track Order
              </Link>
            </div>
          </div>

          {/* Order Number Info */}
          <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                <Clock className="w-5 h-5 text-yellow-600" />
              </div>
              <div>
                <h4 className="font-medium text-yellow-900">Order Number Format</h4>
                <p className="text-yellow-800 text-sm mt-1">
                  After completing checkout, you'll receive an order number in the format: <code className="bg-yellow-100 px-1 rounded">CUS123456ABC</code>
                </p>
                <p className="text-yellow-800 text-sm mt-1">
                  Save this number to track your order status and delivery information.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Instructions */}
        <div className="bg-white rounded-lg shadow-sm p-6 mt-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Complete Order Flow</h3>
          <div className="space-y-3 text-gray-700">
            <div className="flex items-start space-x-3">
              <span className="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-semibold">1</span>
              <p>Browse <Link href="/collections" className="text-blue-600 hover:text-blue-800 font-medium">Collections</Link> and add products to your cart</p>
            </div>
            <div className="flex items-start space-x-3">
              <span className="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-semibold">2</span>
              <p>Go to <Link href="/cart" className="text-blue-600 hover:text-blue-800 font-medium">Cart</Link> and proceed to checkout</p>
            </div>
            <div className="flex items-start space-x-3">
              <span className="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-semibold">3</span>
              <p>Complete the checkout process with your shipping details and payment</p>
            </div>
            <div className="flex items-start space-x-3">
              <span className="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-semibold">4</span>
              <p>Save your order number and use it to <Link href="/track-order" className="text-blue-600 hover:text-blue-800 font-medium">Track Order</Link> status</p>
            </div>
            <div className="flex items-start space-x-3">
              <span className="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-semibold">5</span>
              <p>View all your orders in the <Link href="/dashboard/orders" className="text-blue-600 hover:text-blue-800 font-medium">Orders Dashboard</Link></p>
            </div>
          </div>
        </div>

        {/* Features */}
        <div className="bg-white rounded-lg shadow-sm p-6 mt-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Features Demonstrated</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
              <div>
                <p className="font-medium text-gray-900">Order Status Tracking</p>
                <p className="text-sm text-gray-600">Real-time order status with progress indicators</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
              <div>
                <p className="font-medium text-gray-900">Delivery Information</p>
                <p className="text-sm text-gray-600">Tracking numbers and estimated delivery dates</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
              <div>
                <p className="font-medium text-gray-900">Order History</p>
                <p className="text-sm text-gray-600">Complete order management dashboard</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
              <div>
                <p className="font-medium text-gray-900">Search & Filter</p>
                <p className="text-sm text-gray-600">Find orders quickly with advanced filtering</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
