'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Search, Grid, List, ChevronDown, Star, Heart, ShoppingCart } from 'lucide-react';
import { fetchProducts, testAPIConnection, Product } from '../../../utils/productAPI';

// Extended type to handle both static and API product data
type ProductWithImages = Product & {
  image?: string;
  hoverImage?: string;
};

// All data now comes from API - no static fallbacks


export default function CollectionsAll() {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState('featured');
  const [filterBy, setFilterBy] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [allProducts, setAllProducts] = useState<ProductWithImages[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<ProductWithImages[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 10;
  const totalPages = Math.ceil(filteredProducts.length / pageSize);

  // Helper function to get product image from API data
  const getProductImage = (product: ProductWithImages, index: number = 0): string => {
    // For API data (has images array)
    if (product.images && product.images.length > 0) {
      return `https://customine4ae3bb77bc244f68885ca662b9bd1b4e1a04b-dev.s3.ap-south-1.amazonaws.com/public/${product.images[index] || product.images[0]}`;
    }
    // Fallback to placeholder
    return '/placeholder-image.jpg';
  };

  // Load products from API
  useEffect(() => {
    const loadProducts = async () => {
      try {
        setLoading(true);
        setError(null);

        // Test API connection first
        const apiTest = await testAPIConnection();
        console.log('API Test Result:', apiTest);

        if (!apiTest.connected) {
          console.warn('API not connected:', apiTest.error);
          setAllProducts([]);
          setError(`API unavailable: ${apiTest.error}. Please check your connection and try again.`);
        } else if (!apiTest.hasProducts) {
          console.warn('Database is empty');
          setAllProducts([]);
          setError('Database is empty. Please import products using the admin panel.');
        } else {
          // API is working, fetch products
          const result = await fetchProducts();
          if (result.items.length === 0) {
            console.warn('No products returned from API');
            setAllProducts([]);
            setError('No products found in database. Please import products using the admin panel.');
          } else {
            setAllProducts(result.items as ProductWithImages[]);
            setError(null);
          }
        }
      } catch (err) {
        console.error('Error loading products:', err);
        setError('Failed to load products. Please check your connection and try again.');
        setAllProducts([]);
      } finally {
        setLoading(false);
      }
    };

    loadProducts();
  }, []);

  // Filter and search products
  useEffect(() => {
    let filtered = allProducts;

    // Filter by category
    if (filterBy !== 'all') {
      filtered = filtered.filter(product => product.category === filterBy);
    }

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Sort products
    switch (sortBy) {
      case 'price-low':
        filtered.sort((a, b) => a.price - b.price);
        break;
      case 'price-high':
        filtered.sort((a, b) => b.price - a.price);
        break;
      case 'rating':
        filtered.sort((a, b) => (b.rating || 0) - (a.rating || 0));
        break;
      case 'newest':
        filtered.sort((a, b) => new Date(b.createdAt || '').getTime() - new Date(a.createdAt || '').getTime());
        break;
      default:
        // Featured - keep original order or sort by featured flag
        filtered.sort((a, b) => (b.isFeatured ? 1 : 0) - (a.isFeatured ? 1 : 0));
        break;
    }

    setFilteredProducts(filtered);
    setCurrentPage(1); // Reset to first page when filters change
  }, [allProducts, filterBy, searchTerm, sortBy]);

  // Reset currentPage to 1 when filters/search change
  useEffect(() => {
    setCurrentPage(1);
  }, [filterBy, searchTerm, sortBy]);

  const paginatedProducts = filteredProducts.slice((currentPage - 1) * pageSize, currentPage * pageSize);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Breadcrumb */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4 py-4">
          <nav className="text-sm text-gray-600">
            <Link href="/" className="hover:text-gray-900 transition-colors">Home</Link>
            <span className="mx-2">»</span>
            <span className="text-gray-900">Luxury Gift Box Collection</span>
          </nav>
        </div>
      </div>

      {/* Hero Section */}
      <div className="relative bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-800 text-white overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-black bg-opacity-20"></div>
        <div className="absolute top-0 left-0 w-full h-full">
          <div className="absolute top-10 left-10 w-32 h-32 bg-white bg-opacity-10 rounded-full blur-xl"></div>
          <div className="absolute top-32 right-20 w-24 h-24 bg-pink-300 bg-opacity-20 rounded-full blur-lg"></div>
          <div className="absolute bottom-20 left-1/4 w-40 h-40 bg-purple-300 bg-opacity-15 rounded-full blur-2xl"></div>
          <div className="absolute bottom-10 right-10 w-28 h-28 bg-indigo-300 bg-opacity-20 rounded-full blur-xl"></div>
        </div>

        <div className="relative container mx-auto px-4 py-20">
          <div className="max-w-5xl mx-auto text-center">
            {/* Main Heading */}
            <div className="mb-8">
              <h1 className="text-6xl md:text-7xl font-bold mb-4 bg-gradient-to-r from-white via-pink-100 to-purple-100 bg-clip-text text-transparent">
                Luxury Gift Box Collection
              </h1>
              <div className="w-24 h-1 bg-gradient-to-r from-pink-400 to-purple-400 mx-auto rounded-full"></div>
            </div>

            {/* Description */}
            <p className="text-xl md:text-2xl text-gray-100 mb-12 leading-relaxed max-w-4xl mx-auto">
              Curated with love, delivered with care. Our premium gift boxes feature handpicked items
              from artisan makers and beloved brands across India.
            </p>

            {/* Feature Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-6 border border-white border-opacity-20 hover:bg-opacity-20 transition-all duration-300">
                <div className="flex items-center justify-center mb-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full flex items-center justify-center">
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                  </div>
                </div>
                <h3 className="text-lg font-bold mb-2 text-gray-900">Free India Shipping</h3>
                <p className="text-gray-700 text-sm font-medium">On orders above ₹2000</p>
              </div>

              <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-6 border border-white border-opacity-20 hover:bg-opacity-20 transition-all duration-300">
                <div className="flex items-center justify-center mb-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-400 to-cyan-500 rounded-full flex items-center justify-center">
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                    </svg>
                  </div>
                </div>
                <h3 className="text-lg font-bold mb-2 text-gray-900">Handwritten Notes</h3>
                <p className="text-gray-700 text-sm font-medium">Personal touch included</p>
              </div>

              <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-6 border border-white border-opacity-20 hover:bg-opacity-20 transition-all duration-300">
                <div className="flex items-center justify-center mb-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-purple-400 to-pink-500 rounded-full flex items-center justify-center">
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                    </svg>
                  </div>
                </div>
                <h3 className="text-lg font-bold mb-2 text-gray-900">100% Women Owned</h3>
                <p className="text-gray-700 text-sm font-medium">Supporting women entrepreneurs</p>
              </div>
            </div>

            {/* Stats */}
            <div className="flex flex-wrap justify-center gap-8 text-center">
              <div className="bg-white bg-opacity-5 rounded-lg px-6 py-4">
                <div className="text-3xl font-bold text-white">{filteredProducts.length}+</div>
                <div className="text-gray-300 text-sm">Curated Products</div>
              </div>
              <div className="bg-white bg-opacity-5 rounded-lg px-6 py-4">
                <div className="text-3xl font-bold text-white">500+</div>
                <div className="text-gray-300 text-sm">Happy Customers</div>
              </div>
              <div className="bg-white bg-opacity-5 rounded-lg px-6 py-4">
                <div className="text-3xl font-bold text-white">4.8★</div>
                <div className="text-gray-300 text-sm">Average Rating</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white border-b sticky top-0 z-40">
        <div className="container mx-auto px-4 py-4">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search gift boxes..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-slate-300 rounded-xl focus:ring-2 focus:ring-slate-900 focus:border-transparent text-slate-900 placeholder-slate-500 shadow-sm"
              />
            </div>

            {/* Filters and Controls */}
            <div className="flex items-center space-x-4">
              {/* Category Filter */}
              <div className="relative">
                <select
                  value={filterBy}
                  onChange={(e) => setFilterBy(e.target.value)}
                  className="appearance-none bg-white border border-slate-300 rounded-xl px-4 py-2 pr-8 focus:ring-2 focus:ring-slate-900 focus:border-transparent text-slate-900 font-medium shadow-sm"
                >
                  <option value="all">All Categories</option>
                  <option value="luxury">Luxury</option>
                  <option value="budget">Budget-Friendly</option>
                </select>
                <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4 pointer-events-none" />
              </div>

              {/* Sort */}
              <div className="relative">
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="appearance-none bg-white border border-slate-300 rounded-xl px-4 py-2 pr-8 focus:ring-2 focus:ring-slate-900 focus:border-transparent text-slate-900 font-medium shadow-sm"
                >
                  <option value="featured">Featured</option>
                  <option value="newest">Newest</option>
                  <option value="price-low">Price: Low to High</option>
                  <option value="price-high">Price: High to Low</option>
                  <option value="rating">Highest Rated</option>
                </select>
                <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4 pointer-events-none" />
              </div>

              {/* View Mode */}
              <div className="flex border border-slate-300 rounded-xl overflow-hidden shadow-sm">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 transition-all duration-200 ${viewMode === 'grid' ? 'bg-slate-900 text-white' : 'bg-white text-slate-600 hover:bg-slate-50 hover:text-slate-900'}`}
                >
                  <Grid className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 transition-all duration-200 ${viewMode === 'list' ? 'bg-slate-900 text-white' : 'bg-white text-slate-600 hover:bg-slate-50 hover:text-slate-900'}`}
                >
                  <List className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>

          {/* Results count */}
          <div className="mt-4 text-sm text-slate-600 font-medium">
            Showing {filteredProducts.length} of {allProducts.length} products
            {filterBy !== 'all' && (
              <span className="ml-2 px-3 py-1 bg-slate-100 rounded-full text-xs font-semibold text-slate-700">
                {filterBy === 'luxury' ? 'Luxury' : 'Budget-Friendly'}
              </span>
            )}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        {/* Loading State */}
        {loading && (
          <div className="flex items-center justify-center py-20">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Loading products...</p>
            </div>
          </div>
        )}

        {/* Error State */}
        {error && !loading && (
          <div className="flex items-center justify-center py-20">
            <div className="text-center max-w-md mx-auto">
              <div className="text-red-500 mb-4">
                <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Products Available</h3>
              <p className="text-gray-600 mb-4">{error}</p>
              <div className="space-y-2">
                <button
                  onClick={() => window.location.reload()}
                  className="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors mr-2"
                >
                  Try Again
                </button>
                <Link
                  href="/dashboard/bulk-import"
                  className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors inline-block"
                >
                  Import Products
                </Link>
              </div>
              <p className="text-xs text-gray-500 mt-3">
                Go to Admin → Bulk Import to populate the database with products.
              </p>
            </div>
          </div>
        )}

        {/* Products Grid/List */}
        {!loading && !error && (
          <>
            {viewMode === 'grid' ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {paginatedProducts.map((product) => (
              <div key={product.id} className="group bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden">
                <Link href={`/product?id=${product.id}`} className="block">
                  <div className="relative overflow-hidden aspect-square">
                    {/* Product Badge */}
                    {product.badge && (
                      <div className="absolute top-3 left-3 z-10">
                        <span className={`px-3 py-1 text-xs font-bold rounded-full shadow-sm ${
                          product.badge === 'Bestseller' ? 'bg-emerald-100 text-emerald-800' :
                          product.badge === 'New' ? 'bg-blue-100 text-blue-800' :
                          product.badge === 'Popular' ? 'bg-purple-100 text-purple-800' :
                          product.badge === 'Premium' ? 'bg-amber-100 text-amber-800' :
                          product.badge === 'Coffee Lover' ? 'bg-orange-100 text-orange-800' :
                          product.badge === 'Chef&apos;s Choice' ? 'bg-red-100 text-red-800' :
                          'bg-slate-100 text-slate-800'
                        }`}>
                          {product.badge}
                        </span>
                      </div>
                    )}

                    {/* Wishlist Button */}
                    <button className="absolute top-3 right-3 z-10 p-2 bg-white rounded-full shadow-md opacity-0 group-hover:opacity-100 transition-opacity duration-300 hover:bg-slate-50">
                      <Heart className="w-4 h-4 text-slate-600" />
                    </button>

                    {/* Product Images */}
                    {/* eslint-disable-next-line @next/next/no-img-element */}
                    <img
                      src={getProductImage(product, 0)}
                      alt={product.name}
                      className="object-cover transition-transform duration-500 group-hover:scale-105"
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                    />
                    {getProductImage(product, 1) !== getProductImage(product, 0) && (
                      /* eslint-disable-next-line @next/next/no-img-element */
                      <img
                        src={getProductImage(product, 1)}
                        alt={`${product.name} - alternate view`}
                        className="object-cover opacity-0 transition-all duration-500 group-hover:opacity-100 group-hover:scale-105"
                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                      />
                    )}

              
                  </div>

                  {/* Product Info */}
                  <div className="p-4">
                    <h3 className="font-bold text-slate-900 group-hover:text-slate-700 transition-colors mb-1">
                      {product.name}
                    </h3>
                    <p className="text-sm text-slate-600 mb-2 line-clamp-2 font-medium">
                      {product.description}
                    </p>

                    {/* Rating */}
                    {product.rating && (
                      <div className="flex items-center space-x-1 mb-2">
                        <div className="flex">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className={`w-3 h-3 ${
                                i < Math.floor(product.rating!) ? 'text-amber-400 fill-current' : 'text-slate-300'
                              }`}
                            />
                          ))}
                        </div>
                        <span className="text-xs text-slate-600 font-medium">({product.reviews})</span>
                      </div>
                    )}

                    {/* Price */}
                    <div className="flex items-center space-x-2">
                      <span className="text-lg font-bold text-slate-900">
                        ₹{product.price.toLocaleString()}
                      </span>
                      {product.originalPrice && (
                        <span className="text-sm text-slate-500 line-through font-medium">
                          ₹{product.originalPrice.toLocaleString()}
                        </span>
                      )}
                    </div>
                  </div>
                </Link>
              </div>
            ))}
          </div>
        ) : (
          /* List View */
          <div className="space-y-4">
            {paginatedProducts.map((product) => (
              <div key={product.id} className="bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300 overflow-hidden">
                <Link href={`/product?id=${product.id}`} className="flex">
                  <div className="relative w-48 h-48 flex-shrink-0">
                     {/* eslint-disable-next-line @next/next/no-img-element */}
                    <img
                      src={getProductImage(product, 0)}
                      alt={product.name}
                      className="object-cover"
                      sizes="192px"
                    />
                    {product.badge && (
                      <div className="absolute top-3 left-3">
                        <span className={`px-3 py-1 text-xs font-bold rounded-full shadow-sm ${
                          product.badge === 'Bestseller' ? 'bg-emerald-100 text-emerald-800' :
                          product.badge === 'New' ? 'bg-blue-100 text-blue-800' :
                          product.badge === 'Popular' ? 'bg-purple-100 text-purple-800' :
                          product.badge === 'Premium' ? 'bg-amber-100 text-amber-800' :
                          product.badge === 'Coffee Lover' ? 'bg-orange-100 text-orange-800' :
                          product.badge === 'Chef&apos;s Choice' ? 'bg-red-100 text-red-800' :
                          'bg-slate-100 text-slate-800'
                        }`}>
                          {product.badge}
                        </span>
                      </div>
                    )}
                  </div>
                  <div className="flex-1 p-6">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="text-xl font-bold text-slate-900">{product.name}</h3>
                      <button className="p-2 hover:bg-slate-50 rounded-full">
                        <Heart className="w-5 h-5 text-slate-400" />
                      </button>
                    </div>
                    <p className="text-slate-600 mb-3 font-medium">{product.description}</p>

                    {/* Rating */}
                    {product.rating && (
                      <div className="flex items-center space-x-2 mb-3">
                        <div className="flex">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className={`w-4 h-4 ${
                                i < Math.floor(product.rating!) ? 'text-amber-400 fill-current' : 'text-slate-300'
                              }`}
                            />
                          ))}
                        </div>
                        <span className="text-sm text-slate-600 font-medium">({product.reviews} reviews)</span>
                      </div>
                    )}

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <span className="text-2xl font-bold text-slate-900">
                          ₹{product.price.toLocaleString()}
                        </span>
                        {product.originalPrice && (
                          <span className="text-lg text-slate-500 line-through font-medium">
                            ₹{product.originalPrice.toLocaleString()}
                          </span>
                        )}
                      </div>
                      <button className="bg-slate-900 text-white px-6 py-2 rounded-xl font-semibold hover:bg-slate-800 transition-all duration-200 flex items-center space-x-2 shadow-lg">
                        <ShoppingCart className="w-4 h-4" />
                        <span>Add to Cart</span>
                      </button>
                    </div>
                  </div>
                </Link>
              </div>
            ))}
          </div>
        )}

        {/* No Results */}
        {filteredProducts.length === 0 && (
          <div className="text-center py-16">
            <div className="text-gray-400 mb-4">
              <Search className="w-16 h-16 mx-auto" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">No products found</h3>
            <p className="text-gray-600 mb-4">Try adjusting your search or filter criteria</p>
            <button
              onClick={() => {
                setSearchTerm('');
                setFilterBy('all');
              }}
              className="bg-gray-900 text-white px-6 py-2 rounded-lg font-medium hover:bg-gray-800 transition-colors"
            >
              Clear Filters
            </button>
          </div>
        )}



        {/* Pagination */}
        {filteredProducts.length > 0 && (
          <div className="mt-12 flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0">
            <div className="text-sm text-gray-600">
              Showing {paginatedProducts.length} of {filteredProducts.length} products (Page {currentPage} of {totalPages})
            </div>
            <div className="flex items-center space-x-2">
              <button
                className="px-4 py-2 border border-gray-300 rounded-lg text-gray-600 hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                onClick={() => setCurrentPage(currentPage - 1)}
                disabled={currentPage === 1}
              >
                Previous
              </button>
              <div className="flex space-x-1">
                {Array.from({ length: totalPages }, (_, i) => (
                  <button
                    key={i + 1}
                    className={`px-3 py-2 rounded-lg ${currentPage === i + 1 ? 'bg-gray-900 text-white' : 'text-gray-600 hover:bg-gray-100 transition-colors'}`}
                    onClick={() => setCurrentPage(i + 1)}
                  >
                    {i + 1}
                  </button>
                ))}
              </div>
              <button
                className="px-4 py-2 border border-gray-300 rounded-lg text-gray-600 hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                onClick={() => setCurrentPage(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                Next
              </button>
            </div>
          </div>
        )}
        </>
        )}

        {/* Trust Indicators */}
        <div className="mt-16 bg-white rounded-2xl p-8 shadow-sm">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div>
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Quality Guaranteed</h4>
              <p className="text-sm text-gray-600">Every item is carefully curated and quality-checked before shipping</p>
            </div>
            <div>
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Fast Delivery</h4>
              <p className="text-sm text-gray-600">Free shipping across India on orders above ₹2000</p>
            </div>
            <div>
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Handwritten Notes</h4>
              <p className="text-sm text-gray-600">Personal touch with complimentary handwritten messages</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
