"use client";

import React, { useState } from 'react';
import Link from 'next/link';

export default function LoginPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);
    // Dummy validation
    if (!email || !password) {
      setError('Please enter both email and password.');
      setLoading(false);
      return;
    }
    // Simulate login
    setTimeout(() => {
      setLoading(false);
      if (email !== '<EMAIL>' || password !== 'password') {
        setError('Invalid email or password.');
      } else {
        // Redirect or set auth state here
        alert('Login successful!');
      }
    }, 1200);
  };

  return (
    <div className="min-h-screen flex items-center justify-center relative px-4">
      {/* Blurred background overlay */}
      <div className="absolute inset-0 bg-[var(--color-bg-main)]/80 backdrop-blur-md z-0" />
      <div className="w-full max-w-md bg-white rounded-2xl shadow-lg p-8 relative z-10">
        <h2 className="text-3xl font-bold text-center mb-6 text-[var(--color-navy)]">Login</h2>
        <form onSubmit={handleSubmit} className="space-y-5">
          <div>
            <label className="block text-sm font-medium text-[var(--color-navy)] mb-1">Email</label>
            <input
              type="email"
              className="w-full px-4 py-2 border border-[var(--color-sky-blue)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-navy)]"
              value={email}
              onChange={e => setEmail(e.target.value)}
              autoComplete="email"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-[var(--color-navy)] mb-1">Password</label>
            <input
              type="password"
              className="w-full px-4 py-2 border border-[var(--color-sky-blue)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-navy)]"
              value={password}
              onChange={e => setPassword(e.target.value)}
              autoComplete="current-password"
              required
            />
          </div>
          {error && <div className="text-red-600 text-sm text-center">{error}</div>}
          <button
            type="submit"
            className="w-full py-3 rounded-lg font-bold text-white bg-[var(--color-navy)] hover:bg-[var(--color-teal)] transition-colors duration-200 disabled:opacity-60"
            disabled={loading}
          >
            {loading ? 'Logging in...' : 'Login'}
          </button>
        </form>
        <div className="mt-6 text-center text-sm text-[var(--color-navy)]">
          Don&apos;t have an account?{' '}
          <Link href="/register" className="text-[var(--color-teal)] font-semibold hover:underline">Register</Link>
        </div>
      </div>
    </div>
  );
} 