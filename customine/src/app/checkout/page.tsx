'use client';

import React from 'react';
import { useAuth } from '../../context/AuthContext';
import CheckoutFormNew from '../../components/CheckoutFormNew';

export default function CheckoutPage() {
  const { isLoggedIn } = useAuth();

  if (!isLoggedIn) {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{ background: 'var(--color-bg-main)' }}>
        <div className="text-center">
          <h2 className="text-2xl font-bold text-[var(--color-navy)] mb-4">Please Login</h2>
          <p className="text-[var(--color-text-secondary)]">You need to be logged in to proceed with checkout.</p>
        </div>
      </div>
    );
  }

  return (
    <CheckoutFormNew />
  );
}
