'use client';

import React, { useState, useEffect } from 'react';
import { Package, ShoppingCart, Users, TrendingUp, Eye, Plus, Loader2, ExternalLink } from 'lucide-react';
import Link from 'next/link';
import { adminApi } from '../../services/adminApi';
import ApiNotification from '../../components/dashboard/ApiNotification';
import UserDataInfo from '../../components/dashboard/UserDataInfo';
import UserDashboard from '../../components/dashboard/UserDashboard';
import APIDebugger from '../../components/APIDebugger';
import { useAuth } from '../../context/AuthContext';
import { useRoleAccess } from '../../hooks/useRoleAccess';

interface RecentOrder {
  id: string;
  customer: string;
  amount: number;
  status: string;
  date?: string;
}

interface DashboardStats {
  totalProducts: number;
  totalOrders: number;
  totalCustomers: number;
  revenue: string;
  recentOrders: RecentOrder[];
  monthlyGrowth?: {
    products: string;
    orders: string;
    customers: string;
    revenue: string;
  };
}

const quickActions = [
  {
    title: 'Add New Product',
    description: 'Create a new gift box product',
    href: '/dashboard/products/add',
    icon: Plus,
    color: 'bg-blue-500',
  },
  {
    title: 'Manage All Orders',
    description: 'View and manage customer orders',
    href: '/dashboard/admin-orders',
    icon: ShoppingCart,
    color: 'bg-orange-500',
  },
  {
    title: 'Manage Home Gallery',
    description: 'Update home page images',
    href: '/dashboard/gallery/home',
    icon: Eye,
    color: 'bg-green-500',
  },
  {
    title: 'Luxury Cards Gallery',
    description: 'Manage luxury collection images',
    href: '/dashboard/gallery/luxury',
    icon: Package,
    color: 'bg-purple-500',
  },
  {
    title: 'View Main Website',
    description: 'Go to the public Customine website',
    href: '/',
    icon: ExternalLink,
    color: 'bg-gray-500',
    external: true,
  },
];

export default function AdminDashboard() {
  const { user } = useAuth();
  const { isAdmin } = useRoleAccess();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // If user is not admin, show user dashboard
  if (!isAdmin()) {
    return <UserDashboard />;
  }

  useEffect(() => {
    try {
      fetchDashboardStats();
    } catch (err) {
      console.error('Error in useEffect:', err);
      setError('Failed to initialize dashboard');
      setLoading(false);
    }
  }, []);

  const fetchDashboardStats = async () => {
    try {
      setLoading(true);

      // Try to fetch from API, but fall back to static data if it fails
      const response = await adminApi.getDashboardStats();

      if (response.success && response.data) {
        setStats(response.data);
        setLoading(false);
        return;
      } else {
        console.warn('API failed, using static data. Error:', response.error);
      }

      // Fallback to static data
      const staticStats: DashboardStats = {
        totalProducts: 20,
        totalOrders: 247,
        totalCustomers: 156,
        revenue: '₹4,56,789',
        recentOrders: [
          { id: 'CUS2024001', customer: 'John Doe', amount: 2499, status: 'completed', date: '2024-01-15' },
          { id: 'CUS2024002', customer: 'Jane Smith', amount: 1899, status: 'processing', date: '2024-01-14' },
          { id: 'CUS2024003', customer: 'Mike Johnson', amount: 3299, status: 'shipped', date: '2024-01-13' },
          { id: 'CUS2024004', customer: 'Sarah Wilson', amount: 2199, status: 'pending', date: '2024-01-12' },
        ],
        monthlyGrowth: {
          products: '+12%',
          orders: '+8%',
          customers: '+15%',
          revenue: '+23%'
        }
      };

      setStats(staticStats);

    } catch (err) {
      console.error('Error fetching dashboard stats:', err);
      setError('Failed to load dashboard statistics');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center max-w-md">
          <div className="text-red-600 mb-4">
            <h2 className="text-xl font-semibold mb-2">Something went wrong</h2>
            <p className="text-sm mb-4">The admin dashboard encountered an error. This might be due to API connectivity issues.</p>
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
              <h3 className="font-semibold text-red-800 mb-2">Error Details</h3>
              <p className="text-red-700 text-sm break-words">{error}</p>
            </div>
          </div>
          <div className="space-y-2">
            <button
              onClick={fetchDashboardStats}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 w-full"
            >
              Retry
            </button>
            <a
              href="/dashboard/debug-simple"
              className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 w-full inline-block"
            >
              Debug API Issues
            </a>
          </div>
        </div>
      </div>
    );
  }

  const statsData = [
    {
      title: 'Total Products',
      value: stats?.totalProducts?.toString() || '0',
      change: stats?.monthlyGrowth?.products || '+0%',
      changeType: 'positive',
      icon: Package,
    },
    {
      title: 'Total Orders',
      value: (stats?.totalOrders ?? 0).toLocaleString(),
      change: stats?.monthlyGrowth?.orders || '+0%',
      changeType: 'positive',
      icon: ShoppingCart,
    },
    {
      title: 'Total Customers',
      value: (stats?.totalCustomers ?? 0).toLocaleString(),
      change: stats?.monthlyGrowth?.customers || '+0%',
      changeType: 'positive',
      icon: Users,
    },
    {
      title: 'Revenue',
      value: stats?.revenue || '₹0',
      change: stats?.monthlyGrowth?.revenue || '+0%',
      changeType: 'positive',
      icon: TrendingUp,
    },
  ];
  return (
    <div className="space-y-6">
      {/* API Notification */}
   


      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Welcome back, {user?.name || 'Admin'}! 👋
            </h1>
            <p className="text-gray-600 mt-2">
              Here&apos;s what&apos;s happening with your personalized Customine store.
            </p>
            <div className="mt-4 flex items-center space-x-4 text-sm text-gray-500">
              <span>📧 {user?.email}</span>
              <span>🆔 User ID: {user?.id}</span>
              <span>📅 Last login: {new Date().toLocaleDateString()}</span>
            </div>
          </div>
          <div className="hidden md:block">
            <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
              <span className="text-white font-bold text-xl">
                {user?.name?.charAt(0).toUpperCase() || 'A'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statsData.map((stat, index) => (
          <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                <p className={`text-sm ${
                  stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
                }`}>
                  {stat.change} from last month
                </p>
              </div>
              <div className="w-12 h-12 bg-blue-50 rounded-lg flex items-center justify-center">
                <stat.icon className="w-6 h-6 text-blue-600" />
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {quickActions.map((action, index) => (
            <Link
              key={index}
              href={action.href}
              className="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              {...(action.external ? { target: '_blank', rel: 'noopener noreferrer' } : {})}
            >
              <div className={`w-10 h-10 ${action.color} rounded-lg flex items-center justify-center`}>
                <action.icon className="w-5 h-5 text-white" />
              </div>
              <div>
                <h3 className="font-medium text-gray-900">{action.title}</h3>
                <p className="text-sm text-gray-600">{action.description}</p>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
}
