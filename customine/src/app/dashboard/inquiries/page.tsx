'use client';

import React, { useState, useEffect, useCallback } from 'react';
import toast from 'react-hot-toast';
import { useAuth } from '@/context/AuthContext';
import {
  MessageSquare,
  Search,
  Filter,
  Eye,
  Edit,
  Trash2,
  Calendar,
  User,
  Mail,
  Phone,
  MapPin,
  Users,
  DollarSign,
  Clock,
  CheckCircle,
  AlertCircle,
  XCircle,
  Building,
  Gift
} from 'lucide-react';
import { inquiryAPI } from '@/utils/inquiryAPI';
import { SpecialOccasionInquiry } from '../../../services/inquiryApi';

export default function InquiriesPage() {
  const [inquiries, setInquiries] = useState<SpecialOccasionInquiry[]>([]);
  const [filteredInquiries, setFilteredInquiries] = useState<SpecialOccasionInquiry[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [selectedInquiry, setSelectedInquiry] = useState<SpecialOccasionInquiry | null>(null);
  const [editingInquiry, setEditingInquiry] = useState<SpecialOccasionInquiry | null>(null);

  const { user } = useAuth();

  const loadInquiries = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      if (!user?.id) {
        setError('User not authenticated');
        return;
      }

      const result = await inquiryAPI.getUserInquiries(user.id);

      if (result.success) {
        setInquiries(result.data || []);
      } else {
        setError(result.error || 'Failed to load inquiries');
      }
    } catch (err) {
      console.error('Error loading inquiries:', err);
      setError('Failed to load inquiries');
    } finally {
      setLoading(false);
    }
  }, [user?.id]);



  const filterInquiries = useCallback(() => {
    let filtered = inquiries;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(inquiry =>
        inquiry.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        inquiry.firstName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        inquiry.lastName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        inquiry.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        inquiry.subject?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        inquiry.eventType?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        inquiry.giftingType?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        inquiry.location?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        inquiry.eventLocation?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        inquiry.company?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply status filter
    switch (selectedFilter) {
      case 'new':
        filtered = filtered.filter(inquiry => inquiry.status === 'NEW');
        break;
      case 'open':
        filtered = filtered.filter(inquiry => inquiry.status === 'OPEN');
        break;
      case 'in_progress':
        filtered = filtered.filter(inquiry => inquiry.status === 'IN_PROGRESS');
        break;
      case 'resolved':
        filtered = filtered.filter(inquiry => inquiry.status === 'RESOLVED');
        break;
      case 'special_occasion':
        filtered = filtered.filter(inquiry => inquiry.type === 'SPECIAL_OCCASION');
        break;
      case 'unread':
        filtered = filtered.filter(inquiry => !inquiry.isRead);
        break;
    }

    setFilteredInquiries(filtered);
  }, [inquiries, searchTerm, selectedFilter]);

  // useEffect hooks after function definitions
  useEffect(() => {
    if (user?.id) {
      loadInquiries();
    }
  }, [user?.id, loadInquiries]);

  useEffect(() => {
    filterInquiries();
  }, [inquiries, searchTerm, selectedFilter, filterInquiries]);

  const handleMarkAsRead = async (inquiryId: string) => {
    try {
      const result = await inquiryAPI.markAsRead(inquiryId);
      
      if (result.success) {
        await loadInquiries();
      }
    } catch (err) {
      console.error('Error marking as read:', err);
    }
  };

  const handleDeleteInquiry = async (inquiryId: string, inquiryName: string) => {
    if (!confirm(`Are you sure you want to delete inquiry from "${inquiryName}"? This action cannot be undone.`)) {
      return;
    }

    try {
      const result = await inquiryAPI.deleteInquiry(inquiryId);
      
      if (result.success) {
        await loadInquiries();
        toast.success('Inquiry deleted successfully', { icon: '🗑️' });
      } else {
        toast.error('Failed to delete inquiry');
      }
    } catch (err) {
      console.error('Error deleting inquiry:', err);
      toast.error('Failed to delete inquiry');
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'NEW':
        return <AlertCircle className="w-4 h-4 text-blue-600" />;
      case 'OPEN':
        return <Clock className="w-4 h-4 text-yellow-600" />;
      case 'IN_PROGRESS':
        return <Clock className="w-4 h-4 text-orange-600" />;
      case 'RESOLVED':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'CLOSED':
        return <XCircle className="w-4 h-4 text-gray-600" />;
      default:
        return <AlertCircle className="w-4 h-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'NEW':
        return 'bg-blue-100 text-blue-800';
      case 'OPEN':
        return 'bg-yellow-100 text-yellow-800';
      case 'IN_PROGRESS':
        return 'bg-orange-100 text-orange-800';
      case 'RESOLVED':
        return 'bg-green-100 text-green-800';
      case 'CLOSED':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Calculate statistics
  const stats = {
    total: inquiries.length,
    new: inquiries.filter(i => i.status === 'NEW').length,
    inProgress: inquiries.filter(i => i.status === 'IN_PROGRESS').length,
    resolved: inquiries.filter(i => i.status === 'RESOLVED').length,
    unread: inquiries.filter(i => !i.isRead).length
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading inquiries...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Inquiries Management</h1>
          <p className="text-gray-600">Manage customer inquiries and special occasion requests</p>
        </div>
        <div className="flex space-x-3">
          {/* <button
            onClick={fixExistingData}
            disabled={fixingData}
            className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 disabled:opacity-50 flex items-center space-x-2"
          >
            <AlertCircle className="w-4 h-4" />
            <span>{fixingData ? 'Fixing...' : 'Fix Data'}</span>
          </button> */}
          <button
            onClick={loadInquiries}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
          >
            <MessageSquare className="w-4 h-4" />
            <span>Refresh</span>
          </button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6 mb-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <MessageSquare className="w-8 h-8 text-blue-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Inquiries</p>
              <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
            </div>
          </div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <AlertCircle className="w-8 h-8 text-blue-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">New</p>
              <p className="text-2xl font-bold text-blue-600">{stats.new}</p>
            </div>
          </div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <Clock className="w-8 h-8 text-orange-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">In Progress</p>
              <p className="text-2xl font-bold text-orange-600">{stats.inProgress}</p>
            </div>
          </div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <CheckCircle className="w-8 h-8 text-green-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Resolved</p>
              <p className="text-2xl font-bold text-green-600">{stats.resolved}</p>
            </div>
          </div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <Mail className="w-8 h-8 text-red-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Unread</p>
              <p className="text-2xl font-bold text-red-600">{stats.unread}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filter */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 mb-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
          <div className="flex items-center space-x-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search inquiries..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900"
              />
            </div>
            <div className="relative">
              <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <select
                value={selectedFilter}
                onChange={(e) => setSelectedFilter(e.target.value)}
                className="pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none bg-white text-gray-900"
              >
                <option value="all">All Inquiries</option>
                <option value="new">New</option>
                <option value="open">Open</option>
                <option value="in_progress">In Progress</option>
                <option value="resolved">Resolved</option>
                <option value="special_occasion">Special Occasions</option>
                <option value="unread">Unread</option>
              </select>
            </div>
          </div>
          <div className="text-sm text-gray-600">
            Showing {filteredInquiries.length} of {inquiries.length} inquiries
          </div>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      {/* Inquiries Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Customer
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Event Details
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Created
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredInquiries.map((inquiry) => (
                <tr 
                  key={inquiry.id} 
                  className={`hover:bg-gray-50 ${!inquiry.isRead ? 'bg-blue-50' : ''}`}
                  onClick={() => handleMarkAsRead(inquiry.id!)}
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                          <User className="w-5 h-5 text-gray-600" />
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900 flex items-center">
                          {inquiry.name}
                          {!inquiry.isRead && (
                            <span className="ml-2 w-2 h-2 bg-blue-600 rounded-full"></span>
                          )}
                        </div>
                        <div className="text-sm text-gray-500 flex items-center">
                          <Mail className="w-3 h-3 mr-1" />
                          {inquiry.email}
                        </div>
                        {inquiry.phone && (
                          <div className="text-sm text-gray-500 flex items-center">
                            <Phone className="w-3 h-3 mr-1" />
                            {inquiry.phone}
                          </div>
                        )}
                        {inquiry.company && (
                          <div className="text-sm text-gray-500 flex items-center">
                            <Building className="w-3 h-3 mr-1" />
                            {inquiry.company}
                          </div>
                        )}
                        {inquiry.giftingType && (
                          <div className="text-sm text-gray-500 flex items-center">
                            <Gift className="w-3 h-3 mr-1" />
                            {inquiry.giftingType.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                          </div>
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      <div className="font-medium">{inquiry.subject}</div>
                      {inquiry.eventType && (
                        <div className="text-gray-500 flex items-center mt-1">
                          <Calendar className="w-3 h-3 mr-1" />
                          {inquiry.eventType}
                          {inquiry.eventDate && ` - ${new Date(inquiry.eventDate).toLocaleDateString()}`}
                        </div>
                      )}
                      {inquiry.totalGifts && (
                        <div className="text-gray-500 flex items-center">
                          <Gift className="w-3 h-3 mr-1" />
                          {inquiry.totalGifts} gifts
                        </div>
                      )}
                      {inquiry.totalBudget && (
                        <div className="text-gray-500 flex items-center">
                          <DollarSign className="w-3 h-3 mr-1" />
                          {inquiry.totalBudget}
                        </div>
                      )}
                      {inquiry.eventLocation && (
                        <div className="text-gray-500 flex items-center">
                          <MapPin className="w-3 h-3 mr-1" />
                          {inquiry.eventLocation}
                        </div>
                      )}
                      {inquiry.source && (
                        <div className="text-gray-500 flex items-center">
                          <Users className="w-3 h-3 mr-1" />
                          Source: {inquiry.source.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(inquiry.status || 'NEW')}
                      <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(inquiry.status || 'NEW')}`}>
                        {inquiry.status || 'NEW'}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {inquiry.createdAt && formatDate(inquiry.createdAt)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          setSelectedInquiry(inquiry);
                        }}
                        className="text-blue-600 hover:text-blue-900"
                        title="View Details"
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteInquiry(inquiry.id!, inquiry.name);
                        }}
                        className="text-red-600 hover:text-red-900"
                        title="Delete"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredInquiries.length === 0 && (
          <div className="text-center py-12">
            <MessageSquare className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No inquiries found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchTerm || selectedFilter !== 'all' 
                ? 'Try adjusting your search or filter criteria.' 
                : 'No inquiries have been submitted yet.'}
            </p>
          </div>
        )}
      </div>

      {/* View Inquiry Modal */}
      {selectedInquiry && (
        <div className="fixed inset-0 bg-gradient-to-br from-black/60 to-black/80 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
            {/* Modal Header */}
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 px-6 py-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                    <MessageSquare className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h2 className="text-xl font-bold text-white">Inquiry Details</h2>
                    <p className="text-blue-100 text-sm">{selectedInquiry.subject}</p>
                  </div>
                </div>
                <button
                  onClick={() => setSelectedInquiry(null)}
                  className="text-white/80 hover:text-white hover:bg-white/20 rounded-full p-2 transition-all duration-200"
                >
                  <XCircle className="w-5 h-5" />
                </button>
              </div>
            </div>

            {/* Modal Content */}
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-80px)]">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Customer Information */}
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-5 border border-blue-200">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <User className="w-5 h-5 mr-2 text-blue-600" />
                    Customer Information
                  </h3>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center py-2 border-b border-blue-200">
                      <span className="text-sm font-medium text-gray-600">Name:</span>
                      <span className="text-sm text-gray-900 font-medium">{selectedInquiry.name}</span>
                    </div>
                    <div className="flex justify-between items-center py-2 border-b border-blue-200">
                      <span className="text-sm font-medium text-gray-600">Email:</span>
                      <span className="text-sm text-gray-900">{selectedInquiry.email}</span>
                    </div>
                    {selectedInquiry.phone && (
                      <div className="flex justify-between items-center py-2 border-b border-blue-200">
                        <span className="text-sm font-medium text-gray-600">Phone:</span>
                        <span className="text-sm text-gray-900">{selectedInquiry.phone}</span>
                      </div>
                    )}
                    {selectedInquiry.company && (
                      <div className="flex justify-between items-center py-2">
                        <span className="text-sm font-medium text-gray-600">Company:</span>
                        <span className="text-sm text-gray-900">{selectedInquiry.company}</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Event Details */}
                <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-5 border border-green-200">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <Calendar className="w-5 h-5 mr-2 text-green-600" />
                    Event Details
                  </h3>
                  <div className="space-y-3">
                    {selectedInquiry.eventType && (
                      <div className="flex justify-between items-center py-2 border-b border-green-200">
                        <span className="text-sm font-medium text-gray-600">Event Type:</span>
                        <span className="text-sm text-gray-900 font-medium">{selectedInquiry.eventType}</span>
                      </div>
                    )}
                    {selectedInquiry.eventDate && (
                      <div className="flex justify-between items-center py-2 border-b border-green-200">
                        <span className="text-sm font-medium text-gray-600">Event Date:</span>
                        <span className="text-sm text-gray-900">{new Date(selectedInquiry.eventDate).toLocaleDateString()}</span>
                      </div>
                    )}
                    {selectedInquiry.guestCount && (
                      <div className="flex justify-between items-center py-2 border-b border-green-200">
                        <span className="text-sm font-medium text-gray-600">Guest Count:</span>
                        <span className="text-sm text-gray-900">{selectedInquiry.guestCount}</span>
                      </div>
                    )}
                    {selectedInquiry.budget && (
                      <div className="flex justify-between items-center py-2 border-b border-green-200">
                        <span className="text-sm font-medium text-gray-600">Budget:</span>
                        <span className="text-sm text-gray-900">{selectedInquiry.budget}</span>
                      </div>
                    )}
                    {selectedInquiry.location && (
                      <div className="flex justify-between items-center py-2">
                        <span className="text-sm font-medium text-gray-600">Location:</span>
                        <span className="text-sm text-gray-900">{selectedInquiry.location}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Message */}
              <div className="mt-6 bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-5 border border-purple-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <MessageSquare className="w-5 h-5 mr-2 text-purple-600" />
                  Message
                </h3>
                <div className="bg-white rounded-lg p-4 border border-purple-200">
                  <p className="text-gray-900 whitespace-pre-wrap">{selectedInquiry.message}</p>
                </div>
              </div>

              {/* Status Information */}
              <div className="mt-6 bg-gradient-to-r from-orange-50 to-amber-50 rounded-xl p-5 border border-orange-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <AlertCircle className="w-5 h-5 mr-2 text-orange-600" />
                  Status Information
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex justify-between items-center py-2 border-b border-orange-200">
                    <span className="text-sm font-medium text-gray-600">Status:</span>
                    <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(selectedInquiry.status || 'NEW')}`}>
                      {selectedInquiry.status || 'NEW'}
                    </span>
                  </div>
                  <div className="flex justify-between items-center py-2 border-b border-orange-200">
                    <span className="text-sm font-medium text-gray-600">Priority:</span>
                    <span className="text-sm text-gray-900">{selectedInquiry.priority || 'Medium'}</span>
                  </div>
                  <div className="flex justify-between items-center py-2 border-b border-orange-200">
                    <span className="text-sm font-medium text-gray-600">Read:</span>
                    <span className="text-sm text-gray-900">{selectedInquiry.isRead ? 'Yes' : 'No'}</span>
                  </div>
                  <div className="flex justify-between items-center py-2 border-b border-orange-200">
                    <span className="text-sm font-medium text-gray-600">Replied:</span>
                    <span className="text-sm text-gray-900">{selectedInquiry.isReplied ? 'Yes' : 'No'}</span>
                  </div>
                  <div className="flex justify-between items-center py-2">
                    <span className="text-sm font-medium text-gray-600">Created:</span>
                    <span className="text-sm text-gray-900">{selectedInquiry.createdAt && formatDate(selectedInquiry.createdAt)}</span>
                  </div>
                  {selectedInquiry.assignedTo && (
                    <div className="flex justify-between items-center py-2">
                      <span className="text-sm font-medium text-gray-600">Assigned To:</span>
                      <span className="text-sm text-gray-900">{selectedInquiry.assignedTo}</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Admin Notes */}
              {(selectedInquiry.adminNotes || selectedInquiry.internalNotes) && (
                <div className="mt-6 bg-gradient-to-r from-gray-50 to-slate-50 rounded-xl p-5 border border-gray-200">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Admin Notes</h3>
                  {selectedInquiry.adminNotes && (
                    <div className="mb-3">
                      <h4 className="text-sm font-medium text-gray-600 mb-2">Public Notes:</h4>
                      <div className="bg-white rounded-lg p-3 border border-gray-200">
                        <p className="text-gray-900 text-sm">{selectedInquiry.adminNotes}</p>
                      </div>
                    </div>
                  )}
                  {selectedInquiry.internalNotes && (
                    <div>
                      <h4 className="text-sm font-medium text-gray-600 mb-2">Internal Notes:</h4>
                      <div className="bg-white rounded-lg p-3 border border-gray-200">
                        <p className="text-gray-900 text-sm">{selectedInquiry.internalNotes}</p>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Edit Inquiry Modal */}
      {editingInquiry && (
        <div className="fixed inset-0 bg-gradient-to-br from-black/60 to-black/80 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-hidden">
            {/* Modal Header */}
            <div className="bg-gradient-to-r from-green-600 to-blue-600 px-6 py-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                    <Edit className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h2 className="text-xl font-bold text-white">Update Inquiry Status</h2>
                    <p className="text-green-100 text-sm">{editingInquiry.name} - {editingInquiry.subject}</p>
                  </div>
                </div>
                <button
                  onClick={() => setEditingInquiry(null)}
                  className="text-white/80 hover:text-white hover:bg-white/20 rounded-full p-2 transition-all duration-200"
                >
                  <XCircle className="w-5 h-5" />
                </button>
              </div>
            </div>

            {/* Modal Content */}
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
              <div className="space-y-6">
                {/* Status Update */}
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-5 border border-blue-200">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <AlertCircle className="w-5 h-5 mr-2 text-blue-600" />
                    Status Management
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                      <select
                        value={editingInquiry.status || 'NEW'}
                        onChange={(e) => setEditingInquiry({...editingInquiry, status: e.target.value})}
                        className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                      >
                        <option value="NEW">New</option>
                        <option value="OPEN">Open</option>
                        <option value="IN_PROGRESS">In Progress</option>
                        <option value="WAITING_FOR_CUSTOMER">Waiting for Customer</option>
                        <option value="RESOLVED">Resolved</option>
                        <option value="CLOSED">Closed</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Priority</label>
                      <select
                        value={editingInquiry.priority || 'medium'}
                        onChange={(e) => setEditingInquiry({...editingInquiry, priority: e.target.value})}
                        className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                      >
                        <option value="low">Low</option>
                        <option value="medium">Medium</option>
                        <option value="high">High</option>
                        <option value="urgent">Urgent</option>
                      </select>
                    </div>
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-2">Assigned To</label>
                      <input
                        type="text"
                        value={editingInquiry.assignedTo || ''}
                        onChange={(e) => setEditingInquiry({...editingInquiry, assignedTo: e.target.value})}
                        className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                        placeholder="Enter admin name or email"
                      />
                    </div>
                  </div>
                </div>

                {/* Flags */}
                <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-5 border border-green-200">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <CheckCircle className="w-5 h-5 mr-2 text-green-600" />
                    Status Flags
                  </h3>
                  <div className="space-y-4">
                    <label className="flex items-center p-3 bg-white rounded-lg border border-gray-200 hover:bg-gray-50 transition-all duration-200">
                      <input
                        type="checkbox"
                        checked={editingInquiry.isRead || false}
                        onChange={(e) => setEditingInquiry({...editingInquiry, isRead: e.target.checked})}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 w-4 h-4"
                      />
                      <div className="ml-3">
                        <span className="text-sm font-medium text-gray-900">Mark as Read</span>
                        <p className="text-xs text-gray-500">Inquiry has been reviewed</p>
                      </div>
                    </label>
                    <label className="flex items-center p-3 bg-white rounded-lg border border-gray-200 hover:bg-gray-50 transition-all duration-200">
                      <input
                        type="checkbox"
                        checked={editingInquiry.isReplied || false}
                        onChange={(e) => setEditingInquiry({...editingInquiry, isReplied: e.target.checked})}
                        className="rounded border-gray-300 text-green-600 focus:ring-green-500 w-4 h-4"
                      />
                      <div className="ml-3">
                        <span className="text-sm font-medium text-gray-900">Mark as Replied</span>
                        <p className="text-xs text-gray-500">Response has been sent to customer</p>
                      </div>
                    </label>
                  </div>
                </div>

                {/* Notes */}
                <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-5 border border-purple-200">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <MessageSquare className="w-5 h-5 mr-2 text-purple-600" />
                    Admin Notes
                  </h3>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Public Notes (visible to customer)</label>
                      <textarea
                        value={editingInquiry.adminNotes || ''}
                        onChange={(e) => setEditingInquiry({...editingInquiry, adminNotes: e.target.value})}
                        rows={3}
                        className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                        placeholder="Notes that will be visible to the customer..."
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Internal Notes (admin only)</label>
                      <textarea
                        value={editingInquiry.internalNotes || ''}
                        onChange={(e) => setEditingInquiry({...editingInquiry, internalNotes: e.target.value})}
                        rows={3}
                        className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                        placeholder="Internal notes for admin team..."
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 mt-6">
                <button
                  onClick={() => setEditingInquiry(null)}
                  className="px-6 py-3 text-gray-700 bg-gray-100 rounded-xl hover:bg-gray-200 transition-all duration-200 font-medium"
                >
                  Cancel
                </button>
                <button
                  onClick={async () => {
                    try {
                      const result = await inquiryAPI.updateInquiryStatus(editingInquiry.id!, {
                        status: editingInquiry.status,
                        priority: editingInquiry.priority,
                        assignedTo: editingInquiry.assignedTo,
                        isRead: editingInquiry.isRead,
                        isReplied: editingInquiry.isReplied,
                        adminNotes: editingInquiry.adminNotes,
                        internalNotes: editingInquiry.internalNotes
                      });

                      if (result.success) {
                        await loadInquiries();
                        setEditingInquiry(null);
                        toast.success('Inquiry updated successfully', { icon: '✅' });
                      } else {
                        toast.error('Failed to update inquiry');
                      }
                    } catch (err) {
                      console.error('Error updating inquiry:', err);
                      toast.error('Failed to update inquiry');
                    }
                  }}
                  className="px-6 py-3 bg-gradient-to-r from-green-600 to-blue-600 text-white rounded-xl hover:from-green-700 hover:to-blue-700 transition-all duration-200 flex items-center space-x-2 font-medium shadow-lg"
                >
                  <CheckCircle className="w-4 h-4" />
                  <span>Update Inquiry</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
