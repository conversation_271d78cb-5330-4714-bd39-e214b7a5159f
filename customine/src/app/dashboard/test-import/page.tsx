'use client';

import React, { useState } from 'react';
import { Play, CheckCircle, XCircle, RefreshCw } from 'lucide-react';
import { addSampleProducts } from '../../../scripts/addSampleProducts';

interface ImportResult {
  success: boolean;
  imported: number;
  failed: number;
  errors: string[];
}

export default function TestImportPage() {
  const [isImporting, setIsImporting] = useState(false);
  const [importResult, setImportResult] = useState<ImportResult | null>(null);

  const handleTestImport = async () => {
    setIsImporting(true);
    setImportResult(null);
    
    try {
      const result = await addSampleProducts();
      setImportResult(result);
    } catch (error) {
      console.error('Test import failed:', error);
      setImportResult({
        success: false,
        imported: 0,
        failed: 1,
        errors: [error instanceof Error ? error.message : 'Unknown error occurred']
      });
    } finally {
      setIsImporting(false);
    }
  };

  const resetTest = () => {
    setImportResult(null);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-3xl mx-auto">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex items-center space-x-3 mb-4">
            <Play className="w-8 h-8 text-green-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Test Product Import</h1>
              <p className="text-gray-600">Test the import functionality with 3 sample products</p>
            </div>
          </div>
          
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
              <div>
                <h3 className="font-medium text-green-900">Test Import</h3>
                <p className="text-sm text-green-700 mt-1">
                  This will import 3 sample products (Creative Canvas, Green Thumb Haven, Home Sweet Nest) 
                  to test the import functionality before running the full bulk import.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Test Section */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Import Test</h2>
          
          {!importResult && (
            <div className="text-center py-8">
              <Play className="w-16 h-16 text-green-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Ready to Test Import</h3>
              <p className="text-gray-600 mb-6">
                Click the button below to test importing 3 sample products.
              </p>
              <button
                onClick={handleTestImport}
                disabled={isImporting}
                className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 mx-auto"
              >
                {isImporting && <RefreshCw className="w-4 h-4 animate-spin" />}
                <span>{isImporting ? 'Testing Import...' : 'Start Test Import'}</span>
              </button>
            </div>
          )}

          {isImporting && (
            <div className="text-center py-8">
              <RefreshCw className="w-16 h-16 text-green-600 mx-auto mb-4 animate-spin" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Testing Import...</h3>
              <p className="text-gray-600">
                Please wait while we test the import with sample products.
              </p>
            </div>
          )}

          {importResult && (
            <div className="py-8">
              <div className="text-center mb-6">
                {importResult.success ? (
                  <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-4" />
                ) : (
                  <XCircle className="w-16 h-16 text-red-600 mx-auto mb-4" />
                )}
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {importResult.success ? 'Test Import Successful!' : 'Test Import Failed'}
                </h3>
              </div>

              {/* Import Statistics */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center space-x-3">
                    <CheckCircle className="w-8 h-8 text-green-600" />
                    <div>
                      <p className="text-sm font-medium text-green-900">Successfully Imported</p>
                      <p className="text-2xl font-bold text-green-600">{importResult.imported}</p>
                    </div>
                  </div>
                </div>
                
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <div className="flex items-center space-x-3">
                    <XCircle className="w-8 h-8 text-red-600" />
                    <div>
                      <p className="text-sm font-medium text-red-900">Failed to Import</p>
                      <p className="text-2xl font-bold text-red-600">{importResult.failed}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Error Details */}
              {importResult.errors.length > 0 && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                  <h4 className="font-medium text-red-900 mb-2">Import Errors:</h4>
                  <ul className="text-sm text-red-700 space-y-1">
                    {importResult.errors.map((error, index) => (
                      <li key={index} className="flex items-start space-x-2">
                        <span className="text-red-500">•</span>
                        <span>{error}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Success Message */}
              {importResult.success && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                  <h4 className="font-medium text-green-900 mb-2">✅ Test Successful!</h4>
                  <p className="text-sm text-green-700">
                    The import functionality is working correctly. You can now proceed with the full bulk import 
                    if you want to import all 20 products.
                  </p>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex justify-center space-x-4">
                <button
                  onClick={resetTest}
                  className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  Test Again
                </button>
                <a
                  href="/dashboard/products"
                  className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                >
                  View Products
                </a>
                {importResult.success && (
                  <a
                    href="/dashboard/bulk-import"
                    className="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors"
                  >
                    Full Bulk Import
                  </a>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Sample Products Preview */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mt-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Sample Products for Testing</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[
              { name: "Creative Canvas", category: "Creative", price: "₹2,499", sku: "CUS-0001" },
              { name: "Green Thumb Haven", category: "Gardening", price: "₹1,899", sku: "CUS-0002" },
              { name: "Home Sweet Nest", category: "Home", price: "₹2,199", sku: "CUS-0003" }
            ].map((product, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <h3 className="font-medium text-gray-900">{product.name}</h3>
                <p className="text-sm text-gray-600">{product.category}</p>
                <p className="text-sm font-semibold text-blue-600">{product.price}</p>
                <p className="text-xs text-gray-500">SKU: {product.sku}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
