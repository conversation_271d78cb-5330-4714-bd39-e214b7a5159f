'use client';

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { useAuth } from '../../../context/AuthContext';
import { 
  Package, 
  Eye, 
  Calendar, 
  CreditCard, 
  MapPin,
  ArrowLeft,
  RefreshCw,
  Search,
  Filter,
  Download
} from 'lucide-react';
import { useUserOrders, getOrderStatusColor, getPaymentStatusColor, getOrderStatusText, OrderTrackingData } from '../../../hooks/useOrderTracking';

export default function OrdersDashboard() {
  const { user, isLoggedIn } = useAuth();
  const { loading, error, orders, fetchUserOrders, refreshOrders } = useUserOrders();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('ALL');
  const [filteredOrders, setFilteredOrders] = useState<OrderTrackingData[]>([]);

  useEffect(() => {
    if (user?.id) {
      fetchUserOrders(user.id);
    }
  }, [fetchUserOrders, user?.id]);

  useEffect(() => {
    let filtered = orders;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(order => 
        order.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.items.some(item => item.name.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Filter by status
    if (statusFilter !== 'ALL') {
      filtered = filtered.filter(order => order.status === statusFilter);
    }

    setFilteredOrders(filtered);
  }, [orders, searchTerm, statusFilter]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const handleRefresh = () => {
    if (user?.id) {
      refreshOrders(user.id);
    }
  };

  const getOrderSummary = () => {
    const total = orders.length;
    const delivered = orders.filter(order => order.status === 'DELIVERED').length;
    const pending = orders.filter(order => ['PENDING', 'CONFIRMED', 'PROCESSING'].includes(order.status)).length;
    const shipped = orders.filter(order => ['SHIPPED', 'OUT_FOR_DELIVERY'].includes(order.status)).length;

    return { total, delivered, pending, shipped };
  };

  const summary = getOrderSummary();

  // Show login prompt if user is not logged in
  if (!isLoggedIn || !user?.id) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md mx-auto text-center">
          <div className="bg-white rounded-lg shadow-sm p-8">
            <Package className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Login Required</h2>
            <p className="text-gray-600 mb-6">
              Please log in to view your order history and track your orders.
            </p>
            <div className="space-y-3">
              <Link
                href="/login"
                className="block w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Log In
              </Link>
              <Link
                href="/"
                className="block w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
              >
                Back to Home
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Enhanced Header */}
      <div className="bg-white shadow-lg border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link
                href="/"
                className="inline-flex items-center text-slate-600 hover:text-slate-900 transition-colors group"
              >
                <ArrowLeft className="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform" />
                Back to Home
              </Link>
              <div className="h-6 border-l border-gray-300"></div>
              <div>
                <h1 className="text-3xl font-bold text-slate-900 flex items-center">
                  <Package className="w-8 h-8 mr-3 text-blue-600" />
                  My Orders
                </h1>
                <p className="text-slate-600 font-medium mt-1">Track and view your personal orders</p>
              </div>
            </div>
            <button
              onClick={handleRefresh}
              disabled={loading}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 disabled:opacity-50 shadow-sm hover:shadow-md transition-all"
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
     

        {/* Enhanced Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200 hover:shadow-md transition-shadow">
            <div className="flex items-center">
              <div className="p-3 bg-blue-50 rounded-lg border border-blue-100">
                <Package className="w-6 h-6 text-blue-700" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-semibold text-slate-700">Total Orders</p>
                <p className="text-2xl font-bold text-slate-900">{summary.total}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200 hover:shadow-md transition-shadow">
            <div className="flex items-center">
              <div className="p-3 bg-amber-50 rounded-lg border border-amber-100">
                <Calendar className="w-6 h-6 text-amber-700" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-semibold text-slate-700">Pending</p>
                <p className="text-2xl font-bold text-amber-700">{summary.pending}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200 hover:shadow-md transition-shadow">
            <div className="flex items-center">
              <div className="p-3 bg-violet-50 rounded-lg border border-violet-100">
                <Package className="w-6 h-6 text-violet-700" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-semibold text-slate-700">Shipped</p>
                <p className="text-2xl font-bold text-violet-700">{summary.shipped}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200 hover:shadow-md transition-shadow">
            <div className="flex items-center">
              <div className="p-3 bg-emerald-50 rounded-lg border border-emerald-100">
                <Package className="w-6 h-6 text-emerald-700" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-semibold text-slate-700">Delivered</p>
                <p className="text-2xl font-bold text-emerald-700">{summary.delivered}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Filters */}
        <div className="bg-white rounded-xl shadow-sm p-6 mb-6 border border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-bold text-slate-900">Search & Filter</h3>
            <button
              onClick={() => {
                setSearchTerm('');
                setStatusFilter('ALL');
              }}
              className="text-sm text-blue-700 hover:text-blue-900 font-semibold bg-blue-50 px-3 py-1 rounded-lg hover:bg-blue-100 transition-colors"
            >
              Clear All
            </button>
          </div>
          <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
            {/* Search */}
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-500" />
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search orders by number or item name..."
                className="w-full pl-10 pr-4 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-slate-800 font-medium placeholder-slate-400 bg-white"
              />
            </div>

            {/* Status Filter */}
            <div className="relative">
              <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-500" />
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="pl-10 pr-8 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none bg-white text-slate-800 font-medium"
              >
                <option value="ALL" className="text-slate-700">All Status</option>
                <option value="PENDING" className="text-slate-700">Pending</option>
                <option value="CONFIRMED" className="text-slate-700">Confirmed</option>
                <option value="PROCESSING" className="text-slate-700">Processing</option>
                <option value="PACKED" className="text-slate-700">Packed</option>
                <option value="SHIPPED" className="text-slate-700">Shipped</option>
                <option value="OUT_FOR_DELIVERY" className="text-slate-700">Out for Delivery</option>
                <option value="DELIVERED" className="text-slate-700">Delivered</option>
                <option value="CANCELLED" className="text-slate-700">Cancelled</option>
              </select>
            </div>
          </div>
        </div>

        {/* Enhanced Orders List */}
        {loading && orders.length === 0 ? (
          <div className="bg-white rounded-xl shadow-sm p-12 text-center border border-gray-200">
            <div className="w-10 h-10 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-slate-700 font-semibold">Loading your orders...</p>
            <p className="text-slate-500 text-sm mt-1">Please wait while we fetch your data</p>
          </div>
        ) : error ? (
          <div className="bg-red-50 border border-red-200 rounded-xl p-6">
            <p className="text-red-700 font-semibold">{error}</p>
          </div>
        ) : filteredOrders.length === 0 ? (
          <div className="bg-white rounded-xl shadow-sm p-12 text-center border border-gray-200">
            <Package className="w-16 h-16 text-slate-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-slate-900 mb-2">No orders found</h3>
            <p className="text-slate-600 font-medium mb-6">
              {orders.length === 0
                ? "You haven't placed any orders yet."
                : "No orders match your current filters."
              }
            </p>
            <Link
              href="/collections"
              className="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 shadow-sm hover:shadow-md transition-all"
            >
              Start Shopping
            </Link>
          </div>
        ) : (
          <div className="space-y-4">
            {filteredOrders.map((order) => (
              <div key={order.id} className="bg-white rounded-xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-shadow duration-300">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h3 className="text-lg font-bold text-slate-900">Order #{order.orderNumber}</h3>
                    <p className="text-slate-600 font-medium">Placed on {formatDate(order.createdAt)}</p>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold ${getOrderStatusColor(order.status)}`}>
                      {getOrderStatusText(order.status)}
                    </div>
                    <Link
                      href={`/dashboard/track-order?orderNumber=${order.orderNumber}`}
                      className="inline-flex items-center px-4 py-2 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 shadow-sm hover:shadow-md transition-all"
                    >
                      <Eye className="w-4 h-4 mr-2" />
                      Track
                    </Link>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {/* Order Items */}
                  <div className="md:col-span-2">
                    <h4 className="font-semibold text-slate-900 mb-3">Items ({order.items.length})</h4>
                    <div className="space-y-3">
                      {order.items.slice(0, 3).map((item, index) => (
                        <div key={index} className="flex items-center justify-between text-sm bg-slate-50 p-3 rounded-lg">
                          <span className="text-slate-700 font-medium">{item.name} × {item.quantity}</span>
                          <span className="font-semibold text-emerald-700">₹{item.price.toLocaleString()}</span>
                        </div>
                      ))}
                      {order.items.length > 3 && (
                        <p className="text-sm text-slate-600 font-medium bg-slate-100 p-2 rounded text-center">+{order.items.length - 3} more items</p>
                      )}
                    </div>
                  </div>

                  {/* Order Summary */}
                  <div className="bg-slate-50 p-4 rounded-lg">
                    <div className="space-y-3">
                      <div className="flex items-center justify-between border-b border-slate-200 pb-2">
                        <span className="text-slate-700 font-semibold">Total Amount</span>
                        <span className="font-bold text-xl text-emerald-700">₹{order.total.toLocaleString()}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <CreditCard className="w-4 h-4 text-slate-500" />
                        <span className={`text-sm px-3 py-1 rounded-full font-semibold ${getPaymentStatusColor(order.paymentStatus)}`}>
                          {order.paymentStatus}
                        </span>
                      </div>
                      {order.trackingNumber && (
                        <div className="flex items-center space-x-2">
                          <Package className="w-4 h-4 text-slate-500" />
                          <span className="text-sm text-slate-700 font-medium">Tracking: <span className="font-mono text-blue-700 bg-blue-50 px-2 py-1 rounded">{order.trackingNumber}</span></span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
