'use client';

import React, { useState, useEffect, useCallback } from 'react';
import toast from 'react-hot-toast';
import { Search, Filter, Eye, Edit, Trash2, Mail, Phone, UserPlus, Loader2 } from 'lucide-react';
import { adminApi } from '../../../services/adminApi';

interface Customer {
  id: string;
  name: string;
  email: string;
  phone: string;
  address?: string;
  totalOrders: number;
  totalSpent: number;
  lastOrderDate?: string;
  isActive: boolean;
  emailVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

export default function CustomersPage() {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  // Modal states for future implementation
  // const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  // const [showCustomerModal, setShowCustomerModal] = useState(false);
  // const [showEditModal, setShowEditModal] = useState(false);

  const fetchCustomers = useCallback(async () => {
    try {
      setLoading(true);

      // Try to fetch from API, but fall back to static data if it fails
      const response = await adminApi.getCustomers({
        page: currentPage,
        limit: 10,
        search: searchTerm || undefined,
      });

      if (response.success && response.data) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        setCustomers(response.data.customers as any);
        setTotalPages(response.data.pagination?.pages || 1);
        setTotalCount(response.data.pagination?.total || 0);
        setLoading(false);
        return;
      } else {
        console.warn('Customers API failed, using static data. Error:', response.error);
      }

      // Fallback to static data
      const staticCustomers: Customer[] = [
        {
          id: '1',
          name: 'Arumuganainar Petchi',
          email: '<EMAIL>',
          phone: '09751564773',
          address: '123 Main Street, Chennai, Tamil Nadu, 600001',
          totalOrders: 5,
          totalSpent: 12495,
          lastOrderDate: '2025-01-11T10:30:00Z',
          isActive: true,
          emailVerified: true,
          createdAt: '2024-12-01T10:00:00Z',
          updatedAt: '2025-01-11T10:30:00Z'
        },
        {
          id: '2',
          name: 'Test User',
          email: '<EMAIL>',
          phone: '1234567890',
          address: '456 Oak Avenue, Mumbai, Maharashtra, 400001',
          totalOrders: 2,
          totalSpent: 4398,
          lastOrderDate: '2025-01-10T15:20:00Z',
          isActive: true,
          emailVerified: false,
          createdAt: '2024-11-15T09:30:00Z',
          updatedAt: '2025-01-10T15:20:00Z'
        },
        {
          id: '3',
          name: 'John Doe',
          email: '<EMAIL>',
          phone: '9876543210',
          address: '789 Pine Road, Bangalore, Karnataka, 560001',
          totalOrders: 8,
          totalSpent: 18750,
          lastOrderDate: '2025-01-09T12:45:00Z',
          isActive: true,
          emailVerified: true,
          createdAt: '2024-10-20T14:15:00Z',
          updatedAt: '2025-01-09T12:45:00Z'
        },
        {
          id: '4',
          name: 'Jane Smith',
          email: '<EMAIL>',
          phone: '8765432109',
          address: '321 Elm Street, Delhi, Delhi, 110001',
          totalOrders: 0,
          totalSpent: 0,
          isActive: false,
          emailVerified: false,
          createdAt: '2024-12-20T16:30:00Z',
          updatedAt: '2024-12-20T16:30:00Z'
        }
      ];

      // Apply filters to static data
      let filteredCustomers = staticCustomers;

      if (searchTerm) {
        filteredCustomers = filteredCustomers.filter(customer =>
          customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
          customer.phone.includes(searchTerm)
        );
      }

      if (selectedFilter) {
        switch (selectedFilter) {
          case 'active':
            filteredCustomers = filteredCustomers.filter(c => c.isActive);
            break;
          case 'inactive':
            filteredCustomers = filteredCustomers.filter(c => !c.isActive);
            break;
          case 'verified':
            filteredCustomers = filteredCustomers.filter(c => c.emailVerified);
            break;
          case 'unverified':
            filteredCustomers = filteredCustomers.filter(c => !c.emailVerified);
            break;
          case 'high-value':
            filteredCustomers = filteredCustomers.filter(c => c.totalSpent > 10000);
            break;
        }
      }

      setCustomers(filteredCustomers);
      setTotalPages(1);
      setTotalCount(filteredCustomers.length);

    } catch (err) {
      console.error('Error fetching customers:', err);
      setError('Failed to load customers');
    } finally {
      setLoading(false);
    }
  }, [currentPage, searchTerm, selectedFilter]);

  useEffect(() => {
    fetchCustomers();
  }, [fetchCustomers]);

  const handleDeleteCustomer = async (id: string) => {
    // Show confirmation toast
    toast((t) => (
      <div className="flex flex-col space-y-2">
        <span>Are you sure you want to delete this customer?</span>
        <div className="flex space-x-2">
          <button
            onClick={() => {
              toast.dismiss(t.id);
              performDelete(id);
            }}
            className="bg-red-600 text-white px-3 py-1 rounded text-sm"
          >
            Delete
          </button>
          <button
            onClick={() => toast.dismiss(t.id)}
            className="bg-gray-300 text-gray-700 px-3 py-1 rounded text-sm"
          >
            Cancel
          </button>
        </div>
      </div>
    ), {
      duration: 10000,
      icon: '⚠️',
    });
  };

  const performDelete = async (id: string) => {
    try {
      const loadingToast = toast.loading('Deleting customer...', { icon: '🗑️' });

      // In production, you'd call: await adminApi.deleteCustomer(id);
      console.log('Would delete customer:', id);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));

      toast.dismiss(loadingToast);
      toast.success('Customer deleted successfully', { icon: '✅' });

      fetchCustomers(); // Refresh the list
    } catch (err) {
      console.error('Error deleting customer:', err);
      toast.error('Failed to delete customer');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const openCustomerModal = (customer: Customer) => {
    // TODO: Implement customer view modal
    console.log('View customer:', customer);
  };

  const openEditModal = (customer: Customer) => {
    // TODO: Implement customer edit modal
    console.log('Edit customer:', customer);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Loading customers...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={fetchCustomers}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Customers</h1>
          <p className="text-gray-600">Manage customer accounts and information</p>
        </div>
        <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2">
          <UserPlus className="w-4 h-4" />
          <span>Add Customer</span>
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <UserPlus className="w-6 h-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Customers</p>
              <p className="text-2xl font-bold text-gray-900">{totalCount}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <Mail className="w-6 h-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Verified</p>
              <p className="text-2xl font-bold text-gray-900">
                {customers.filter(c => c.emailVerified).length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Phone className="w-6 h-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active</p>
              <p className="text-2xl font-bold text-gray-900">
                {customers.filter(c => c.isActive).length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <UserPlus className="w-6 h-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">High Value</p>
              <p className="text-2xl font-bold text-gray-900">
                {customers.filter(c => c.totalSpent > 10000).length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4">
          {/* Search */}
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search customers..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Filter */}
          <div className="flex items-center space-x-2">
            <Filter className="w-4 h-4 text-gray-400" />
            <select
              value={selectedFilter}
              onChange={(e) => setSelectedFilter(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">All Customers</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="verified">Email Verified</option>
              <option value="unverified">Email Unverified</option>
              <option value="high-value">High Value (₹10k+)</option>
            </select>
          </div>
        </div>
      </div>

      {/* Customers Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="text-left py-3 px-6 font-medium text-gray-600">Customer</th>
                <th className="text-left py-3 px-6 font-medium text-gray-600">Contact</th>
                <th className="text-left py-3 px-6 font-medium text-gray-600">Orders</th>
                <th className="text-left py-3 px-6 font-medium text-gray-600">Total Spent</th>
                <th className="text-left py-3 px-6 font-medium text-gray-600">Status</th>
                <th className="text-left py-3 px-6 font-medium text-gray-600">Joined</th>
                <th className="text-left py-3 px-6 font-medium text-gray-600">Actions</th>
              </tr>
            </thead>
            <tbody>
              {customers.map((customer) => (
                <tr key={customer.id} className="border-b border-gray-100 hover:bg-gray-50">
                  <td className="py-4 px-6">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                        <span className="text-blue-600 font-medium">
                          {customer.name.charAt(0).toUpperCase()}
                        </span>
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">{customer.name}</p>
                        <div className="flex items-center space-x-2">
                          {customer.emailVerified && (
                            <span className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                              Verified
                            </span>
                          )}
                          {!customer.isActive && (
                            <span className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800">
                              Inactive
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="py-4 px-6">
                    <div>
                      <p className="text-gray-900">{customer.email}</p>
                      <p className="text-sm text-gray-500">{customer.phone}</p>
                    </div>
                  </td>
                  <td className="py-4 px-6 text-gray-600">
                    {customer.totalOrders} orders
                  </td>
                  <td className="py-4 px-6 font-medium text-gray-900">
                    ₹{customer.totalSpent.toLocaleString()}
                  </td>
                  <td className="py-4 px-6">
                    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                      customer.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {customer.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td className="py-4 px-6 text-gray-600">
                    {formatDate(customer.createdAt)}
                  </td>
                  <td className="py-4 px-6">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => openCustomerModal(customer)}
                        className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                        title="View Customer"
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => openEditModal(customer)}
                        className="p-1 text-gray-400 hover:text-green-600 transition-colors"
                        title="Edit Customer"
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteCustomer(customer.id)}
                        className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                        title="Delete Customer"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
              {customers.length === 0 && (
                <tr>
                  <td colSpan={7} className="py-8 px-6 text-center text-gray-500">
                    No customers found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between">
        <p className="text-sm text-gray-600">
          Showing {customers.length} of {totalCount} customers
        </p>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
            className="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Previous
          </button>

          {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
            const page = i + 1;
            return (
              <button
                key={page}
                onClick={() => setCurrentPage(page)}
                className={`px-3 py-1 rounded text-sm ${
                  currentPage === page
                    ? 'bg-blue-600 text-white'
                    : 'border border-gray-300 hover:bg-gray-50'
                }`}
              >
                {page}
              </button>
            );
          })}

          <button
            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages}
            className="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Next
          </button>
        </div>
      </div>
    </div>
  );
}
