/* eslint-disable @next/next/no-img-element */
'use client';

import React, { useState, useEffect } from 'react';
import { Upload, X, Edit, Save, Plus, Trash2, Loader2, Crown } from 'lucide-react';
import { Amplify } from 'aws-amplify';
import { getCurrentUser } from 'aws-amplify/auth';
import { generateClient } from 'aws-amplify/api';
import { uploadData, getUrl } from 'aws-amplify/storage';
import amplifyconfig from '../../../../amplifyconfiguration.json';

// Import GraphQL operations
import { listGalleries } from '../../../../graphql/queries';
import { createGallery, updateGallery, deleteGallery } from '../../../../graphql/mutations';

// Import AWS configuration
import { generateS3Url } from '../../../../config/aws';

// Configure Amplify
Amplify.configure(amplifyconfig);
const client = generateClient();

interface GalleryImage {
  id: string;
  title: string;
  description?: string;
  imageKey: string;
  imageUrl?: string;
  category: 'HOME' | 'LUXURY' | 'CORPORATE' | 'WEDDING' | 'FESTIVAL' | 'PERSONAL';
  subcategory?: string;
  tags?: string[];
  isActive: boolean;
  sortOrder?: number;
  altText?: string;
  metaTitle?: string;
  metaDescription?: string;
  createdBy?: string;
  updatedBy?: string;
  createdAt?: string;
  updatedAt?: string;
}

interface CurrentUser {
  userId: string;
  username: string;
}

// Luxury card subcategories
const LUXURY_SUBCATEGORIES = [
  { value: 'ALL', label: 'All Categories' },
  { value: 'ACRYLIC_CARDS', label: 'Acrylic Cards' },
  { value: 'LASERCUT_CARDS', label: 'Lasercut Cards' },
  { value: 'BOXED_CARDS', label: 'Boxed Cards' },
  { value: 'ECO_FRIENDLY_CARDS', label: 'Eco-Friendly Cards' },
  { value: 'HANDMADE_ARTISAN_CARDS', label: 'Handmade or Artisan Cards' },
  { value: 'TRANSPARENT_GLASS_CARDS', label: 'Transparent or Glass-Finish Cards' },
  { value: 'THEME_BASED_CARDS', label: 'Theme-Based Cards' },
];

export default function LuxuryGalleryPage() {
  const [images, setImages] = useState<GalleryImage[]>([]);
  const [filteredImages, setFilteredImages] = useState<GalleryImage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [newImage, setNewImage] = useState<File | null>(null);
  const [newImagePreview, setNewImagePreview] = useState<string>('');
  const [uploading, setUploading] = useState(false);
  const [currentUser, setCurrentUser] = useState<CurrentUser | null>(null);
  const [selectedSubcategory, setSelectedSubcategory] = useState<string>('ALL');
  const [newImageData, setNewImageData] = useState({
    title: '',
    description: '',
    altText: '',
    subcategory: '',
    tags: [] as string[],
  });

  useEffect(() => {
    initializeUser();
  }, []);

  // Filter images based on selected subcategory
  useEffect(() => {
    if (selectedSubcategory === 'ALL') {
      setFilteredImages(images);
    } else {
      const filtered = images.filter(image => image.subcategory === selectedSubcategory);
      setFilteredImages(filtered);
    }
  }, [images, selectedSubcategory]);

  const initializeUser = async () => {
    try {
      const user = await getCurrentUser();
      setCurrentUser({
        userId: user.userId,
        username: user.username || user.userId
      });
      fetchGalleryItems();
    } catch (error) {
      console.error('Error getting current user:', error);
      setError('Authentication required');
    }
  };

  const fetchGalleryItems = async () => {
    try {
      setLoading(true);
      setError(null);

      const result = await client.graphql({
        query: listGalleries,
        variables: {
          filter: {
            category: { eq: 'LUXURY' }
          }
        }
      });

      if (result.data?.listGalleries?.items) {
        const galleryItems = result.data.listGalleries.items;

        // Get image URLs from S3 for each item
        const imagesWithUrls = await Promise.all(
          galleryItems.map(async (item: any) => {
            let imageUrl = '';
            if (item.imageKey) {
              try {
                // Use Amplify getUrl for signed URLs (works with current auth setup)
                const urlResult = await getUrl({ key: item.imageKey });
                imageUrl = urlResult.url.toString();
                console.log('Generated signed URL for', item.imageKey, ':', imageUrl);
              } catch (urlError) {
                console.error('Error getting signed URL:', urlError);
                // Fallback to direct S3 URL
                imageUrl = generateS3Url(item.imageKey);
                console.log('Fallback to direct URL:', imageUrl);
              }
            }

            return {
              id: item.id,
              title: item.title,
              description: item.description || '',
              imageKey: item.imageKey,
              imageUrl,
              category: item.category,
              subcategory: item.subcategory || '',
              tags: item.tags || [],
              isActive: item.isActive !== false,
              sortOrder: item.sortOrder || 0,
              altText: item.altText || '',
              metaTitle: item.metaTitle || '',
              metaDescription: item.metaDescription || '',
              createdBy: item.createdBy,
              updatedBy: item.updatedBy,
              createdAt: item.createdAt,
              updatedAt: item.updatedAt,
            } as GalleryImage;
          })
        );

        // Sort by sortOrder
        imagesWithUrls.sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));
        setImages(imagesWithUrls);
      } else {
        setImages([]);
      }
    } catch (err: any) {
      console.error('Error fetching gallery items:', err);
      setError(`Failed to load gallery items: ${err.message || 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (id: string) => {
    setEditingId(id);
  };

  const handleSave = async (id: string, updatedData: Partial<GalleryImage>) => {
    try {
      const result = await client.graphql({
        query: updateGallery,
        variables: {
          input: {
            id,
            ...updatedData,
            updatedBy: currentUser?.userId
          }
        }
      });

      if (result.data?.updateGallery) {
        setImages(prev => prev.map(img =>
          img.id === id ? { ...img, ...updatedData } : img
        ));
        setEditingId(null);
      }
    } catch (error: any) {
      console.error('Error updating gallery item:', error);
      alert(`Failed to update image: ${error.message}`);
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this luxury image?')) {
      return;
    }

    try {
      await client.graphql({
        query: deleteGallery,
        variables: {
          input: { id }
        }
      });

      setImages(prev => prev.filter(img => img.id !== id));
    } catch (error: any) {
      console.error('Error deleting gallery item:', error);
      alert(`Failed to delete image: ${error.message}`);
    }
  };

  const handleToggleActive = async (id: string) => {
    const image = images.find(img => img.id === id);
    if (!image) return;

    try {
      const result = await client.graphql({
        query: updateGallery,
        variables: {
          input: {
            id,
            isActive: !image.isActive,
            updatedBy: currentUser?.userId
          }
        }
      });

      if (result.data?.updateGallery) {
        setImages(prev => prev.map(img =>
          img.id === id ? { ...img, isActive: !img.isActive } : img
        ));
      }
    } catch (error: any) {
      console.error('Error toggling active status:', error);
      alert(`Failed to update status: ${error.message}`);
    }
  };



  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setNewImage(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setNewImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleAddImage = async () => {
    if (!newImage || !newImageData.title || !currentUser) {
      alert('Please select an image and enter a title');
      return;
    }

    try {
      setUploading(true);

      // Upload image to S3
      const fileExtension = newImage.name.split('.').pop();
      const fileName = `gallery/luxury/${Date.now()}-${Math.random().toString(36).substring(7)}.${fileExtension}`;

      const uploadResult = await uploadData({
        key: fileName,
        data: newImage,
        options: {
          contentType: newImage.type
        }
      }).result;

      // Create gallery item in DynamoDB
      const maxSortOrder = Math.max(...images.map(img => img.sortOrder || 0), 0);

      const result = await client.graphql({
        query: createGallery,
        variables: {
          input: {
            title: newImageData.title,
            description: newImageData.description || undefined,
            imageKey: uploadResult.key,
            category: 'LUXURY',
            subcategory: newImageData.subcategory || undefined,
            tags: newImageData.tags.length > 0 ? newImageData.tags : undefined,
            isActive: true,
            sortOrder: maxSortOrder + 1,
            altText: newImageData.altText || undefined,
            createdBy: currentUser.userId
          }
        }
      });

      if (result.data?.createGallery) {
        // Refresh the list
        await fetchGalleryItems();

        // Reset form
        setNewImage(null);
        setNewImagePreview('');
        setNewImageData({
          title: '',
          description: '',
          altText: '',
          subcategory: '',
          tags: []
        });
      }
    } catch (error: any) {
      console.error('Error adding image:', error);
      alert(`Failed to add image: ${error.message}`);
    } finally {
      setUploading(false);
    }
  };

  const moveImage = async (id: string, direction: 'up' | 'down') => {
    const currentIndex = images.findIndex(img => img.id === id);
    if (
      (direction === 'up' && currentIndex === 0) ||
      (direction === 'down' && currentIndex === images.length - 1)
    ) {
      return;
    }

    const newImages = [...images];
    const targetIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;

    // Swap the images
    [newImages[currentIndex], newImages[targetIndex]] = [newImages[targetIndex], newImages[currentIndex]];

    // Update sort orders in database
    try {
      await Promise.all([
        client.graphql({
          query: updateGallery,
          variables: {
            input: {
              id: newImages[currentIndex].id,
              sortOrder: currentIndex,
              updatedBy: currentUser?.userId
            }
          }
        }),
        client.graphql({
          query: updateGallery,
          variables: {
            input: {
              id: newImages[targetIndex].id,
              sortOrder: targetIndex,
              updatedBy: currentUser?.userId
            }
          }
        })
      ]);

      // Update local state
      newImages[currentIndex].sortOrder = currentIndex;
      newImages[targetIndex].sortOrder = targetIndex;
      setImages(newImages);
    } catch (error) {
      console.error('Error updating sort order:', error);
      alert('Failed to update image order');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-purple-600" />
          <p className="text-gray-600">Loading luxury gallery...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6">
            <h2 className="text-lg font-semibold text-red-800 mb-2">Error Loading Gallery</h2>
            <p className="text-red-600">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="mt-4 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900 flex items-center space-x-2">
          <Crown className="w-8 h-8 text-yellow-600" />
          <span>Luxury Gallery Management</span>
        </h1>
        <p className="text-gray-600">Manage luxury collection images displayed on the website</p>
      </div>

      {/* Subcategory Filter */}
      <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-bold text-slate-900">Filter by Category</h3>
          <span className="text-sm text-slate-600 font-medium">
            Showing: {filteredImages.length} of {images.length} images
          </span>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-3">
          {LUXURY_SUBCATEGORIES.map((category) => (
            <button
              key={category.value}
              onClick={() => setSelectedSubcategory(category.value)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                selectedSubcategory === category.value
                  ? 'bg-blue-600 text-white shadow-md'
                  : 'bg-slate-100 text-slate-700 hover:bg-slate-200 hover:text-slate-900'
              }`}
            >
              {category.label}
            </button>
          ))}
        </div>
      </div>

      {/* Add New Image */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center space-x-2">
          <Crown className="w-5 h-5 text-purple-600" />
          <span>Add New Luxury Image</span>
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Image Upload */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Upload Image</label>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              {newImagePreview ? (
                <div className="relative">
                  <img
                    src={newImagePreview}
                    alt="Preview"
                    className="w-full h-48 object-cover rounded-lg"
                  />
                  <button
                    onClick={() => {
                      setNewImage(null);
                      setNewImagePreview('');
                    }}
                    className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              ) : (
                <>
                  <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-600 mb-2">Upload luxury image</p>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="hidden"
                    id="luxury-image-upload"
                  />
                  <label
                    htmlFor="luxury-image-upload"
                    className="inline-block bg-purple-600 text-white px-4 py-2 rounded-lg cursor-pointer hover:bg-purple-700"
                  >
                    Choose File
                  </label>
                </>
              )}
            </div>
          </div>

          {/* Image Details */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Title *</label>
              <input
                type="text"
                value={newImageData.title}
                onChange={(e) => setNewImageData(prev => ({ ...prev, title: e.target.value }))}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white placeholder-gray-500 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                placeholder="Enter luxury image title"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
              <textarea
                value={newImageData.description}
                onChange={(e) => setNewImageData(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white placeholder-gray-500 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                placeholder="Enter luxury image description"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Card Category</label>
              <select
                value={newImageData.subcategory}
                onChange={(e) => setNewImageData(prev => ({ ...prev, subcategory: e.target.value }))}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              >
                <option value="">Select card category (optional)</option>
                {LUXURY_SUBCATEGORIES.slice(1).map((category) => (
                  <option key={category.value} value={category.value}>
                    {category.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Alt Text</label>
              <input
                type="text"
                value={newImageData.altText}
                onChange={(e) => setNewImageData(prev => ({ ...prev, altText: e.target.value }))}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white placeholder-gray-500 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                placeholder="Alt text for accessibility"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Tags (comma-separated)</label>
              <input
                type="text"
                value={newImageData.tags.join(', ')}
                onChange={(e) => setNewImageData(prev => ({
                  ...prev,
                  tags: e.target.value.split(',').map(tag => tag.trim()).filter(tag => tag)
                }))}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white placeholder-gray-500 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                placeholder="luxury, premium, exclusive"
              />
            </div>

            <div className="flex space-x-4">
              <button
                onClick={handleAddImage}
                disabled={uploading || !newImage || !newImageData.title}
                className="flex-1 bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
              >
                {uploading ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin" />
                    <span>Uploading...</span>
                  </>
                ) : (
                  <>
                    <Plus className="w-4 h-4" />
                    <span>Add Luxury Image</span>
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Current Luxury Images */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          Current Luxury Images
          {selectedSubcategory !== 'ALL' && (
            <span className="ml-2 text-sm font-normal text-slate-600">
              - {LUXURY_SUBCATEGORIES.find(cat => cat.value === selectedSubcategory)?.label}
            </span>
          )}
        </h2>

        {filteredImages.length === 0 ? (
          <div className="text-center py-12">
            <Crown className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            {images.length === 0 ? (
              <>
                <p className="text-gray-500 text-lg">No luxury images yet</p>
                <p className="text-gray-400">Add your first luxury image above</p>
              </>
            ) : (
              <>
                <p className="text-gray-500 text-lg">No images in this category</p>
                <p className="text-gray-400">Try selecting a different category or add new images</p>
              </>
            )}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredImages.map((image, index) => (
              <div key={image.id} className="border border-gray-200 rounded-lg overflow-hidden bg-gradient-to-br from-purple-50 to-pink-50">
                <div className="relative">
                  <img
                    src={image.imageUrl || '/placeholder-image.jpg'}
                    alt={image.altText || image.title}
                    className="w-full h-48 object-cover"
                    onError={(e) => {
                      console.error('Image failed to load:', image.imageUrl);
                      console.error('Image key:', image.imageKey);
                      e.currentTarget.src = '/placeholder-image.jpg';
                    }}
                    onLoad={() => {
                      console.log('Image loaded successfully:', image.imageUrl);
                    }}
                  />

                  {/* Order Controls */}
                  <div className="absolute top-2 right-2 flex space-x-1">
                    <button
                      onClick={() => moveImage(image.id, 'up')}
                      disabled={images.findIndex(img => img.id === image.id) === 0}
                      className="bg-black bg-opacity-50 text-white p-1 rounded disabled:opacity-50 disabled:cursor-not-allowed"
                      title="Move up"
                    >
                      ↑
                    </button>
                    <button
                      onClick={() => moveImage(image.id, 'down')}
                      disabled={images.findIndex(img => img.id === image.id) === images.length - 1}
                      className="bg-black bg-opacity-50 text-white p-1 rounded disabled:opacity-50 disabled:cursor-not-allowed"
                      title="Move down"
                    >
                      ↓
                    </button>
                  </div>

                  {/* Order Number */}
                  <div className="absolute top-2 left-2">
                    <span className="bg-purple-600 text-white px-2 py-1 rounded text-xs font-medium">
                      #{images.findIndex(img => img.id === image.id) + 1}
                    </span>
                    {selectedSubcategory !== 'ALL' && (
                      <span className="bg-blue-600 text-white px-2 py-1 rounded text-xs font-medium ml-1">
                        F#{index + 1}
                      </span>
                    )}
                  </div>
                </div>

                <div className="p-4">
                  {editingId === image.id ? (
                    <EditImageForm
                      image={image}
                      onSave={(data) => handleSave(image.id, data)}
                      onCancel={() => setEditingId(null)}
                    />
                  ) : (
                    <>
                      <div className="mb-3">
                        <h3 className="font-bold text-gray-900 text-lg">{image.title}</h3>
                        <p className="text-sm text-gray-600 mt-1">{image.description}</p>
                        {image.subcategory && (
                          <p className="text-xs text-blue-600 mt-1 font-medium bg-blue-50 px-2 py-1 rounded">
                            📂 {LUXURY_SUBCATEGORIES.find(cat => cat.value === image.subcategory)?.label || image.subcategory}
                          </p>
                        )}

                        {image.tags && image.tags.length > 0 && (
                          <div className="flex flex-wrap gap-1 mt-2">
                            {image.tags.map((tag, tagIndex) => (
                              <span
                                key={tagIndex}
                                className="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded"
                              >
                                {tag}
                              </span>
                            ))}
                          </div>
                        )}
                      </div>

                      <div className="flex items-center justify-between">
                        <button
                          onClick={() => handleToggleActive(image.id)}
                          className={`px-2 py-1 text-xs rounded ${
                            image.isActive
                              ? 'bg-green-100 text-green-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}
                        >
                          {image.isActive ? 'Active' : 'Inactive'}
                        </button>

                        <div className="flex items-center space-x-1">
                          <button
                            onClick={() => handleEdit(image.id)}
                            className="p-1 text-gray-400 hover:text-purple-600"
                          >
                            <Edit className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleDelete(image.id)}
                            className="p-1 text-gray-400 hover:text-red-600"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

function EditImageForm({
  image,
  onSave,
  onCancel
}: {
  image: GalleryImage;
  onSave: (data: Partial<GalleryImage>) => void;
  onCancel: () => void;
}) {
  // Access to luxury subcategories within the component
  const luxurySubcategories = LUXURY_SUBCATEGORIES;
  const [formData, setFormData] = useState({
    title: image.title,
    description: image.description || '',
    altText: image.altText || '',
    subcategory: image.subcategory || '',
    tags: image.tags || [],
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-3">
      <div>
        <label className="block text-xs font-medium text-gray-700 mb-1">Title *</label>
        <input
          type="text"
          value={formData.title}
          onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
          className="w-full border border-gray-300 rounded px-2 py-1 text-sm text-gray-900 bg-white placeholder-gray-500 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          placeholder="Enter image title"
          required
        />
      </div>
      <div>
        <label className="block text-xs font-medium text-gray-700 mb-1">Description</label>
        <textarea
          value={formData.description}
          onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
          rows={2}
          className="w-full border border-gray-300 rounded px-2 py-1 text-sm text-gray-900 bg-white placeholder-gray-500 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          placeholder="Enter description"
        />
      </div>
      <div>
        <label className="block text-xs font-medium text-gray-700 mb-1">Card Category</label>
        <select
          value={formData.subcategory}
          onChange={(e) => setFormData(prev => ({ ...prev, subcategory: e.target.value }))}
          className="w-full border border-gray-300 rounded px-2 py-1 text-sm text-gray-900 bg-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
        >
          <option value="">Select card category (optional)</option>
          {luxurySubcategories.slice(1).map((category) => (
            <option key={category.value} value={category.value}>
              {category.label}
            </option>
          ))}
        </select>

      </div>
      <div>
        <label className="block text-xs font-medium text-gray-700 mb-1">Alt Text</label>
        <input
          type="text"
          value={formData.altText}
          onChange={(e) => setFormData(prev => ({ ...prev, altText: e.target.value }))}
          className="w-full border border-gray-300 rounded px-2 py-1 text-sm text-gray-900 bg-white placeholder-gray-500 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          placeholder="Alt text for accessibility"
        />
      </div>
      <div>
        <label className="block text-xs font-medium text-gray-700 mb-1">Tags</label>
        <input
          type="text"
          value={formData.tags.join(', ')}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            tags: e.target.value.split(',').map(tag => tag.trim()).filter(tag => tag)
          }))}
          className="w-full border border-gray-300 rounded px-2 py-1 text-sm text-gray-900 bg-white placeholder-gray-500 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          placeholder="Tags (comma-separated)"
        />
      </div>
      <div className="flex space-x-2">
        <button
          type="submit"
          className="flex-1 bg-purple-600 text-white px-2 py-1 rounded text-sm hover:bg-purple-700"
        >
          <Save className="w-3 h-3 inline mr-1" />
          Save
        </button>
        <button
          type="button"
          onClick={onCancel}
          className="flex-1 bg-gray-300 text-gray-700 px-2 py-1 rounded text-sm hover:bg-gray-400"
        >
          Cancel
        </button>
      </div>
    </form>
  );
}
