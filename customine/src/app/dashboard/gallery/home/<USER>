/* eslint-disable @next/next/no-img-element */
'use client';

import React, { useState, useEffect } from 'react';
import { Upload, X, Edit, Save, Plus, Trash2, Loader2, Eye, EyeOff, ArrowUp, ArrowDown } from 'lucide-react';
import { Amplify } from 'aws-amplify';
import { getCurrentUser } from 'aws-amplify/auth';
import { generateClient } from 'aws-amplify/api';
import { uploadData, getUrl } from 'aws-amplify/storage';
import amplifyconfig from '../../../../amplifyconfiguration.json';

// Import GraphQL operations
import { listGalleries } from '../../../../graphql/queries';
import { createGallery, updateGallery, deleteGallery } from '../../../../graphql/mutations';

// Import AWS configuration
import { generateS3Url } from '../../../../config/aws';

// Configure Amplify
Amplify.configure(amplifyconfig);
const client = generateClient();

interface GalleryImage {
  id: string;
  title: string;
  description?: string;
  imageKey: string;
  imageUrl?: string;
  category: 'HOME' | 'LUXURY' | 'CORPORATE' | 'WEDDING' | 'FESTIVAL' | 'PERSONAL';
  subcategory?: string;
  tags?: string[];
  isActive: boolean;
  sortOrder?: number;
  altText?: string;
  metaTitle?: string;
  metaDescription?: string;
  createdBy?: string;
  updatedBy?: string;
  createdAt?: string;
  updatedAt?: string;
}

interface CurrentUser {
  userId: string;
  username: string;
}

export default function HomeGalleryPage() {
  const [images, setImages] = useState<GalleryImage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [newImage, setNewImage] = useState<File | null>(null);
  const [newImagePreview, setNewImagePreview] = useState<string>('');
  const [uploading, setUploading] = useState(false);
  const [currentUser, setCurrentUser] = useState<CurrentUser | null>(null);
  const [newImageData, setNewImageData] = useState({
    title: '',
    description: '',
    altText: '',
    tags: [] as string[],
  });

  useEffect(() => {
    initializeUser();
  }, []);

  const initializeUser = async () => {
    try {
      const user = await getCurrentUser();
      setCurrentUser({
        userId: user.userId,
        username: user.username || user.userId
      });
      fetchGalleryItems();
    } catch (error) {
      console.error('Error getting current user:', error);
      setError('Authentication required');
    }
  };

  const fetchGalleryItems = async () => {
    try {
      setLoading(true);
      setError(null);

      const result = await client.graphql({
        query: listGalleries,
        variables: {
          filter: {
            category: { eq: 'HOME' }
          }
        }
      });

      if (result.data?.listGalleries?.items) {
        const galleryItems = result.data.listGalleries.items;

        // Get image URLs from S3 for each item
        const imagesWithUrls = await Promise.all(
          galleryItems.map(async (item: any) => {
            let imageUrl = '';
            if (item.imageKey) {
              try {
                // Use Amplify getUrl for signed URLs (works with current auth setup)
                const urlResult = await getUrl({ key: item.imageKey });
                imageUrl = urlResult.url.toString();
                console.log('Generated signed URL for', item.imageKey, ':', imageUrl);
              } catch (urlError) {
                console.error('Error getting signed URL:', urlError);
                // Fallback to direct S3 URL
                imageUrl = generateS3Url(item.imageKey);
                console.log('Fallback to direct URL:', imageUrl);
              }
            }

            return {
              id: item.id,
              title: item.title,
              description: item.description || '',
              imageKey: item.imageKey,
              imageUrl,
              category: item.category,
              tags: item.tags || [],
              isActive: item.isActive !== false,
              sortOrder: item.sortOrder || 0,
              altText: item.altText || '',
              metaTitle: item.metaTitle || '',
              metaDescription: item.metaDescription || '',
              createdBy: item.createdBy,
              updatedBy: item.updatedBy,
              createdAt: item.createdAt,
              updatedAt: item.updatedAt,
            } as GalleryImage;
          })
        );

        // Sort by sortOrder
        imagesWithUrls.sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));
        setImages(imagesWithUrls);
      } else {
        setImages([]);
      }
    } catch (err: any) {
      console.error('Error fetching gallery items:', err);
      setError(`Failed to load gallery items: ${err.message || 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (id: string) => {
    setEditingId(id);
  };

  const handleSave = async (id: string, updatedData: Partial<GalleryImage>) => {
    try {
      const result = await client.graphql({
        query: updateGallery,
        variables: {
          input: {
            id,
            ...updatedData,
            updatedBy: currentUser?.userId
          }
        }
      });

      if (result.data?.updateGallery) {
        setImages(prev => prev.map(img =>
          img.id === id ? { ...img, ...updatedData } : img
        ));
        setEditingId(null);
      }
    } catch (error: any) {
      console.error('Error updating gallery item:', error);
      alert(`Failed to update image: ${error.message}`);
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this image?')) {
      return;
    }

    try {
      await client.graphql({
        query: deleteGallery,
        variables: {
          input: { id }
        }
      });

      setImages(prev => prev.filter(img => img.id !== id));
    } catch (error: any) {
      console.error('Error deleting gallery item:', error);
      alert(`Failed to delete image: ${error.message}`);
    }
  };

  const handleToggleActive = async (id: string) => {
    const image = images.find(img => img.id === id);
    if (!image) return;

    try {
      const result = await client.graphql({
        query: updateGallery,
        variables: {
          input: {
            id,
            isActive: !image.isActive,
            updatedBy: currentUser?.userId
          }
        }
      });

      if (result.data?.updateGallery) {
        setImages(prev => prev.map(img =>
          img.id === id ? { ...img, isActive: !img.isActive } : img
        ));
      }
    } catch (error: any) {
      console.error('Error toggling active status:', error);
      alert(`Failed to update status: ${error.message}`);
    }
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setNewImage(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setNewImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleAddImage = async () => {
    if (!newImage || !newImageData.title || !currentUser) {
      alert('Please select an image and enter a title');
      return;
    }

    try {
      setUploading(true);

      // Upload image to S3
      const fileExtension = newImage.name.split('.').pop();
      const fileName = `gallery/home/<USER>

      const uploadResult = await uploadData({
        key: fileName,
        data: newImage,
        options: {
          contentType: newImage.type
        }
      }).result;

      // Create gallery item in DynamoDB
      const maxSortOrder = Math.max(...images.map(img => img.sortOrder || 0), 0);

      const result = await client.graphql({
        query: createGallery,
        variables: {
          input: {
            title: newImageData.title,
            description: newImageData.description || undefined,
            imageKey: uploadResult.key,
            category: 'HOME',
            tags: newImageData.tags.length > 0 ? newImageData.tags : undefined,
            isActive: true,
            sortOrder: maxSortOrder + 1,
            altText: newImageData.altText || undefined,
            createdBy: currentUser.userId
          }
        }
      });

      if (result.data?.createGallery) {
        // Refresh the list
        await fetchGalleryItems();

        // Reset form
        setNewImage(null);
        setNewImagePreview('');
        setNewImageData({
          title: '',
          description: '',
          altText: '',
          tags: []
        });
      }
    } catch (error: any) {
      console.error('Error adding image:', error);
      alert(`Failed to add image: ${error.message}`);
    } finally {
      setUploading(false);
    }
  };

  const moveImage = async (id: string, direction: 'up' | 'down') => {
    const currentIndex = images.findIndex(img => img.id === id);
    if (
      (direction === 'up' && currentIndex === 0) ||
      (direction === 'down' && currentIndex === images.length - 1)
    ) {
      return;
    }

    const newImages = [...images];
    const targetIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;

    [newImages[currentIndex], newImages[targetIndex]] = [newImages[targetIndex], newImages[currentIndex]];

    // Update order numbers
    newImages.forEach((img, index) => {
      img.sortOrder = index + 1;
    });

    setImages(newImages);

    // Update the database with new sort orders
    try {
      await Promise.all([
        client.graphql({
          query: updateGallery,
          variables: {
            input: {
              id: newImages[currentIndex].id,
              sortOrder: newImages[currentIndex].sortOrder,
              updatedBy: currentUser?.userId
            }
          }
        }),
        client.graphql({
          query: updateGallery,
          variables: {
            input: {
              id: newImages[targetIndex].id,
              sortOrder: newImages[targetIndex].sortOrder,
              updatedBy: currentUser?.userId
            }
          }
        })
      ]);
    } catch (error: any) {
      console.error('Error updating sort order:', error);
      // Revert the change if database update fails
      fetchGalleryItems();
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Loading gallery...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={fetchGalleryItems}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Home Gallery Management</h1>
        <p className="text-gray-600">Manage images displayed on the home page</p>
      </div>

      {/* Status Information */}
      {images.length > 0 && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <p className="text-sm text-green-700">
            ✅ {images.length} image{images.length !== 1 ? 's' : ''} loaded successfully
          </p>
        </div>
      )}

      {/* Add New Image */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Add New Image</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Image Upload */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Upload Image</label>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              {newImagePreview ? (
                <div className="relative">
                  <img
                    src={newImagePreview}
                    alt="Preview"
                    className="w-full h-48 object-cover rounded-lg"
                  />
                  <button
                    onClick={() => {
                      setNewImage(null);
                      setNewImagePreview('');
                    }}
                    className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              ) : (
                <>
                  <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-600 mb-2">Click to upload image</p>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="hidden"
                    id="new-image-upload"
                  />
                  <label
                    htmlFor="new-image-upload"
                    className="inline-block bg-blue-600 text-white px-4 py-2 rounded-lg cursor-pointer hover:bg-blue-700"
                  >
                    Choose File
                  </label>
                </>
              )}
            </div>
          </div>

          {/* Image Details */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Title *</label>
              <input
                type="text"
                value={newImageData.title}
                onChange={(e) => setNewImageData(prev => ({ ...prev, title: e.target.value }))}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter image title"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
              <textarea
                value={newImageData.description}
                onChange={(e) => setNewImageData(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter image description"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Alt Text</label>
              <input
                type="text"
                value={newImageData.altText}
                onChange={(e) => setNewImageData(prev => ({ ...prev, altText: e.target.value }))}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Alt text for accessibility"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Tags (comma separated)</label>
              <input
                type="text"
                value={newImageData.tags.join(', ')}
                onChange={(e) => setNewImageData(prev => ({
                  ...prev,
                  tags: e.target.value.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0)
                }))}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter tags separated by commas"
              />
            </div>
            <button
              onClick={handleAddImage}
              disabled={uploading || !newImage || !newImageData.title}
              className="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {uploading ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>Uploading...</span>
                </>
              ) : (
                <>
                  <Plus className="w-4 h-4" />
                  <span>Add Image</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Current Images */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Current Gallery Images</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {images.map((image) => (
            <div key={image.id} className="border border-gray-200 rounded-lg overflow-hidden">
              <div className="relative">
                <img
                  src={image.imageUrl || '/placeholder-image.jpg'}
                  alt={image.altText || image.title}
                  className="w-full h-48 object-cover"
                  onError={(e) => {
                    console.error('Image failed to load:', image.imageUrl);
                    console.error('Image key:', image.imageKey);
                    e.currentTarget.src = '/placeholder-image.jpg';
                  }}
                  onLoad={() => {
                    console.log('Image loaded successfully:', image.imageUrl);
                  }}
                />
                <div className="absolute top-2 right-2 flex space-x-1">
                  <button
                    onClick={() => moveImage(image.id, 'up')}
                    className="bg-black bg-opacity-50 text-white p-1 rounded hover:bg-opacity-70"
                    title="Move up"
                  >
                    <ArrowUp className="w-3 h-3" />
                  </button>
                  <button
                    onClick={() => moveImage(image.id, 'down')}
                    className="bg-black bg-opacity-50 text-white p-1 rounded hover:bg-opacity-70"
                    title="Move down"
                  >
                    <ArrowDown className="w-3 h-3" />
                  </button>
                </div>
                <div className="absolute top-2 left-2">
                  <span className="bg-blue-600 text-white px-2 py-1 rounded text-xs">
                    #{image.sortOrder || 0}
                  </span>
                </div>
                {!image.isActive && (
                  <div className="absolute inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center">
                    <span className="text-white font-semibold">INACTIVE</span>
                  </div>
                )}
              </div>
              
              <div className="p-4">
                {editingId === image.id ? (
                  <EditImageForm
                    image={image}
                    onSave={(data) => handleSave(image.id, data)}
                    onCancel={() => setEditingId(null)}
                  />
                ) : (
                  <>
                    <h3 className="font-semibold text-gray-900 mb-1">{image.title}</h3>
                    <p className="text-sm text-gray-600 mb-2">{image.description}</p>

                    {/* Tags */}
                    {image.tags && image.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1 mb-3">
                        {image.tags.map((tag, index) => (
                          <span
                            key={index}
                            className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                    )}

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleToggleActive(image.id)}
                          className={`px-2 py-1 text-xs rounded ${
                            image.isActive
                              ? 'bg-green-100 text-green-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}
                        >
                          {image.isActive ? 'Active' : 'Inactive'}
                        </button>
                      </div>

                      <div className="flex items-center space-x-1">
                        <button
                          onClick={() => handleEdit(image.id)}
                          className="p-1 text-gray-400 hover:text-blue-600"
                          title="Edit"
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleDelete(image.id)}
                          className="p-1 text-gray-400 hover:text-red-600"
                          title="Delete"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

function EditImageForm({
  image,
  onSave,
  onCancel
}: {
  image: GalleryImage;
  onSave: (data: Partial<GalleryImage>) => void;
  onCancel: () => void;
}) {
  const [title, setTitle] = useState(image.title);
  const [description, setDescription] = useState(image.description || '');
  const [altText, setAltText] = useState(image.altText || '');
  const [tags, setTags] = useState(image.tags?.join(', ') || '');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave({
      title,
      description: description || undefined,
      altText: altText || undefined,
      tags: tags ? tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0) : undefined
    });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-3">
      <input
        type="text"
        value={title}
        onChange={(e) => setTitle(e.target.value)}
        className="w-full border border-gray-300 rounded px-2 py-1 text-sm text-gray-900 bg-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        placeholder="Title"
        required
      />
      <textarea
        value={description}
        onChange={(e) => setDescription(e.target.value)}
        rows={2}
        className="w-full border border-gray-300 rounded px-2 py-1 text-sm text-gray-900 bg-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        placeholder="Description"
      />
      <input
        type="text"
        value={altText}
        onChange={(e) => setAltText(e.target.value)}
        className="w-full border border-gray-300 rounded px-2 py-1 text-sm text-gray-900 bg-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        placeholder="Alt text for accessibility"
      />
      <input
        type="text"
        value={tags}
        onChange={(e) => setTags(e.target.value)}
        className="w-full border border-gray-300 rounded px-2 py-1 text-sm text-gray-900 bg-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        placeholder="Tags (comma separated)"
      />
      <div className="flex space-x-2">
        <button
          type="submit"
          className="flex-1 bg-blue-600 text-white px-2 py-1 rounded text-sm hover:bg-blue-700 flex items-center justify-center"
        >
          <Save className="w-3 h-3 mr-1" />
          Save
        </button>
        <button
          type="button"
          onClick={onCancel}
          className="flex-1 bg-gray-300 text-gray-700 px-2 py-1 rounded text-sm hover:bg-gray-400"
        >
          Cancel
        </button>
      </div>
    </form>
  );
}
