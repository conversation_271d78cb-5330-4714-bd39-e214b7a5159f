
'use client';

import React, { useState, useEffect, Suspense } from 'react';
import toast from 'react-hot-toast';
import { ArrowLeft, Upload, X, Loader2 } from 'lucide-react';
import Link from 'next/link';
import { adminApi } from '../../../../services/adminApi';
import { uploadData, getUrl } from 'aws-amplify/storage';
import { useSearchParams } from 'next/navigation';
import { Product } from '@/types/product';

import { cleanupProductImages } from '../../../../utils/imageCleanup';

// Common input styles for better readability
const inputStyles = "w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent";
const flexInputStyles = "flex-1 border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent";
const selectStyles = "w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent";

function AddProductPageContent() {
  // const router = useRouter(); // Removed unused variable
  const searchParams = useSearchParams();
  const productId = searchParams.get('id');
  const [loadingProduct, setLoadingProduct] = useState(false);
  const [originalProduct, setOriginalProduct] = useState<Product | null>(null);

  const [formData, setFormData] = useState({
    name: '',
    slug: '',
    description: '',
    shortDescription: '',
    narration: '',
    luxuryDescription: '',
    budgetDescription: '',
    price: '',
    originalPrice: '',
    category: '',
    subcategory: '',
    sku: '',
    stockQuantity: '',
    tags: [''],
    weight: '',
    dimensions: '',
    metaTitle: '',
    metaDescription: '',
    isFeatured: false,
    isActive: true,
    inStock: true,
    sortOrder: '',
    // Product features
    features: [''],
    luxuryFeatures: [''],
    budgetFeatures: [''],
    // Product specifications
    specifications: [{ key: '', value: '' }],
    materials: [''],
    careInstructions: '',
    warranty: '',
    // Shipping and return
    shippingInfo: '',
    returnPolicy: '',
    // Rating and additional fields
    rating: '',
    reviewCount: '',
    badge: '',
    relatedProductIds: [''],
  });

  const [images, setImages] = useState<File[]>([]);
  const [imagePreviews, setImagePreviews] = useState<string[]>([]);

  // Fetch product for edit mode
  useEffect(() => {
    if (productId) {
      setLoadingProduct(true);
      console.log('Loading product for edit, ID:', productId);

      adminApi.getProduct(productId).then(res => {
        console.log('Product API response:', res);

        if (res.success && res.data) {
          const rawProduct = res.data as Product;
          console.log('Raw product data:', rawProduct);

          // Clean up malformed image URLs
          const product = cleanupProductImages(rawProduct);
          console.log('Product loaded and images cleaned:', {
            originalImages: rawProduct.images,
            cleanedImages: product.images
          });

          // Store original product data for later use
          setOriginalProduct(product);
          setFormData({
            name: product.name || '',
            slug: product.slug || '',
            description: product.description || '',
            shortDescription: product.shortDescription || '',
            narration: product.narration || '',
            luxuryDescription: product.luxuryDescription || '',
            budgetDescription: product.budgetDescription || '',
            price: product.price?.toString() || '',
            originalPrice: product.originalPrice?.toString() || '',
            category: product.category || '',
            subcategory: product.subcategory || '',
            sku: product.sku || '',
            stockQuantity: product.stockQuantity?.toString() || '',
            tags: product.tags || [''],
            weight: product.weight?.toString() || '',
            dimensions: product.dimensions || '',
            metaTitle: product.metaTitle || '',
            metaDescription: product.metaDescription || '',
            isFeatured: product.isFeatured ?? false,
            isActive: product.isActive ?? true,
            inStock: product.inStock ?? true,
            sortOrder: product.sortOrder?.toString() || '',
            features: product.features || [''],
            luxuryFeatures: product.luxuryFeatures || [''],
            budgetFeatures: product.budgetFeatures || [''],
            specifications: product.specifications
              ? (() => {
                  try {
                    // Parse JSON string from AWSJSON field
                    const specsObj = typeof product.specifications === 'string'
                      ? JSON.parse(product.specifications)
                      : product.specifications;
                    return Object.entries(specsObj).map(([key, value]) => ({
                      key,
                      value: typeof value === 'string' ? value : JSON.stringify(value)
                    }));
                  } catch (error) {
                    console.error('Error parsing specifications:', error);
                    return [{ key: '', value: '' }];
                  }
                })()
              : [{ key: '', value: '' }],
            materials: product.materials || [''],
            careInstructions: product.careInstructions || '',
            warranty: product.warranty || '',
            shippingInfo: product.shippingInfo || '',
            returnPolicy: product.returnPolicy || '',
            rating: product.rating?.toString() || '',
            reviewCount: product.reviewCount?.toString() || '',
            badge: product.badge || '',
            relatedProductIds: product.relatedProductIds || [''],
          });
          console.log('Loading product images for edit:', product.images);

          // Handle S3 images - convert keys to fresh URLs
          const processImages = async () => {
            if (product.images && product.images.length > 0) {
              console.log('Original product images from database:', product.images);

              try {
                const imageUrls = await Promise.all(
                  product.images.map(async (imageData, index) => {
                    console.log(`Processing image ${index + 1}:`, imageData);

                    // If it's a relative path, use it as is
                    if (imageData.startsWith('/')) {
                      console.log(`Image ${index + 1} is a relative path`);
                      return imageData;
                    }

                    // If it's an S3 key (starts with products/ or contains products/)
                    if (imageData.startsWith('products/') || imageData.includes('products/')) {
                      console.log(`Image ${index + 1} is an S3 key, generating fresh URL...`);
                      try {
                        // Use Amplify getUrl for signed URLs (same approach as gallery pages)
                        const urlResult = await getUrl({ key: imageData });
                        const urlString = urlResult.url.toString();
                        console.log(`Image ${index + 1} fresh URL generated`);
                        return urlString;
                      } catch (error) {
                        console.error(`Error generating URL for S3 key ${index + 1}:`, error);
                        return '/placeholder-product.png';
                      }
                    }

                    // If it's already a full URL (legacy data), try to extract key and refresh
                    if (imageData.startsWith('http')) {
                      console.log(`Image ${index + 1} is a URL, trying to extract S3 key...`);
                      try {
                        // Extract key from S3 URL pattern
                        const urlParts = imageData.split('/');
                        const keyIndex = urlParts.findIndex(part => part === 'products');
                        if (keyIndex !== -1 && keyIndex < urlParts.length - 1) {
                          const key = urlParts.slice(keyIndex).join('/');
                          console.log(`Extracted S3 key: ${key}, generating fresh URL...`);

                          const urlResult = await getUrl({ key });
                          const refreshedUrl = urlResult.url.toString();
                          console.log(`Image ${index + 1} URL refreshed from extracted key`);
                          return refreshedUrl;
                        }
                      } catch (refreshError) {
                        console.warn(`Could not refresh URL for image ${index + 1}:`, refreshError);
                      }

                      // If we can't extract key, use original URL as fallback
                      return imageData;
                    }

                    // For any other format, use placeholder
                    console.log(`Image ${index + 1} format not recognized, using placeholder`);
                    return '/placeholder-product.png';
                  })
                );

                console.log('Final processed image URLs:', imageUrls.length);
                setImagePreviews(imageUrls);
              } catch (error) {
                console.error('Error processing images:', error);
                // Fallback to placeholder images
                const placeholderUrls = product.images.map(() => '/placeholder-product.png');
                setImagePreviews(placeholderUrls);
              }
            }
          };

          processImages();
          setImages([]); // No File[] for edit mode
          setLoadingProduct(false);
        } else {
          console.error('Failed to load product:', res.error || 'Unknown error');
          alert('Failed to load product for editing: ' + (res.error || 'Unknown error'));
          setLoadingProduct(false);
        }
      }).catch(error => {
        console.error('Error loading product:', error);
        alert('Error loading product: ' + error.message);
        setLoadingProduct(false);
      });
    }
  }, [productId]);

  // Autofill function with sample product data for testing
  const autofillTestData = () => {
    setFormData({
      name: 'Creative Canvas',
      slug: 'creative-canvas',
      description: 'Perfect art gift set for creative souls',
      shortDescription: 'A beautiful art gift set for creative people.',
      narration: 'A story about creativity and gifting.',
      luxuryDescription: 'Luxury version with premium art supplies.',
      budgetDescription: 'Budget version with essential art supplies.',
      price: '2499',
      originalPrice: '2999',
      category: 'Art & Craft',
      subcategory: 'Painting',
      sku: 'ART-001',
      stockQuantity: '15',
      tags: ['art', 'canvas', 'gift'],
      weight: '1.5',
      dimensions: '30cm x 20cm x 10cm',
      metaTitle: 'Creative Canvas - Art Gift Set',
      metaDescription: 'Perfect art gift set for creative souls',
      isFeatured: true,
      isActive: true,
      inStock: true,
      sortOrder: '1',
      features: ['Premium art supplies', 'Canvas included'],
      luxuryFeatures: ['Gold brush', 'Premium canvas'],
      budgetFeatures: ['Basic brush'],
      specifications: [
        { key: 'Material', value: 'Canvas, Wood' },
        { key: 'Color', value: 'Multicolor' }
      ],
      materials: ['Canvas', 'Wood'],
      careInstructions: 'Keep dry and clean.',
      warranty: '6 months',
      shippingInfo: 'Ships in 2 days',
      returnPolicy: '7-day return',
      rating: '4.8',
      reviewCount: '25',
      badge: 'Best Seller',
      relatedProductIds: [''],
    });
    setImages([]); // User must upload manually
    setImagePreviews(['/Creative_Canvas.png']); // Show preview if image exists in public folder
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
      
      // Auto-generate slug from name
      if (name === 'name') {
        const slug = value.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');
        setFormData(prev => ({ ...prev, slug }));
      }
    }
  };

  // Helper functions for managing array fields
  const addArrayItem = (fieldName: string) => {
    setFormData(prev => ({
      ...prev,
      [fieldName]: [...(prev[fieldName as keyof typeof prev] as string[]), '']
    }));
  };

  const removeArrayItem = (fieldName: string, index: number) => {
    setFormData(prev => ({
      ...prev,
      [fieldName]: (prev[fieldName as keyof typeof prev] as string[]).filter((_, i) => i !== index)
    }));
  };

  const updateArrayItem = (fieldName: string, index: number, value: string) => {
    setFormData(prev => ({
      ...prev,
      [fieldName]: (prev[fieldName as keyof typeof prev] as string[]).map((item, i) => i === index ? value : item)
    }));
  };

  // Helper functions for specifications
  const addSpecification = () => {
    setFormData(prev => ({
      ...prev,
      specifications: [...prev.specifications, { key: '', value: '' }]
    }));
  };

  const removeSpecification = (index: number) => {
    setFormData(prev => ({
      ...prev,
      specifications: prev.specifications.filter((_, i) => i !== index)
    }));
  };

  const updateSpecification = (index: number, field: 'key' | 'value', value: string) => {
    setFormData(prev => ({
      ...prev,
      specifications: prev.specifications.map((spec, i) =>
        i === index ? { ...spec, [field]: value } : spec
      )
    }));
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setImages(prev => [...prev, ...files]);
    
    // Create previews
    files.forEach(file => {
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreviews(prev => [...prev, e.target?.result as string]);
      };
      reader.readAsDataURL(file);
    });
  };

  const removeImage = (index: number) => {
    setImages(prev => prev.filter((_, i) => i !== index));
    setImagePreviews(prev => prev.filter((_, i) => i !== index));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (!formData.name || !formData.description || !formData.price || !formData.category) {
      alert('Please fill in all required fields (Name, Description, Price, Category)');
      return;
    }

    // Validate at least one valid specification
    const validSpecs = (formData.specifications as Array<{key: string, value: string}>)
      .filter(spec => spec.key.trim() && spec.value.trim());
    if (validSpecs.length === 0) {
      alert('Please add at least one valid specification (key and value).');
      return;
    }

    // 1. Upload images to S3 and get URLs (Amplify v5+ modular API)
    let imageUrls: string[] = [];

    // Check if we have either new images or existing images
    const hasNewImages = images.length > 0;
    const hasExistingImages = imagePreviews.length > 0;

    console.log('Image validation:', {
      hasNewImages,
      hasExistingImages,
      newImagesCount: images.length,
      existingImagesCount: imagePreviews.length,
      imagePreviews,
      productId
    });

    if (!hasNewImages && !hasExistingImages) {
      alert('Please add at least one product image');
      return;
    }

    // Upload new images to S3 if any
    if (hasNewImages) {
      try {
        const newImageUrls = await Promise.all(
          images.map(async (file) => {
            const filename = `products/${Date.now()}-${file.name}`;
            console.log('Uploading image to S3:', filename);

            // Upload to S3 with authenticated access
            await uploadData({
              key: filename,
              data: file,
              options: {
                contentType: file.type
              }
            }).result;

            console.log('S3 upload successful:', {
              filename,
              key: filename,
              size: file.size
            });

            // Return the S3 key instead of URL - we'll generate URLs when needed
            return filename;
          })
        );
        imageUrls = [...newImageUrls];
        console.log('All images uploaded to S3:', imageUrls.length);
      } catch (err) {
        console.error('S3 upload error:', err);
        alert('Image upload failed: ' + (err instanceof Error ? err.message : JSON.stringify(err)));
        return;
      }
    }

    // For edit mode, combine existing images with new ones
    if (productId && hasExistingImages && originalProduct) {
      // Get the original S3 keys from the database (not the preview URLs)
      const existingImageKeys = originalProduct.images || [];
      console.log('Existing image keys from database:', existingImageKeys);

      // Filter out any that were removed (not in current imagePreviews)
      const validExistingKeys = existingImageKeys.filter((_, index) =>
        index < imagePreviews.length &&
        !imagePreviews[index].includes('placeholder-product.png')
      );

      imageUrls = [...validExistingKeys, ...imageUrls];
      console.log('Combined images for edit mode:', {
        existingKeys: validExistingKeys.length,
        newKeys: imageUrls.length - validExistingKeys.length,
        total: imageUrls.length
      });
    }

    // 2. Build the payload for GraphQL (including specifications)
    // Convert specifications array to JSON string for AWSJSON field
    const specificationsObject: Record<string, string> = {};
    formData.specifications.forEach(spec => {
      if (spec.key.trim() && spec.value.trim()) {
        specificationsObject[spec.key.trim()] = spec.value.trim();
      }
    });

    // Convert to JSON string for AWSJSON field
    const specificationsJson = Object.keys(specificationsObject).length > 0
      ? JSON.stringify(specificationsObject)
      : null;

    console.log('Specifications processing:', {
      formDataSpecs: formData.specifications,
      specificationsObject,
      specificationsJson
    });

    const payload = {
      name: formData.name,
      slug: formData.slug,
      description: formData.description,
      shortDescription: formData.shortDescription,
      narration: formData.narration,
      luxuryDescription: formData.luxuryDescription,
      budgetDescription: formData.budgetDescription,
      category: formData.category,
      subcategory: formData.subcategory,
      sku: formData.sku,
      careInstructions: formData.careInstructions,
      warranty: formData.warranty,
      shippingInfo: formData.shippingInfo,
      returnPolicy: formData.returnPolicy,
      badge: formData.badge,
      metaTitle: formData.metaTitle,
      metaDescription: formData.metaDescription,
      isFeatured: formData.isFeatured,
      isActive: formData.isActive,
      inStock: formData.inStock,
      price: parseFloat(formData.price),
      originalPrice: formData.originalPrice ? parseFloat(formData.originalPrice) : undefined,
      stockQuantity: formData.stockQuantity ? parseInt(formData.stockQuantity) : undefined,
      weight: formData.weight ? parseFloat(formData.weight) : undefined,
      sortOrder: formData.sortOrder ? parseInt(formData.sortOrder) : undefined,
      rating: formData.rating ? parseFloat(formData.rating) : undefined,
      reviewCount: formData.reviewCount ? parseInt(formData.reviewCount) : undefined,
      images: imageUrls,
      tags: formData.tags.filter(tag => tag && tag.trim()),
      features: formData.features.filter(f => f && f.trim()),
      luxuryFeatures: formData.luxuryFeatures.filter(f => f && f.trim()),
      budgetFeatures: formData.budgetFeatures.filter(f => f && f.trim()),
      materials: formData.materials.filter(m => m && m.trim()),
      relatedProductIds: formData.relatedProductIds.filter(id => id && id.trim()),
      specifications: specificationsJson, // Include specifications as JSON string for AWSJSON
    };

    // 4. Call the GraphQL mutation
    console.log('Payload to GraphQL:', payload);
    let response;
    if (productId) {
      response = await adminApi.updateProduct(productId, payload);
    } else {
      response = await adminApi.createProduct(payload);
    }

    if (response.success) {
      toast.success(productId ? 'Product updated successfully!' : 'Product created successfully!', {
        icon: '✅',
        duration: 3000,
      });
      setTimeout(() => {
        window.location.href = '/dashboard/products';
      }, 1000);
    } else {
      toast.error(`Error ${productId ? 'updating' : 'creating'} product: ` + (response.error || response.message), {
        icon: '❌',
        duration: 5000,
      });
    }
  };

  if (loadingProduct) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Loading product for editing...</p>
          <p className="text-sm text-gray-500 mt-2">Product ID: {productId}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Debug Info for Edit Mode */}
      {productId && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
          <h3 className="font-medium text-blue-900 mb-2">Edit Mode Debug Info</h3>
          <p className="text-sm text-blue-700">Product ID: {productId}</p>
          <p className="text-sm text-blue-700">Form Data Loaded: {formData.name ? 'Yes' : 'No'}</p>
          <p className="text-sm text-blue-700">Product Name: {formData.name || 'Not loaded'}</p>
          <p className="text-sm text-blue-700">Original Product: {originalProduct ? 'Loaded' : 'Not loaded'}</p>
        </div>
      )}

      {/* Autofill Button */}
      <button
        type="button"
        onClick={autofillTestData}
        className="mb-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
      >
        Autofill Test Data
      </button>
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link href="/dashboard/products" className="p-2 hover:bg-gray-100 rounded-lg">
          <ArrowLeft className="w-5 h-5" />
        </Link>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            {productId ? 'Edit Product' : 'Add New Product'}
          </h1>
          <p className="text-gray-600">
            {productId ? 'Update your gift box product' : 'Create a new gift box product'}
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Basic Information</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Product Name *</label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Slug *</label>
              <input
                type="text"
                name="slug"
                value={formData.slug}
                onChange={handleInputChange}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Price *</label>
              <input
                type="number"
                name="price"
                value={formData.price}
                onChange={handleInputChange}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Original Price</label>
              <input
                type="number"
                name="originalPrice"
                value={formData.originalPrice}
                onChange={handleInputChange}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Category *</label>
              <select
                name="category"
                value={formData.category}
                onChange={handleInputChange}
                className={selectStyles}
                required
              >
                <option value="">Select Category</option>
                <option value="wedding">Wedding</option>
                <option value="corporate">Corporate</option>
                <option value="birthday">Birthday</option>
                <option value="anniversary">Anniversary</option>
                <option value="festival">Festival</option>
                <option value="luxury">Luxury</option>
                <option value="budget">Budget</option>
                <option value="premium">Premium</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Subcategory</label>
              <input
                type="text"
                name="subcategory"
                value={formData.subcategory}
                onChange={handleInputChange}
                className={inputStyles}
                placeholder="e.g., Engagement, Housewarming"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">SKU</label>
              <input
                type="text"
                name="sku"
                value={formData.sku}
                onChange={handleInputChange}
                className={inputStyles}
                placeholder="Product SKU"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Stock Quantity *</label>
              <input
                type="number"
                name="stockQuantity"
                value={formData.stockQuantity}
                onChange={handleInputChange}
                className={inputStyles}
                required
                min="0"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Weight (kg)</label>
              <input
                type="number"
                name="weight"
                value={formData.weight}
                onChange={handleInputChange}
                className={inputStyles}
                step="0.1"
                min="0"
                placeholder="Product weight in kg"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Dimensions</label>
              <input
                type="text"
                name="dimensions"
                value={formData.dimensions}
                onChange={handleInputChange}
                className={inputStyles}
                placeholder="e.g., 20cm x 15cm x 10cm"
              />
            </div>
          </div>
        </div>

        {/* Descriptions */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Descriptions</h2>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Short Description</label>
              <textarea
                name="shortDescription"
                value={formData.shortDescription}
                onChange={handleInputChange}
                rows={2}
                className={inputStyles}
                placeholder="Brief product summary for listings..."
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Main Description *</label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows={4}
                className={inputStyles}
                required
                placeholder="Detailed product description..."
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Narration (Storytelling)</label>
              <textarea
                name="narration"
                value={formData.narration}
                onChange={handleInputChange}
                rows={3}
                className={inputStyles}
                placeholder="Tell the story behind this product..."
              />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Luxury Description</label>
                <textarea
                  name="luxuryDescription"
                  value={formData.luxuryDescription}
                  onChange={handleInputChange}
                  rows={3}
                  className={inputStyles}
                  placeholder="Premium experience description..."
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Budget Description</label>
                <textarea
                  name="budgetDescription"
                  value={formData.budgetDescription}
                  onChange={handleInputChange}
                  rows={3}
                  className={inputStyles}
                  placeholder="Value-focused description..."
                />
              </div>
            </div>
          </div>
        </div>

        {/* Images */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Product Images</h2>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Upload Images</label>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-600 mb-2">Click to upload or drag and drop</p>
                <p className="text-sm text-gray-500">PNG, JPG, GIF up to 10MB</p>
                <input
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                  id="image-upload"
                />
                <label
                  htmlFor="image-upload"
                  className="mt-2 inline-block bg-blue-600 text-white px-4 py-2 rounded-lg cursor-pointer hover:bg-blue-700"
                >
                  Choose Files
                </label>
              </div>
            </div>

            {/* Image Previews */}
            {console.log('Current imagePreviews:', imagePreviews)}
            {imagePreviews.length > 0 ? (
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {imagePreviews.map((preview, index) => (
                  <div key={index} className="relative">
                    <img
                      src={preview || '/placeholder-product.png'}
                      alt={`Preview ${index + 1}`}
                      className="w-full h-32 object-cover rounded-lg border border-gray-200"
                      onError={(e) => {
                        console.error('Image failed to load:', preview);
                        e.currentTarget.src = '/placeholder-product.png';
                      }}
                      onLoad={() => {
                        console.log('Image loaded successfully:', preview);
                      }}
                    />
                    <button
                      type="button"
                      onClick={() => removeImage(index)}
                      className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-4 text-gray-500">
                <p>No images uploaded yet</p>
              </div>
            )}
          </div>
        </div>

        {/* Product Features */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Product Features</h2>

          {/* General Features */}
          <div className="space-y-4 mb-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">General Features</label>
              {formData.features.map((feature, index) => (
                <div key={index} className="flex items-center space-x-2 mb-2">
                  <input
                    type="text"
                    value={feature}
                    onChange={(e) => updateArrayItem('features', index, e.target.value)}
                    className={flexInputStyles}
                    placeholder="Enter feature"
                  />
                  <button
                    type="button"
                    onClick={() => removeArrayItem('features', index)}
                    className="p-2 text-red-600 hover:bg-red-50 rounded-lg"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              ))}
              <button
                type="button"
                onClick={() => addArrayItem('features')}
                className="text-blue-600 hover:text-blue-700 text-sm font-medium"
              >
                + Add Feature
              </button>
            </div>
          </div>

          {/* Luxury Features */}
          <div className="space-y-4 mb-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Luxury Features</label>
              {formData.luxuryFeatures.map((feature, index) => (
                <div key={index} className="flex items-center space-x-2 mb-2">
                  <input
                    type="text"
                    value={feature}
                    onChange={(e) => updateArrayItem('luxuryFeatures', index, e.target.value)}
                    className={flexInputStyles}
                    placeholder="Enter luxury feature"
                  />
                  <button
                    type="button"
                    onClick={() => removeArrayItem('luxuryFeatures', index)}
                    className="p-2 text-red-600 hover:bg-red-50 rounded-lg"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              ))}
              <button
                type="button"
                onClick={() => addArrayItem('luxuryFeatures')}
                className="text-blue-600 hover:text-blue-700 text-sm font-medium"
              >
                + Add Luxury Feature
              </button>
            </div>
          </div>

          {/* Budget Features */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Budget Features</label>
              {formData.budgetFeatures.map((feature, index) => (
                <div key={index} className="flex items-center space-x-2 mb-2">
                  <input
                    type="text"
                    value={feature}
                    onChange={(e) => updateArrayItem('budgetFeatures', index, e.target.value)}
                    className={flexInputStyles}
                    placeholder="Enter budget feature"
                  />
                  <button
                    type="button"
                    onClick={() => removeArrayItem('budgetFeatures', index)}
                    className="p-2 text-red-600 hover:bg-red-50 rounded-lg"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              ))}
              <button
                type="button"
                onClick={() => addArrayItem('budgetFeatures')}
                className="text-blue-600 hover:text-blue-700 text-sm font-medium"
              >
                + Add Budget Feature
              </button>
            </div>
          </div>
        </div>

        {/* Product Specifications */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Product Specifications</h2>
          <div className="space-y-4">
            {formData.specifications.map((spec, index) => (
              <div key={index} className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <input
                  type="text"
                  value={spec.key}
                  onChange={(e) => updateSpecification(index, 'key', e.target.value)}
                  className="border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Specification name (e.g., Box Dimensions)"
                />
                <div className="flex items-center space-x-2">
                  <input
                    type="text"
                    value={spec.value}
                    onChange={(e) => updateSpecification(index, 'value', e.target.value)}
                    className={flexInputStyles}
                    placeholder="Specification value (e.g., 20cm x 15cm x 10cm)"
                  />
                  <button
                    type="button"
                    onClick={() => removeSpecification(index)}
                    className="p-2 text-red-600 hover:bg-red-50 rounded-lg"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ))}
            <button
              type="button"
              onClick={addSpecification}
              className="text-blue-600 hover:text-blue-700 text-sm font-medium"
            >
              + Add Specification
            </button>
          </div>
        </div>

        {/* Materials & Care */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Materials & Care Information</h2>
          <div className="space-y-4">
            {/* Materials */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Materials</label>
              {formData.materials.map((material, index) => (
                <div key={index} className="flex items-center space-x-2 mb-2">
                  <input
                    type="text"
                    value={material}
                    onChange={(e) => updateArrayItem('materials', index, e.target.value)}
                    className={flexInputStyles}
                    placeholder="Enter material"
                  />
                  <button
                    type="button"
                    onClick={() => removeArrayItem('materials', index)}
                    className="p-2 text-red-600 hover:bg-red-50 rounded-lg"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              ))}
              <button
                type="button"
                onClick={() => addArrayItem('materials')}
                className="text-blue-600 hover:text-blue-700 text-sm font-medium"
              >
                + Add Material
              </button>
            </div>

            {/* Care Instructions */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Care Instructions</label>
              <textarea
                name="careInstructions"
                value={formData.careInstructions}
                onChange={handleInputChange}
                rows={3}
                className={inputStyles}
                placeholder="How to care for this product..."
              />
            </div>

            {/* Warranty */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Warranty Information</label>
              <textarea
                name="warranty"
                value={formData.warranty}
                onChange={handleInputChange}
                rows={2}
                className={inputStyles}
                placeholder="Warranty details..."
              />
            </div>
          </div>
        </div>

        {/* Shipping & Returns */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Shipping & Returns</h2>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Shipping Information</label>
              <textarea
                name="shippingInfo"
                value={formData.shippingInfo}
                onChange={handleInputChange}
                rows={3}
                className={inputStyles}
                placeholder="Shipping details, delivery time, etc..."
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Return Policy</label>
              <textarea
                name="returnPolicy"
                value={formData.returnPolicy}
                onChange={handleInputChange}
                rows={3}
                className={inputStyles}
                placeholder="Return policy details..."
              />
            </div>
          </div>
        </div>

        {/* Additional Information */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Additional Information</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Badge</label>
              <select
                name="badge"
                value={formData.badge}
                onChange={handleInputChange}
                className={selectStyles}
              >
                <option value="">No Badge</option>
                <option value="Bestseller">Bestseller</option>
                <option value="New">New</option>
                <option value="Limited Edition">Limited Edition</option>
                <option value="Premium">Premium</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Initial Rating</label>
              <input
                type="number"
                name="rating"
                value={formData.rating}
                onChange={handleInputChange}
                className={inputStyles}
                min="0"
                max="5"
                step="0.1"
                placeholder="4.5"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Initial Review Count</label>
              <input
                type="number"
                name="reviewCount"
                value={formData.reviewCount}
                onChange={handleInputChange}
                className={inputStyles}
                min="0"
                placeholder="0"
              />
            </div>
          </div>
        </div>

        {/* SEO & Meta Information */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">SEO & Meta Information</h2>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Meta Title</label>
              <input
                type="text"
                name="metaTitle"
                value={formData.metaTitle}
                onChange={handleInputChange}
                className={inputStyles}
                placeholder="SEO title for search engines"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Meta Description</label>
              <textarea
                name="metaDescription"
                value={formData.metaDescription}
                onChange={handleInputChange}
                rows={3}
                className={inputStyles}
                placeholder="SEO description for search engines (150-160 characters)"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Tags</label>
              <input
                type="text"
                name="tags"
                value={formData.tags.join(', ')}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  tags: e.target.value.split(',').map(tag => tag.trim()).filter(tag => tag)
                }))}
                className={inputStyles}
                placeholder="Enter tags separated by commas (e.g., wedding, luxury, gift box)"
              />
            </div>
          </div>
        </div>

        {/* Product Settings */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Product Settings</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Sort Order</label>
              <input
                type="number"
                name="sortOrder"
                value={formData.sortOrder}
                onChange={handleInputChange}
                className={inputStyles}
                placeholder="Display order (lower numbers appear first)"
                min="0"
              />
            </div>
            <div className="space-y-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isActive"
                  name="isActive"
                  checked={formData.isActive}
                  onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="isActive" className="ml-2 block text-sm text-gray-900">
                  Product is Active (visible to customers)
                </label>
              </div>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isFeatured"
                  name="isFeatured"
                  checked={formData.isFeatured}
                  onChange={(e) => setFormData(prev => ({ ...prev, isFeatured: e.target.checked }))}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="isFeatured" className="ml-2 block text-sm text-gray-900">
                  Featured Product (appears in featured sections)
                </label>
              </div>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="inStock"
                  name="inStock"
                  checked={formData.inStock}
                  onChange={(e) => setFormData(prev => ({ ...prev, inStock: e.target.checked }))}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="inStock" className="ml-2 block text-sm text-gray-900">
                  In Stock (available for purchase)
                </label>
              </div>
            </div>
          </div>
        </div>

        {/* Submit Buttons */}
        <div className="flex items-center justify-end space-x-4">
          <Link
            href="/dashboard/products"
            className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
          >
            Cancel
          </Link>
          <button
            type="submit"
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            {productId ? 'Update Product' : 'Create Product'}
          </button>
        </div>
      </form>
    </div>
  );
}

export default function AddProductPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    }>
      <AddProductPageContent />
    </Suspense>
  );
}
