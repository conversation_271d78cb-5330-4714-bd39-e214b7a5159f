'use client';

import React, { useState, useEffect } from 'react';
import toast from 'react-hot-toast';
import Link from 'next/link';
import { Plus, Search, Filter, Edit, Trash2, Eye, Loader2, AlertCircle } from 'lucide-react';
import { adminApi, Product } from '../../../services/adminApi';

// Import Amplify configuration
import { Amplify } from 'aws-amplify';
// import { getUrl } from 'aws-amplify/storage'; // Unused for now
import amplifyconfig from '../../../amplifyconfiguration.json';

// Configure Amplify
Amplify.configure(amplifyconfig);

// Utility function to convert S3 key to URL (currently unused)
/*
const getImageUrl = async (imageData: string): Promise<string> => {
  try {
    // If it's a relative path, use it as is
    if (imageData.startsWith('/')) {
      return imageData;
    }

    // If it's an S3 key, convert to URL
    if (imageData.startsWith('products/') || imageData.includes('products/')) {
      const { url } = await getUrl({
        key: imageData,
        options: { expiresIn: 86400 } // 24 hours
      });
      return url.toString();
    }

    // If it's already a URL, try to extract key and refresh
    if (imageData.startsWith('http')) {
      const urlParts = imageData.split('/');
      const keyIndex = urlParts.findIndex(part => part === 'products');
      if (keyIndex !== -1 && keyIndex < urlParts.length - 1) {
        const key = urlParts.slice(keyIndex).join('/');
        const { url } = await getUrl({
          key,
          options: { expiresIn: 86400 }
        });
        return url.toString();
      }
      return imageData; // Fallback to original URL
    }

    return '/placeholder-product.png';
  } catch (error) {
    console.error('Error converting image to URL:', error);
    return '/placeholder-product.png';
  }
};
*/

export default function ProductsPage() {
  const [products, setProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');

  // Load products from Amplify DynamoDB
  useEffect(() => {
    const loadProducts = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await adminApi.getProducts();

        console.log('Products API response:', response); // Debug log

        if (response.success && response.data) {
          // Handle the nested response structure from adminApi.getProducts
          const productsData = response.data.products || [];
          console.log('Products data:', productsData); // Debug log
          setProducts(productsData);
          setFilteredProducts(productsData);
        } else {
          console.error('Failed to load products:', response.error); // Debug log
          setError(response.error || 'Failed to load products');
          setProducts([]);
          setFilteredProducts([]);
        }
      } catch (err) {
        console.error('Error loading products:', err);
        setError('Failed to load products. Please try again.');
        setProducts([]);
        setFilteredProducts([]);
      } finally {
        setLoading(false);
      }
    };

    loadProducts();
  }, []);

  // Filter products when search term or category changes
  useEffect(() => {
    let filtered = products;

    // Apply search filter
    if (searchTerm.trim()) {
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.category.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply category filter
    if (selectedCategory) {
      filtered = filtered.filter(product => product.category === selectedCategory);
    }

    setFilteredProducts(filtered);
  }, [products, searchTerm, selectedCategory]);

  // Get unique categories
  const categories = [...new Set(products.map(p => p.category))];

  // Helper functions
  const getProductStatus = (product: Product) => {
    if (!product.isActive) return 'Inactive';
    if ((product.stockQuantity || 0) < 10) return 'Low Stock';
    return 'Active';
  };



  const handleDeleteProduct = async (id: string) => {
    // Show confirmation toast
    toast((t) => (
      <div className="flex flex-col space-y-2">
        <span>Are you sure you want to delete this product?</span>
        <div className="flex space-x-2">
          <button
            onClick={() => {
              toast.dismiss(t.id);
              performDeleteProduct(id);
            }}
            className="bg-red-600 text-white px-3 py-1 rounded text-sm"
          >
            Delete
          </button>
          <button
            onClick={() => toast.dismiss(t.id)}
            className="bg-gray-300 text-gray-700 px-3 py-1 rounded text-sm"
          >
            Cancel
          </button>
        </div>
      </div>
    ), {
      duration: 10000,
      icon: '⚠️',
    });
  };

  const performDeleteProduct = async (id: string) => {
    try {
      const loadingToast = toast.loading('Deleting product...', { icon: '🗑️' });

      const response = await adminApi.deleteProduct(id);

      toast.dismiss(loadingToast);

      if (response.success) {
        setProducts(prev => prev.filter(p => p.id !== id));
        setFilteredProducts(prev => prev.filter(p => p.id !== id));
        toast.success('Product deleted successfully!', { icon: '✅' });
      } else {
        toast.error(response.error || 'Failed to delete product');
      }
    } catch (err) {
      console.error('Error deleting product:', err);
      toast.error('Failed to delete product. Please try again.');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Loading products...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <AlertCircle className="w-8 h-8 mx-auto mb-4 text-red-600" />
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Products</h1>
          <p className="text-gray-600">Manage your gift box products</p>
        </div>
        <Link
          href="/dashboard/products/add"
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
        >
          <Plus className="w-4 h-4" />
          <span>Add Product</span>
        </Link>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4">
          {/* Search */}
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search products..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 placeholder-gray-500"
            />
          </div>

          {/* Category Filter */}
          <div className="flex items-center space-x-2">
            <Filter className="w-4 h-4 text-gray-400" />
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent font-medium"
            >
              <option value="">All Categories</option>
              {categories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Products Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="text-left py-3 px-6 font-medium text-gray-600">Product</th>
                <th className="text-left py-3 px-6 font-medium text-gray-600">Category</th>
                <th className="text-left py-3 px-6 font-medium text-gray-600">Price</th>
                <th className="text-left py-3 px-6 font-medium text-gray-600">Stock</th>
                <th className="text-left py-3 px-6 font-medium text-gray-600">Status</th>
                <th className="text-left py-3 px-6 font-medium text-gray-600">Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredProducts.map((product) => (
                <tr key={product.id} className="border-b border-gray-100 hover:bg-gray-50">
                  <td className="py-4 px-6">
                    <div>
                      <p className="font-medium text-gray-900">{product.name}</p>
                      <p className="text-sm text-gray-500">ID: {product.id}</p>
                    </div>
                  </td>
                  <td className="py-4 px-6 text-gray-600">{product.category}</td>
                  <td className="py-4 px-6 font-medium text-gray-900">₹{product.price.toLocaleString()}</td>
                  <td className="py-4 px-6 text-gray-600">{product.stockQuantity || 0} units</td>
                  <td className="py-4 px-6">
                    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                      getProductStatus(product) === 'Active' ? 'bg-green-100 text-green-800' :
                      getProductStatus(product) === 'Low Stock' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {getProductStatus(product)}
                    </span>
                  </td>
                  <td className="py-4 px-6">
                    <div className="flex items-center space-x-2">
                      <Link
                        href={`/products/${product.slug}`}
                        className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                        title="View Product"
                      >
                        <Eye className="w-4 h-4" />
                      </Link>
                      <Link
                        href={`/dashboard/products/add?id=${product.id}`}
                        className="p-1 text-gray-400 hover:text-green-600 transition-colors"
                        title="Edit Product"
                      >
                        <Edit className="w-4 h-4" />
                      </Link>
                      
                      <button
                        onClick={() => handleDeleteProduct(product.id)}
                        className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                        title="Delete Product"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
              {filteredProducts.length === 0 && (
                <tr>
                  <td colSpan={6} className="py-8 px-6 text-center text-gray-500">
                    No products found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Product Count */}
      <div className="flex items-center justify-between">
        <p className="text-sm text-gray-600">
          Showing {filteredProducts.length} of {products.length} products
        </p>
      </div>
    </div>
  );
}
