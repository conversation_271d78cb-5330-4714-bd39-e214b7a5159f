'use client';

import React, { useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import RoleBasedSidebar from '@/components/dashboard/RoleBasedSidebar';
import AdminHeader from '@/components/dashboard/AdminHeader';
import AdminBreadcrumb from '@/components/dashboard/AdminBreadcrumb';
import RouteProtection from '@/components/dashboard/RouteProtection';
import { AdminErrorBoundary } from '@/components/dashboard/AdminErrorBoundary';
import { useAuth } from '@/context/AuthContext';
import { Loader2, Lock } from 'lucide-react';

// Import admin role helper and migration utility for development/testing
import '@/utils/adminRoleHelper';
import '@/utils/roleMigration';
import '@/utils/inquiryAPI';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { isLoggedIn, loading, user } = useAuth();
  const router = useRouter();

  // Redirect to home page if not authenticated
  useEffect(() => {
    if (!loading && !isLoggedIn) {
      router.push('/');
    }
  }, [loading, isLoggedIn, router]);

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-12 h-12 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600 text-lg">Checking authentication...</p>
        </div>
      </div>
    );
  }

  // Show login required message if not authenticated
  if (!isLoggedIn || !user) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="max-w-md w-full mx-4">
          <div className="bg-white rounded-lg shadow-lg p-8 text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Lock className="w-8 h-8 text-red-600" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Access Restricted</h2>
            <p className="text-gray-600 mb-6">
              You need to be logged in to access this area. Please log in with your account to continue.
            </p>
            <div className="space-y-3">
              <Link
                href="/"
                className="w-full bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors inline-block text-center"
              >
                Go to Home Page
              </Link>
              <p className="text-sm text-gray-500">
                Log in or sign up on the main website to access your dashboard.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show dashboard for authenticated users
  return (
    <AdminErrorBoundary>
      <div className="min-h-screen bg-gray-100 overflow-hidden">
        <AdminHeader />
        <div className="flex h-screen">
          <RoleBasedSidebar />
          <main className="flex-1 p-6 overflow-y-auto">
            <AdminBreadcrumb />
            {/* Route protection and user context */}
            <RouteProtection>
              <div data-user-id={user.id} data-user-email={user.email}>
                {children}
              </div>
            </RouteProtection>
          </main>
        </div>
      </div>
    </AdminErrorBoundary>
  );
}
