'use client';

import React, { useEffect, useState, useCallback } from 'react';
import Link from 'next/link';
import {
  Calendar,
  IndianRupee,
  ArrowLeft,
  Search,
  Filter,
  Eye,
  Edit,
  Package,
  User,
  MapPin,
  Truck,
  RefreshCw,
  Download,
  ChevronDown,
  ChevronUp,
  Save,
  X,
  Check,
  Clock
} from 'lucide-react';
import { useAuth } from '../../../context/AuthContext';
import { orderAPI } from '../../../utils/orderAPI';
import { Amplify } from 'aws-amplify';
import amplifyconfig from '../../../amplifyconfiguration.json';
import toast from 'react-hot-toast';
import { getOrderStatusColor, getPaymentStatusColor, getOrderStatusText } from '../../../hooks/useOrderTracking';
import { useRoleAccess } from '../../../hooks/useRoleAccess';
// Configure Amplify
Amplify.configure(amplifyconfig);

interface AdminOrderData {
  id: string;
  orderNumber: string;
  userId: string;
  status: string;
  paymentStatus: string;
  total: number;
  currency: string;
  createdAt: string;
  updatedAt: string;
  trackingNumber?: string;
  estimatedDelivery?: string;
  deliveredAt?: string;
  shippingAddress?: {
    name: string;
    email?: string;
    phone?: string;
    addressLine1: string;
    city: string;
    state: string;
    pincode: string;
  };
  items: Array<{
    id: string;
    name: string;
    quantity: number;
    price: number;
    giftType?: string;
    customization?: string;
  }>;
}

export default function AdminOrdersPage() {
  const { user, isLoggedIn } = useAuth();
  const [orders, setOrders] = useState<AdminOrderData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('ALL');
  const [paymentFilter, setPaymentFilter] = useState('ALL');
  const [sortBy, setSortBy] = useState('createdAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [expandedOrders, setExpandedOrders] = useState<Set<string>>(new Set());
  const [selectedOrders, setSelectedOrders] = useState<Set<string>>(new Set());

  // New state for inline editing
  const [editingOrder, setEditingOrder] = useState<string | null>(null);
  const [editingStatus, setEditingStatus] = useState<string>('');
  const [editingTrackingNumber, setEditingTrackingNumber] = useState<string>('');
  const [editingEstimatedDelivery, setEditingEstimatedDelivery] = useState<string>('');
  const [savingOrder, setSavingOrder] = useState<string | null>(null);

  const { isAdmin } = useRoleAccess();
  const isAdminUser = isAdmin();

  // Fetch all orders (admin can see all orders)
  const fetchAllOrders = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      // For admin, we'll fetch all orders from the system
      const result = await orderAPI.getAllOrders();

      if (result.success) {
        setOrders(result.data || []);
      } else {
        throw new Error(result.error || 'Failed to fetch orders');
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch orders';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    if (isAdminUser) {
      fetchAllOrders();
    }
  }, [isAdminUser, fetchAllOrders]);

  // Handle inline editing functions
  const startEditing = (order: AdminOrderData) => {
    setEditingOrder(order.id);
    setEditingStatus(order.status);
    setEditingTrackingNumber(order.trackingNumber || '');
    setEditingEstimatedDelivery(order.estimatedDelivery || '');
  };

  const cancelEditing = () => {
    setEditingOrder(null);
    setEditingStatus('');
    setEditingTrackingNumber('');
    setEditingEstimatedDelivery('');
  };

  const saveOrderChanges = async (orderId: string) => {
    setSavingOrder(orderId);
    try {
      const result = await orderAPI.updateOrderStatus(
        orderId,
        editingStatus,
        editingTrackingNumber || undefined,
        editingEstimatedDelivery || undefined
      );

      if (result.success) {
        toast.success('Order updated successfully!');
        // Update the local state
        setOrders(prevOrders =>
          prevOrders.map(order =>
            order.id === orderId
              ? {
                  ...order,
                  status: editingStatus,
                  trackingNumber: editingTrackingNumber || undefined,
                  estimatedDelivery: editingEstimatedDelivery || undefined,
                  updatedAt: new Date().toISOString()
                }
              : order
          )
        );
        cancelEditing();
      } else {
        throw new Error(result.error || 'Failed to update order');
      }
    } catch (error: any) {
      toast.error(error.message || 'Failed to update order');
    } finally {
      setSavingOrder(null);
    }
  };

  // Filter and sort orders
  const filteredOrders = orders.filter(order => {
    const matchesSearch =
      order.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.userId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.shippingAddress?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.shippingAddress?.email?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'ALL' || order.status === statusFilter;
    const matchesPayment = paymentFilter === 'ALL' || order.paymentStatus === paymentFilter;

    return matchesSearch && matchesStatus && matchesPayment;
  }).sort((a, b) => {
    let aValue: any = a[sortBy as keyof AdminOrderData];
    let bValue: any = b[sortBy as keyof AdminOrderData];

    if (sortBy === 'createdAt' || sortBy === 'updatedAt') {
      aValue = new Date(aValue).getTime();
      bValue = new Date(bValue).getTime();
    }

    if (sortOrder === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });

  const toggleOrderExpansion = (orderId: string) => {
    const newExpanded = new Set(expandedOrders);
    if (newExpanded.has(orderId)) {
      newExpanded.delete(orderId);
    } else {
      newExpanded.add(orderId);
    }
    setExpandedOrders(newExpanded);
  };

  const toggleOrderSelection = (orderId: string) => {
    const newSelected = new Set(selectedOrders);
    if (newSelected.has(orderId)) {
      newSelected.delete(orderId);
    } else {
      newSelected.add(orderId);
    }
    setSelectedOrders(newSelected);
  };

  const handleRefresh = () => {
    fetchAllOrders();
  };

  const handleExport = () => {
    // Export selected orders or all filtered orders
    const ordersToExport = selectedOrders.size > 0
      ? filteredOrders.filter(order => selectedOrders.has(order.id))
      : filteredOrders;

    const csvContent = generateCSV(ordersToExport);
    downloadCSV(csvContent, 'admin-orders-export.csv');
    toast.success(`Exported ${ordersToExport.length} orders`);
  };

  const generateCSV = (orders: AdminOrderData[]) => {
    const headers = [
      'Order Number', 'User ID', 'Customer Name', 'Email', 'Phone',
      'Status', 'Payment Status', 'Total', 'Currency', 'Created At',
      'Tracking Number', 'Estimated Delivery', 'Address'
    ];

    const rows = orders.map(order => [
      order.orderNumber,
      order.userId,
      order.shippingAddress?.name || 'N/A',
      order.shippingAddress?.email || '',
      order.shippingAddress?.phone || '',
      order.status,
      order.paymentStatus,
      order.total,
      order.currency,
      new Date(order.createdAt).toLocaleDateString(),
      order.trackingNumber || '',
      order.estimatedDelivery ? new Date(order.estimatedDelivery).toLocaleDateString() : '',
      order.shippingAddress ? `${order.shippingAddress.addressLine1}, ${order.shippingAddress.city}, ${order.shippingAddress.state} - ${order.shippingAddress.pincode}` : 'N/A'
    ]);

    return [headers, ...rows].map(row => row.map(field => `"${field}"`).join(',')).join('\n');
  };

  const downloadCSV = (content: string, filename: string) => {
    const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', filename);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  // Authentication and authorization checks
  if (!isLoggedIn || !user?.id) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md mx-auto text-center">
          <div className="bg-white rounded-lg shadow-sm p-8">
            <Package className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Admin Login Required</h2>
            <p className="text-gray-600 mb-6">
              Please log in with admin credentials to access order management.
            </p>
            <Link
              href="/login"
              className="block w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Log In
            </Link>
          </div>
        </div>
      </div>
    );
  }

  if (!isAdminUser) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md mx-auto text-center">
          <div className="bg-white rounded-lg shadow-sm p-8">
            <Package className="w-16 h-16 text-red-300 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Access Denied</h2>
            <p className="text-gray-600 mb-6">
              You don&apos;t have admin privileges to access this page.
            </p>
            <Link
              href="/dashboard/my-orders"
              className="block w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Go to My Orders
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Enhanced Header */}
      <div className="bg-white shadow-lg border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link
                href="/dashboard"
                className="flex items-center text-gray-600 hover:text-gray-900 transition-colors group"
              >
                <ArrowLeft className="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform" />
                Back to Dashboard
              </Link>
              <div className="h-6 border-l border-gray-300"></div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900 flex items-center">
                  <Package className="w-8 h-8 mr-3 text-blue-600" />
                  Orders Management
                </h1>
                <p className="text-gray-600 mt-1">Manage and track all customer orders</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <div className="text-sm text-gray-500 text-right">
                <p className="font-medium">Total Orders: {orders.length}</p>
                <p>Showing: {filteredOrders.length}</p>
              </div>
              <button
                onClick={handleRefresh}
                disabled={loading}
                className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </button>
              <button
                onClick={handleExport}
                disabled={filteredOrders.length === 0}
                className="inline-flex items-center px-3 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
              >
                <Download className="w-4 h-4 mr-2" />
                Export ({filteredOrders.length})
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Summary Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200 hover:shadow-md transition-shadow">
            <div className="flex items-center">
              <div className="p-3 bg-blue-50 rounded-lg border border-blue-100">
                <Package className="w-6 h-6 text-blue-700" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-semibold text-gray-700">Total Orders</p>
                <p className="text-2xl font-bold text-gray-900">{orders.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200 hover:shadow-md transition-shadow">
            <div className="flex items-center">
              <div className="p-3 bg-amber-50 rounded-lg border border-amber-100">
                <Clock className="w-6 h-6 text-amber-700" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-semibold text-gray-700">Pending</p>
                <p className="text-2xl font-bold text-amber-700">
                  {orders.filter(o => ['PENDING', 'CONFIRMED', 'PROCESSING'].includes(o.status)).length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200 hover:shadow-md transition-shadow">
            <div className="flex items-center">
              <div className="p-3 bg-emerald-50 rounded-lg border border-emerald-100">
                <Check className="w-6 h-6 text-emerald-700" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-semibold text-gray-700">Delivered</p>
                <p className="text-2xl font-bold text-emerald-700">
                  {orders.filter(o => o.status === 'DELIVERED').length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200 hover:shadow-md transition-shadow">
            <div className="flex items-center">
              <div className="p-3 bg-violet-50 rounded-lg border border-violet-100">
                <IndianRupee className="w-6 h-6 text-violet-700" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-semibold text-gray-700">Total Revenue</p>
                <p className="text-2xl font-bold text-violet-700">
                  ₹{orders.reduce((sum, order) => sum + order.total, 0).toLocaleString()}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Filters and Search */}
        <div className="bg-white rounded-xl shadow-sm p-6 mb-6 border border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-bold text-gray-900">Filters & Search</h3>
            <button
              onClick={() => {
                setSearchTerm('');
                setStatusFilter('ALL');
                setPaymentFilter('ALL');
              }}
              className="text-sm text-blue-700 hover:text-blue-900 font-semibold bg-blue-50 px-3 py-1 rounded-lg hover:bg-blue-100 transition-colors"
            >
              Clear All
            </button>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-500 w-4 h-4" />
              <input
                type="text"
                placeholder="Search orders, users, emails..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-slate-800 font-medium placeholder-slate-400 bg-white"
              />
            </div>

            {/* Status Filter */}
            <div className="relative">
              <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-500 w-4 h-4" />
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none text-slate-800 font-medium bg-white"
              >
                <option value="ALL" className="text-slate-700">All Status</option>
                <option value="PENDING" className="text-slate-700">Pending</option>
                <option value="PROCESSING" className="text-slate-700">Processing</option>
                <option value="SHIPPED" className="text-slate-700">Shipped</option>
                <option value="DELIVERED" className="text-slate-700">Delivered</option>
                <option value="CANCELLED" className="text-slate-700">Cancelled</option>
              </select>
            </div>

            {/* Payment Filter */}
            <div className="relative">
              <IndianRupee className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-500 w-4 h-4" />
              <select
                value={paymentFilter}
                onChange={(e) => setPaymentFilter(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none text-slate-800 font-medium bg-white"
              >
                <option value="ALL" className="text-slate-700">All Payments</option>
                <option value="PENDING" className="text-slate-700">Payment Pending</option>
                <option value="COMPLETED" className="text-slate-700">Payment Completed</option>
                <option value="FAILED" className="text-slate-700">Payment Failed</option>
                <option value="REFUNDED" className="text-slate-700">Refunded</option>
              </select>
            </div>

            {/* Sort */}
            <div className="relative">
              <select
                value={`${sortBy}-${sortOrder}`}
                onChange={(e) => {
                  const [field, order] = e.target.value.split('-');
                  setSortBy(field);
                  setSortOrder(order as 'asc' | 'desc');
                }}
                className="w-full px-4 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none text-slate-800 font-medium bg-white"
              >
                <option value="createdAt-desc" className="text-slate-700">Newest First</option>
                <option value="createdAt-asc" className="text-slate-700">Oldest First</option>
                <option value="total-desc" className="text-slate-700">Highest Amount</option>
                <option value="total-asc" className="text-slate-700">Lowest Amount</option>
                <option value="orderNumber-asc" className="text-slate-700">Order Number A-Z</option>
                <option value="orderNumber-desc" className="text-slate-700">Order Number Z-A</option>
              </select>
            </div>
          </div>

          {/* Summary Stats */}
          <div className="mt-6 grid grid-cols-2 md:grid-cols-5 gap-4">
            <div className="text-center bg-slate-50 p-3 rounded-lg">
              <div className="text-2xl font-bold text-slate-900">{filteredOrders.length}</div>
              <div className="text-sm font-medium text-slate-600">Total Orders</div>
            </div>
            <div className="text-center bg-amber-50 p-3 rounded-lg">
              <div className="text-2xl font-bold text-amber-700">
                {filteredOrders.filter(o => o.status === 'PENDING').length}
              </div>
              <div className="text-sm font-medium text-amber-600">Pending</div>
            </div>
            <div className="text-center bg-blue-50 p-3 rounded-lg">
              <div className="text-2xl font-bold text-blue-700">
                {filteredOrders.filter(o => o.status === 'PROCESSING').length}
              </div>
              <div className="text-sm font-medium text-blue-600">Processing</div>
            </div>
            <div className="text-center bg-violet-50 p-3 rounded-lg">
              <div className="text-2xl font-bold text-violet-700">
                {filteredOrders.filter(o => o.status === 'SHIPPED').length}
              </div>
              <div className="text-sm font-medium text-violet-600">Shipped</div>
            </div>
            <div className="text-center bg-emerald-50 p-3 rounded-lg">
              <div className="text-2xl font-bold text-emerald-700">
                {filteredOrders.filter(o => o.status === 'DELIVERED').length}
              </div>
              <div className="text-sm font-medium text-emerald-600">Delivered</div>
            </div>
          </div>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="bg-white rounded-xl shadow-sm p-8 text-center border border-gray-200">
            <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-slate-700 font-semibold">Loading orders...</p>
            <p className="text-slate-500 text-sm mt-1">Please wait while we fetch your data</p>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="bg-white rounded-xl shadow-sm p-8 text-center border border-red-200">
            <div className="text-red-700 mb-4">
              <Package className="w-12 h-12 mx-auto mb-2 text-red-500" />
              <p className="font-semibold text-slate-900">Error loading orders</p>
              <p className="text-sm text-red-600 mt-1 font-medium">{error}</p>
            </div>
            <button
              onClick={handleRefresh}
              className="px-6 py-2 bg-red-600 text-white font-semibold rounded-lg hover:bg-red-700 shadow-sm hover:shadow-md transition-all"
            >
              Try Again
            </button>
          </div>
        )}

        {/* Empty State */}
        {!loading && !error && filteredOrders.length === 0 && (
          <div className="bg-white rounded-xl shadow-sm p-8 text-center border border-gray-200">
            <Package className="w-12 h-12 text-slate-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-slate-900 mb-2">No orders found</h3>
            <p className="text-slate-600 mb-4 font-medium">
              {orders.length === 0
                ? "No orders have been placed yet."
                : "No orders match your current filters."
              }
            </p>
            {orders.length > 0 && (
              <button
                onClick={() => {
                  setSearchTerm('');
                  setStatusFilter('ALL');
                  setPaymentFilter('ALL');
                }}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Clear Filters
              </button>
            )}
          </div>
        )}

        {/* Orders List */}
        {!loading && !error && filteredOrders.length > 0 && (
          <div className="space-y-6">
            {filteredOrders.map((order) => (
              <div key={order.id} className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-shadow duration-300">
                {/* Order Header */}
                <div className="px-6 py-5 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <input
                        type="checkbox"
                        checked={selectedOrders.has(order.id)}
                        onChange={() => toggleOrderSelection(order.id)}
                        className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                      />
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="text-lg font-bold text-slate-900">{order.orderNumber}</h3>
                          <span className={`px-3 py-1 rounded-full text-xs font-semibold ${getOrderStatusColor(order.status)}`}>
                            {getOrderStatusText(order.status)}
                          </span>
                        </div>
                        <div className="flex items-center space-x-4 text-sm text-slate-700">
                          <div className="flex items-center">
                            <Calendar className="w-4 h-4 mr-1 text-slate-500" />
                            <span className="font-medium">{new Date(order.createdAt).toLocaleDateString()}</span>
                          </div>
                          <div className="flex items-center">
                            <User className="w-4 h-4 mr-1 text-slate-500" />
                            <span className="font-medium">{order.shippingAddress?.name || 'N/A'}</span>
                          </div>
                          <span className={`px-2 py-1 rounded-full text-xs font-semibold ${getPaymentStatusColor(order.paymentStatus)}`}>
                            {order.paymentStatus}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      <div className="text-right">
                        <div className="flex items-center justify-end space-x-1">
                          <IndianRupee className="w-5 h-5 text-emerald-700" />
                          <p className="text-xl font-bold text-emerald-700">
                            {order.total.toLocaleString()}
                          </p>
                        </div>
                        <p className="text-sm font-medium text-slate-600">{order.items.length} items</p>
                      </div>
                      <button
                        onClick={() => toggleOrderExpansion(order.id)}
                        className="p-3 text-gray-400 hover:text-gray-600 hover:bg-gray-200 rounded-full transition-all duration-200"
                      >
                        {expandedOrders.has(order.id) ? (
                          <ChevronUp className="w-5 h-5" />
                        ) : (
                          <ChevronDown className="w-5 h-5" />
                        )}
                      </button>
                    </div>
                  </div>
                </div>

                {/* Expanded Order Details */}
                {expandedOrders.has(order.id) && (
                  <div className="border-t border-gray-200">
                    {/* Customer Information */}
                    <div className="px-6 py-4 bg-slate-50">
                      <h4 className="font-semibold text-slate-900 mb-3 flex items-center">
                        <User className="w-5 h-5 mr-2 text-slate-600" />
                        Customer Information
                      </h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-slate-600 font-medium">Name:</span>
                          <span className="ml-2 font-semibold text-slate-800">{order.shippingAddress?.name || 'N/A'}</span>
                        </div>
                        <div>
                          <span className="text-slate-600 font-medium">Email:</span>
                          <span className="ml-2 font-semibold text-slate-800">{order.shippingAddress?.email || 'N/A'}</span>
                        </div>
                        <div>
                          <span className="text-slate-600 font-medium">Phone:</span>
                          <span className="ml-2 font-semibold text-slate-800">{order.shippingAddress?.phone || 'N/A'}</span>
                        </div>
                        <div>
                          <span className="text-slate-600 font-medium">User ID:</span>
                          <span className="ml-2 font-mono text-xs text-slate-700 bg-slate-200 px-2 py-1 rounded">{order.userId}</span>
                        </div>
                      </div>
                    </div>

                    {/* Shipping Address */}
                    <div className="px-6 py-4">
                      <h4 className="font-semibold text-slate-900 mb-3 flex items-center">
                        <MapPin className="w-5 h-5 mr-2 text-slate-600" />
                        Shipping Address
                      </h4>
                      <div className="text-sm text-slate-700 bg-slate-50 p-3 rounded-lg">
                        {order.shippingAddress ? (
                          <>
                            <p className="font-semibold text-slate-900">{order.shippingAddress.name}</p>
                            <p className="font-medium">{order.shippingAddress.addressLine1}</p>
                            <p className="font-medium">
                              {order.shippingAddress.city}, {order.shippingAddress.state} - {order.shippingAddress.pincode}
                            </p>
                          </>
                        ) : (
                          <p className="text-slate-500 italic">No shipping address available</p>
                        )}
                      </div>
                    </div>

                    {/* Order Items */}
                    <div className="px-6 py-4 border-t border-gray-200">
                      <h4 className="font-semibold text-slate-900 mb-3 flex items-center">
                        <Package className="w-5 h-5 mr-2 text-slate-600" />
                        Order Items ({order.items.length})
                      </h4>
                      <div className="space-y-3">
                        {order.items.map((item) => (
                          <div key={item.id} className="flex items-center justify-between p-4 bg-slate-50 rounded-lg border border-slate-200">
                            <div className="flex-1">
                              <h5 className="font-semibold text-slate-900">{item.name}</h5>
                              <div className="text-sm text-slate-700 mt-1 space-y-1">
                                <p><span className="font-medium">Quantity:</span> {item.quantity}</p>
                                {item.giftType && <p><span className="font-medium">Gift Type:</span> {item.giftType}</p>}
                                {item.customization && <p><span className="font-medium">Customization:</span> {item.customization}</p>}
                              </div>
                            </div>
                            <div className="text-right">
                              <p className="font-bold text-slate-900 flex items-center justify-end">
                                <IndianRupee className="w-4 h-4 text-emerald-600" />
                                <span className="text-emerald-700">{item.price.toLocaleString()}</span>
                              </p>
                              <p className="text-sm text-slate-600 font-medium">
                                × {item.quantity} = <span className="text-emerald-700 font-semibold">₹{(item.price * item.quantity).toLocaleString()}</span>
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Order Management & Tracking */}
                    <div className="px-6 py-4 border-t border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50 border-l-4 border-l-blue-500">
                      <div className="flex items-center justify-between mb-4">
                        <h4 className="font-semibold text-slate-900 flex items-center">
                          <Truck className="w-5 h-5 mr-2 text-blue-600" />
                          Order Management
                        </h4>
                        {editingOrder !== order.id && (
                          <button
                            onClick={() => startEditing(order)}
                            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-semibold rounded-lg hover:bg-blue-700 shadow-sm hover:shadow-md transition-all"
                          >
                            <Edit className="w-4 h-4 mr-1" />
                            Edit
                          </button>
                        )}
                      </div>

                      {editingOrder === order.id ? (
                        /* Editing Mode */
                        <div className="space-y-4">
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            {/* Status Dropdown */}
                            <div>
                              <label className="block text-sm font-semibold text-slate-800 mb-2">
                                Order Status
                              </label>
                              <select
                                value={editingStatus}
                                onChange={(e) => setEditingStatus(e.target.value)}
                                className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-slate-800 font-medium bg-white"
                              >
                                <option value="PENDING">Pending</option>
                                <option value="CONFIRMED">Confirmed</option>
                                <option value="PROCESSING">Processing</option>
                                <option value="PACKED">Packed</option>
                                <option value="SHIPPED">Shipped</option>
                                <option value="OUT_FOR_DELIVERY">Out for Delivery</option>
                                <option value="DELIVERED">Delivered</option>
                                <option value="CANCELLED">Cancelled</option>
                                <option value="REFUNDED">Refunded</option>
                                <option value="RETURNED">Returned</option>
                              </select>
                            </div>

                            {/* Tracking Number */}
                            <div>
                              <label className="block text-sm font-semibold text-slate-800 mb-2">
                                Tracking Number
                              </label>
                              <input
                                type="text"
                                value={editingTrackingNumber}
                                onChange={(e) => setEditingTrackingNumber(e.target.value)}
                                placeholder="Enter tracking number"
                                className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-slate-800 font-medium placeholder-slate-400"
                              />
                            </div>

                            {/* Estimated Delivery */}
                            <div>
                              <label className="block text-sm font-semibold text-slate-800 mb-2">
                                Estimated Delivery
                              </label>
                              <input
                                type="date"
                                value={editingEstimatedDelivery}
                                onChange={(e) => setEditingEstimatedDelivery(e.target.value)}
                                className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-slate-800 font-medium"
                              />
                            </div>
                          </div>

                          {/* Action Buttons */}
                          <div className="flex items-center space-x-3 pt-4">
                            <button
                              onClick={() => saveOrderChanges(order.id)}
                              disabled={savingOrder === order.id}
                              className="inline-flex items-center px-6 py-2 bg-emerald-600 text-white text-sm font-semibold rounded-lg hover:bg-emerald-700 disabled:opacity-50 shadow-sm hover:shadow-md transition-all"
                            >
                              {savingOrder === order.id ? (
                                <>
                                  <Clock className="w-4 h-4 mr-2 animate-spin" />
                                  Saving...
                                </>
                              ) : (
                                <>
                                  <Save className="w-4 h-4 mr-2" />
                                  Save Changes
                                </>
                              )}
                            </button>
                            <button
                              onClick={cancelEditing}
                              disabled={savingOrder === order.id}
                              className="inline-flex items-center px-6 py-2 bg-slate-500 text-white text-sm font-semibold rounded-lg hover:bg-slate-600 disabled:opacity-50 shadow-sm hover:shadow-md transition-all"
                            >
                              <X className="w-4 h-4 mr-2" />
                              Cancel
                            </button>
                          </div>
                        </div>
                      ) : (
                        /* View Mode */
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                          <div className="flex items-center space-x-2">
                            <span className="text-slate-700 font-semibold">Status:</span>
                            <span className={`px-3 py-1 rounded-full text-xs font-semibold ${getOrderStatusColor(order.status)}`}>
                              {getOrderStatusText(order.status)}
                            </span>
                          </div>
                          <div>
                            <span className="text-slate-700 font-semibold">Tracking:</span>
                            <span className="ml-2 font-mono text-blue-700 bg-blue-50 px-2 py-1 rounded">
                              {order.trackingNumber || 'Not assigned'}
                            </span>
                          </div>
                          <div>
                            <span className="text-slate-700 font-semibold">Est. Delivery:</span>
                            <span className="ml-2 font-semibold text-slate-800">
                              {order.estimatedDelivery
                                ? new Date(order.estimatedDelivery).toLocaleDateString()
                                : 'Not set'
                              }
                            </span>
                          </div>
                          {order.deliveredAt && (
                            <div className="md:col-span-3">
                              <span className="text-slate-700 font-semibold">Delivered:</span>
                              <span className="ml-2 font-semibold text-emerald-700 bg-emerald-50 px-2 py-1 rounded">
                                {new Date(order.deliveredAt).toLocaleDateString()}
                              </span>
                            </div>
                          )}
                        </div>
                      )}
                    </div>

                    {/* Quick Actions */}
                    <div className="px-6 py-3 border-t border-gray-200 bg-slate-50">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <Link
                            href={`/dashboard/track-order?orderNumber=${order.orderNumber}`}
                            className="inline-flex items-center px-3 py-1.5 text-sm font-semibold text-blue-700 hover:text-blue-900 hover:bg-blue-100 rounded-lg transition-colors"
                          >
                            <Eye className="w-4 h-4 mr-1" />
                            View Tracking
                          </Link>
                          <button
                            onClick={() => {
                              navigator.clipboard.writeText(order.orderNumber);
                              toast.success('Order number copied!');
                            }}
                            className="inline-flex items-center px-3 py-1.5 text-sm font-semibold text-slate-700 hover:text-slate-900 hover:bg-slate-200 rounded-lg transition-colors"
                          >
                            <Package className="w-4 h-4 mr-1" />
                            Copy Order #
                          </button>
                        </div>
                        <div className="text-xs font-medium text-slate-600">
                          Updated: {new Date(order.updatedAt).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
