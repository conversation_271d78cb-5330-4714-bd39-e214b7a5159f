'use client';

import React, { useState } from 'react';
import { Amplify } from 'aws-amplify';
import amplifyconfig from '../../../amplifyconfiguration.json';
import { generateClient } from 'aws-amplify/api';
import { listProducts, createProduct } from '../../../graphql';

// Configure Amplify
Amplify.configure(amplifyconfig);

const client = generateClient();

export default function TestProductsPage() {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const testListProducts = async () => {
    setLoading(true);
    setError(null);
    try {
      console.log('Testing listProducts...');
      const result = await client.graphql({
        query: listProducts,
        variables: {
          limit: 10
        }
      });
      console.log('ListProducts result:', result);
      setResult(result);
    } catch (err) {
      console.error('Error:', err);
      setError(JSON.stringify(err, null, 2));
    } finally {
      setLoading(false);
    }
  };

  const createSampleProduct = async () => {
    setLoading(true);
    setError(null);
    try {
      console.log('Creating sample product...');
      const productInput = {
        name: 'Test Product',
        description: 'A test product created from admin panel',
        price: 1999,
        images: ['/placeholder-product.png'],
        category: 'Test Category',
        inStock: true,
        stockQuantity: 10,
        slug: 'test-product-' + Date.now(),
        isActive: true,
        isFeatured: false
      };

      const result = await client.graphql({
        query: createProduct,
        variables: {
          input: productInput
        }
      });
      console.log('CreateProduct result:', result);
      setResult(result);
    } catch (err) {
      console.error('Error:', err);
      setError(JSON.stringify(err, null, 2));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Test Products API</h1>
      
      <div className="space-y-4 mb-6">
        <button
          onClick={testListProducts}
          disabled={loading}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50"
        >
          {loading ? 'Loading...' : 'Test List Products'}
        </button>
        
        <button
          onClick={createSampleProduct}
          disabled={loading}
          className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 disabled:opacity-50 ml-4"
        >
          {loading ? 'Loading...' : 'Create Sample Product'}
        </button>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <h3 className="font-bold">Error:</h3>
          <pre className="whitespace-pre-wrap text-sm">{error}</pre>
        </div>
      )}

      {result && (
        <div className="bg-gray-100 border border-gray-300 px-4 py-3 rounded">
          <h3 className="font-bold mb-2">Result:</h3>
          <pre className="whitespace-pre-wrap text-sm overflow-auto max-h-96">
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}
