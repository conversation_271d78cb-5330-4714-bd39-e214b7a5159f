'use client';

import React, { useState } from 'react';
import { Play, CheckCircle, XCircle, RefreshCw } from 'lucide-react';
import { adminApi } from '../../../services/adminApi';
import type { ProductInput } from '../../../services/adminApi';

export default function TestSingleProductPage() {
  const [isImporting, setIsImporting] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  // Single test product with proper JSON specifications
  const testProduct: ProductInput = {
    name: "Test Product - Specifications Fix",
    slug: "test-product-specs-fix",
    description: "Test product to verify specifications field works correctly",
    shortDescription: "Testing the specifications JSON conversion fix",
    price: 999.00,
    originalPrice: 1299.00,
    images: ["/Creative_Canvas.png"],
    category: "test",
    tags: ["test", "specifications", "fix"],
    inStock: true,
    stockQuantity: 5,
    sku: "TEST-SPEC-001",
    weight: 1.0,
    dimensions: "10\" x 8\" x 6\"",
    features: ["Test feature 1", "Test feature 2"],
    luxuryFeatures: ["Luxury test feature"],
    budgetFeatures: ["Budget test feature"],
    specifications: JSON.stringify({
      "Box Dimensions": "10\" x 8\" x 6\"",
      "Weight": "1.0 kg",
      "Materials": "Test materials",
      "Origin": "Test origin",
      "Shelf Life": "12 months",
      "Test Field": "This is a test value"
    }),
    materials: ["Test Material 1", "Test Material 2"],
    careInstructions: "Test care instructions",
    warranty: "Test warranty",
    shippingInfo: "Test shipping info",
    returnPolicy: "Test return policy",
    rating: 4.5,
    reviewCount: 10,
    badge: "Test Badge",
    relatedProductIds: [],
    metaTitle: "Test Product - Specifications Fix",
    metaDescription: "Test product to verify specifications field works correctly",
    isActive: true,
    isFeatured: false,
    sortOrder: 999
  };

  const handleTestImport = async () => {
    setIsImporting(true);
    setResult(null);
    setError(null);
    
    try {
      console.log('Testing product import with specifications:', testProduct);
      
      const response = await adminApi.createProduct(testProduct);
      
      if (response.success) {
        setResult(response.data);
        console.log('✅ Test product imported successfully:', response.data);
      } else {
        setError(response.error || 'Failed to import test product');
        console.error('❌ Test product import failed:', response.error);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('❌ Test product import error:', err);
    } finally {
      setIsImporting(false);
    }
  };

  const resetTest = () => {
    setResult(null);
    setError(null);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-3xl mx-auto">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex items-center space-x-3 mb-4">
            <Play className="w-8 h-8 text-blue-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Test Single Product Import</h1>
              <p className="text-gray-600">Test the specifications field fix with a single product</p>
            </div>
          </div>
          
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-blue-600 mt-0.5" />
              <div>
                <h3 className="font-medium text-blue-900">Specifications Fix Test</h3>
                <p className="text-sm text-blue-700 mt-1">
                  This will test importing a single product with properly formatted specifications 
                  to verify the "Variable 'specifications' has an invalid value" error is fixed.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Test Section */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Single Product Test</h2>
          
          {!result && !error && (
            <div className="text-center py-8">
              <Play className="w-16 h-16 text-blue-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Ready to Test</h3>
              <p className="text-gray-600 mb-6">
                Click the button below to test importing a single product with specifications.
              </p>
              <button
                onClick={handleTestImport}
                disabled={isImporting}
                className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 mx-auto"
              >
                {isImporting && <RefreshCw className="w-4 h-4 animate-spin" />}
                <span>{isImporting ? 'Testing...' : 'Test Import'}</span>
              </button>
            </div>
          )}

          {isImporting && (
            <div className="text-center py-8">
              <RefreshCw className="w-16 h-16 text-blue-600 mx-auto mb-4 animate-spin" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Testing Import...</h3>
              <p className="text-gray-600">
                Testing the specifications field fix...
              </p>
            </div>
          )}

          {error && (
            <div className="py-8">
              <div className="text-center mb-6">
                <XCircle className="w-16 h-16 text-red-600 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Test Failed</h3>
              </div>

              <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                <h4 className="font-medium text-red-900 mb-2">Error Details:</h4>
                <p className="text-sm text-red-700">{error}</p>
              </div>

              <div className="flex justify-center">
                <button
                  onClick={resetTest}
                  className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  Try Again
                </button>
              </div>
            </div>
          )}

          {result && (
            <div className="py-8">
              <div className="text-center mb-6">
                <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Test Successful!</h3>
                <p className="text-gray-600">The specifications field fix is working correctly.</p>
              </div>

              <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                <h4 className="font-medium text-green-900 mb-2">✅ Success!</h4>
                <p className="text-sm text-green-700 mb-2">
                  Product imported successfully with ID: <code className="bg-green-100 px-1 rounded">{result.id}</code>
                </p>
                <p className="text-sm text-green-700">
                  The specifications field is now properly formatted as JSON string.
                </p>
              </div>

              <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6">
                <h4 className="font-medium text-gray-900 mb-2">Product Details:</h4>
                <div className="text-sm text-gray-700 space-y-1">
                  <p><strong>Name:</strong> {result.name}</p>
                  <p><strong>Slug:</strong> {result.slug}</p>
                  <p><strong>Price:</strong> ₹{result.price}</p>
                  <p><strong>SKU:</strong> {result.sku}</p>
                  <p><strong>Specifications:</strong> {result.specifications ? 'JSON formatted ✅' : 'Not set'}</p>
                </div>
              </div>

              <div className="flex justify-center space-x-4">
                <button
                  onClick={resetTest}
                  className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  Test Again
                </button>
                <a
                  href="/dashboard/products"
                  className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                >
                  View Products
                </a>
                <a
                  href="/dashboard/test-import"
                  className="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors"
                >
                  Test 3 Products
                </a>
              </div>
            </div>
          )}
        </div>

        {/* Test Product Preview */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mt-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Test Product Details</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="font-medium text-gray-900 mb-2">Basic Info</h3>
              <div className="text-sm text-gray-600 space-y-1">
                <p><strong>Name:</strong> {testProduct.name}</p>
                <p><strong>Price:</strong> ₹{testProduct.price}</p>
                <p><strong>Category:</strong> {testProduct.category}</p>
                <p><strong>SKU:</strong> {testProduct.sku}</p>
              </div>
            </div>
            <div>
              <h3 className="font-medium text-gray-900 mb-2">Specifications (JSON)</h3>
              <div className="text-xs text-gray-600 bg-gray-50 p-2 rounded">
                <pre>{testProduct.specifications}</pre>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
