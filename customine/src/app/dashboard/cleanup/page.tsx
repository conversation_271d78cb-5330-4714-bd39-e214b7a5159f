'use client';

import React, { useState } from 'react';
import { adminApi } from '../../../services/adminApi';
import { cleanupProductImages, analyzeImageUrl } from '../../../utils/imageCleanup';

export default function CleanupPage() {
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<Array<{
    id: string;
    name: string;
    imageCount?: number;
    images?: Array<{
      isDoubleEncoded: boolean;
      isHttpUrl: boolean;
      isRelativePath: boolean;
    }>;
    needsCleanup?: boolean;
    status?: string;
    before?: string[];
    after?: string[];
    error?: string;
  }>>([]);
  const [error, setError] = useState<string | null>(null);

  const analyzeAllProducts = async () => {
    setLoading(true);
    setError(null);
    setResults([]);

    try {
      const response = await adminApi.getProducts();
      
      if (response.success && response.data) {
        const products = response.data.products || [];
        console.log('Analyzing', products.length, 'products...');
        
        const analysisResults = products.map((product: { id: string; name: string; images?: string[] }) => {
          const imageAnalysis = product.images?.map((img: string) => analyzeImageUrl(img)) || [];

          return {
            id: product.id,
            name: product.name,
            imageCount: product.images?.length || 0,
            images: imageAnalysis,
            needsCleanup: imageAnalysis.some((img: { isDoubleEncoded: boolean; isHttpUrl: boolean; isRelativePath: boolean }) =>
              img.isDoubleEncoded || (img.isHttpUrl && !img.isRelativePath))
          };
        });
        
        setResults(analysisResults);
        console.log('Analysis complete:', analysisResults);
      } else {
        setError('Failed to load products: ' + (response.error || 'Unknown error'));
      }
    } catch (err) {
      console.error('Error analyzing products:', err);
      setError('Error analyzing products: ' + (err instanceof Error ? err.message : 'Unknown error'));
    } finally {
      setLoading(false);
    }
  };

  const cleanupAllProducts = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await adminApi.getProducts();
      
      if (response.success && response.data) {
        const products = response.data.products || [];
        console.log('Cleaning up', products.length, 'products...');
        
        let cleanedCount = 0;
        const cleanupResults = [];
        
        for (const product of products) {
          if (product.images && product.images.length > 0) {
            const cleanedProduct = cleanupProductImages(product);
            
            // Check if images were actually changed
            const imagesChanged = JSON.stringify(product.images) !== JSON.stringify(cleanedProduct.images);
            
            if (imagesChanged) {
              console.log(`Updating product ${product.id} with cleaned images...`);
              
              try {
                const updateResponse = await adminApi.updateProduct(product.id, {
                  ...cleanedProduct,
                  // Only update the images field
                  images: cleanedProduct.images
                });
                
                if (updateResponse.success) {
                  cleanedCount++;
                  cleanupResults.push({
                    id: product.id,
                    name: product.name,
                    status: 'success',
                    before: product.images,
                    after: cleanedProduct.images
                  });
                } else {
                  cleanupResults.push({
                    id: product.id,
                    name: product.name,
                    status: 'error',
                    error: updateResponse.error
                  });
                }
              } catch (updateError) {
                cleanupResults.push({
                  id: product.id,
                  name: product.name,
                  status: 'error',
                  error: updateError instanceof Error ? updateError.message : 'Update failed'
                });
              }
            } else {
              cleanupResults.push({
                id: product.id,
                name: product.name,
                status: 'no-change'
              });
            }
          }
        }
        
        setResults(cleanupResults);
        console.log(`Cleanup complete: ${cleanedCount} products updated`);
      } else {
        setError('Failed to load products: ' + (response.error || 'Unknown error'));
      }
    } catch (err) {
      console.error('Error cleaning up products:', err);
      setError('Error cleaning up products: ' + (err instanceof Error ? err.message : 'Unknown error'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Image Cleanup Utility</h1>
      
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
        <h2 className="font-semibold text-yellow-800 mb-2">⚠️ Warning</h2>
        <p className="text-yellow-700">
          This utility will analyze and clean up malformed S3 image URLs in your product database. 
          Always backup your data before running cleanup operations.
        </p>
      </div>
      
      <div className="space-y-4 mb-6">
        <button
          onClick={analyzeAllProducts}
          disabled={loading}
          className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50"
        >
          {loading ? 'Analyzing...' : 'Analyze All Products'}
        </button>
        
        <button
          onClick={cleanupAllProducts}
          disabled={loading}
          className="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 disabled:opacity-50 ml-4"
        >
          {loading ? 'Cleaning...' : 'Cleanup All Products'}
        </button>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <h3 className="font-bold">Error:</h3>
          <p>{error}</p>
        </div>
      )}

      {results.length > 0 && (
        <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
          <div className="px-4 py-3 bg-gray-50 border-b">
            <h3 className="font-semibold">Results ({results.length} products)</h3>
          </div>
          <div className="max-h-96 overflow-y-auto">
            {results.map((result, index) => (
              <div key={index} className="px-4 py-3 border-b border-gray-100">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">{result.name || result.id}</p>
                    {result.imageCount !== undefined && (
                      <p className="text-sm text-gray-500">
                        {result.imageCount} images
                        {result.needsCleanup && <span className="text-red-500 ml-2">• Needs cleanup</span>}
                      </p>
                    )}
                    {result.status && (
                      <p className={`text-sm ${
                        result.status === 'success' ? 'text-green-600' : 
                        result.status === 'error' ? 'text-red-600' : 
                        'text-gray-600'
                      }`}>
                        Status: {result.status}
                      </p>
                    )}
                  </div>
                </div>
                
                {result.images && (
                  <div className="mt-2">
                    <details className="text-sm">
                      <summary className="cursor-pointer text-blue-600">View image analysis</summary>
                      <pre className="mt-2 bg-gray-100 p-2 rounded text-xs overflow-x-auto">
                        {JSON.stringify(result.images, null, 2)}
                      </pre>
                    </details>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
