'use client';

import React, { useState } from 'react';
import { Upload, Download, AlertCircle, CheckCircle, XCircle, RefreshCw } from 'lucide-react';
import { importAllProducts } from '../../../scripts/bulkImportProducts';

interface ImportResult {
  success: boolean;
  imported: number;
  failed: number;
  errors: string[];
}

export default function BulkImportPage() {
  const [isImporting, setIsImporting] = useState(false);
  const [importResult, setImportResult] = useState<ImportResult | null>(null);
  const [showConfirmation, setShowConfirmation] = useState(false);

  const handleBulkImport = async () => {
    setIsImporting(true);
    setImportResult(null);
    
    try {
      const result = await importAllProducts();
      setImportResult(result);
    } catch (error) {
      console.error('Bulk import failed:', error);
      setImportResult({
        success: false,
        imported: 0,
        failed: 1,
        errors: [error instanceof Error ? error.message : 'Unknown error occurred']
      });
    } finally {
      setIsImporting(false);
      setShowConfirmation(false);
    }
  };

  const resetImport = () => {
    setImportResult(null);
    setShowConfirmation(false);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex items-center space-x-3 mb-4">
            <Upload className="w-8 h-8 text-blue-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Bulk Product Import</h1>
              <p className="text-gray-600">Import all static product data into the database</p>
            </div>
          </div>
          
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <AlertCircle className="w-5 h-5 text-blue-600 mt-0.5" />
              <div>
                <h3 className="font-medium text-blue-900">Important Information</h3>
                <p className="text-sm text-blue-700 mt-1">
                  This will import 20 products from the static data into your AWS Amplify database. 
                  Each product includes complete details like images, descriptions, features, and specifications.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Import Section */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Import Products</h2>
          
          {!showConfirmation && !importResult && (
            <div className="text-center py-8">
              <Upload className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Ready to Import Products</h3>
              <p className="text-gray-600 mb-6">
                Click the button below to start importing all 20 products from the static data.
              </p>
              <button
                onClick={() => setShowConfirmation(true)}
                className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium"
              >
                Start Bulk Import
              </button>
            </div>
          )}

          {showConfirmation && (
            <div className="text-center py-8">
              <AlertCircle className="w-16 h-16 text-amber-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Confirm Import</h3>
              <p className="text-gray-600 mb-6">
                Are you sure you want to import all products? This action will create new products in your database.
              </p>
              <div className="flex justify-center space-x-4">
                <button
                  onClick={() => setShowConfirmation(false)}
                  className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleBulkImport}
                  disabled={isImporting}
                  className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                >
                  {isImporting && <RefreshCw className="w-4 h-4 animate-spin" />}
                  <span>{isImporting ? 'Importing...' : 'Confirm Import'}</span>
                </button>
              </div>
            </div>
          )}

          {isImporting && (
            <div className="text-center py-8">
              <RefreshCw className="w-16 h-16 text-blue-600 mx-auto mb-4 animate-spin" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Importing Products...</h3>
              <p className="text-gray-600">
                Please wait while we import all products. This may take a few minutes.
              </p>
            </div>
          )}

          {importResult && (
            <div className="py-8">
              <div className="text-center mb-6">
                {importResult.success ? (
                  <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-4" />
                ) : (
                  <XCircle className="w-16 h-16 text-red-600 mx-auto mb-4" />
                )}
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {importResult.success ? 'Import Completed Successfully!' : 'Import Completed with Errors'}
                </h3>
              </div>

              {/* Import Statistics */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center space-x-3">
                    <CheckCircle className="w-8 h-8 text-green-600" />
                    <div>
                      <p className="text-sm font-medium text-green-900">Successfully Imported</p>
                      <p className="text-2xl font-bold text-green-600">{importResult.imported}</p>
                    </div>
                  </div>
                </div>
                
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <div className="flex items-center space-x-3">
                    <XCircle className="w-8 h-8 text-red-600" />
                    <div>
                      <p className="text-sm font-medium text-red-900">Failed to Import</p>
                      <p className="text-2xl font-bold text-red-600">{importResult.failed}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Error Details */}
              {importResult.errors.length > 0 && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                  <h4 className="font-medium text-red-900 mb-2">Import Errors:</h4>
                  <ul className="text-sm text-red-700 space-y-1">
                    {importResult.errors.map((error, index) => (
                      <li key={index} className="flex items-start space-x-2">
                        <span className="text-red-500">•</span>
                        <span>{error}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex justify-center space-x-4">
                <button
                  onClick={resetImport}
                  className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  Import Again
                </button>
                <a
                  href="/dashboard/products"
                  className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                >
                  View Products
                </a>
              </div>
            </div>
          )}
        </div>

        {/* Product Preview */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Products to be Imported</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[
              { name: "Creative Canvas", category: "Creative", price: "₹2,499" },
              { name: "Green Thumb Haven", category: "Gardening", price: "₹1,899" },
              { name: "Home Sweet Nest", category: "Home", price: "₹2,199" },
              { name: "Raise Your Glass", category: "Celebration", price: "₹3,499" },
              { name: "Brew Bliss", category: "Beverages", price: "₹1,799" },
              { name: "Serene Sanctuary", category: "Wellness", price: "₹2,799" },
              { name: "Snuggle Season", category: "Comfort", price: "₹2,299" },
              { name: "Sunday Feast", category: "Culinary", price: "₹2,899" },
              { name: "Brunch Bliss", category: "Food", price: "₹2,399" },
              { name: "Crunch Time", category: "Snacks", price: "₹1,699" },
              { name: "Zen Zone", category: "Wellness", price: "₹2,599" },
              { name: "Morning Glow", category: "Beauty", price: "₹2,199" },
              { name: "Bubbles Bliss", category: "Bath", price: "₹1,999" },
              { name: "Nestled Nook", category: "Reading", price: "₹2,399" },
              { name: "Page Turners", category: "Books", price: "₹1,899" },
              { name: "Culinary Heartbeat", category: "Cooking", price: "₹2,699" },
              { name: "Workspace Wonders", category: "Office", price: "₹2,199" },
              { name: "Coffee Craze", category: "Beverages", price: "₹2,399" },
              { name: "Spa Serenity", category: "Wellness", price: "₹3,199" },
              { name: "Gourmet Galore", category: "Culinary", price: "₹2,799" }
            ].map((product, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-3">
                <h3 className="font-medium text-gray-900 text-sm">{product.name}</h3>
                <p className="text-xs text-gray-600">{product.category}</p>
                <p className="text-sm font-semibold text-blue-600">{product.price}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
