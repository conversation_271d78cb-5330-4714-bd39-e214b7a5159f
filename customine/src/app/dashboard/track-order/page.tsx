'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { 
  Search, 
  Package, 
  Truck, 
  CheckCircle, 
  Clock, 
  MapPin,
  ArrowLeft,
  Calendar,
  CreditCard,
  Phone,
  Mail
} from 'lucide-react';
import { useOrderTracking, getOrderStatusColor, getPaymentStatusColor, getOrderStatusText } from '../../../hooks/useOrderTracking';
import toast from 'react-hot-toast';

export default function TrackOrderPage() {
  const [orderNumber, setOrderNumber] = useState('');
  const { loading, error, orderData, trackOrder, resetTracking } = useOrderTracking();

  // Check for order number in URL parameters
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      const orderNumberParam = urlParams.get('orderNumber');
      if (orderNumberParam) {
        setOrderNumber(orderNumberParam);
        trackOrder(orderNumberParam);
      }
    }
  }, [trackOrder]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!orderNumber.trim()) {
      toast.error('Please enter an order number');
      return;
    }
    await trackOrder(orderNumber.trim());
  };

  const handleReset = () => {
    setOrderNumber('');
    resetTracking();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusProgress = (status: string) => {
    const statusOrder = ['PENDING', 'CONFIRMED', 'PROCESSING', 'PACKED', 'SHIPPED', 'OUT_FOR_DELIVERY', 'DELIVERED'];
    const currentIndex = statusOrder.indexOf(status);
    return Math.max(0, currentIndex);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Enhanced Header */}
      <div className="bg-white shadow-lg border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-50 rounded-lg flex items-center justify-center border border-blue-100">
                <Truck className="w-6 h-6 text-blue-700" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-slate-900">Track Your Order</h1>
                <p className="text-slate-600 font-medium">Real-time order tracking and delivery updates</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Enhanced Search Form */}
        <div className="bg-white rounded-xl shadow-lg p-8 mb-8 border border-gray-200">
          <div className="text-center mb-6">
            <div className="w-16 h-16 bg-blue-50 rounded-full flex items-center justify-center mx-auto mb-4 border border-blue-100">
              <Package className="w-8 h-8 text-blue-700" />
            </div>
            <h2 className="text-2xl font-bold text-slate-900 mb-2">Enter Your Order Number</h2>
            <p className="text-slate-600 font-medium">Track your order status and delivery information</p>
          </div>

          <form onSubmit={handleSubmit} className="max-w-lg mx-auto">
            <div className="flex space-x-3">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-500" />
                <input
                  type="text"
                  value={orderNumber}
                  onChange={(e) => setOrderNumber(e.target.value)}
                  placeholder="Enter order number (e.g., CUST1234567890ABC)"
                  className="w-full pl-10 pr-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-slate-800 font-medium placeholder-slate-400 bg-white"
                  disabled={loading}
                />
              </div>
              <button
                type="submit"
                disabled={loading}
                className="px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed shadow-sm hover:shadow-md transition-all"
              >
                {loading ? (
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                ) : (
                  'Track Order'
                )}
              </button>
            </div>
          </form>

          {orderData && (
            <div className="mt-6 text-center">
              <button
                onClick={handleReset}
                className="text-blue-700 hover:text-blue-900 text-sm font-semibold bg-blue-50 px-4 py-2 rounded-lg hover:bg-blue-100 transition-colors"
              >
                Track Another Order
              </button>
            </div>
          )}
        </div>

        {/* Enhanced Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-xl p-6 mb-8">
            <div className="flex items-center">
              <div className="text-red-700 text-sm font-semibold">{error}</div>
            </div>
          </div>
        )}

        {/* Order Details */}
        {orderData && (
          <div className="space-y-6">
            {/* Enhanced Order Summary */}
            <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-200">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h3 className="text-xl font-bold text-slate-900">Order #{orderData.orderNumber}</h3>
                  <p className="text-slate-600 font-medium">Placed on {formatDate(orderData.createdAt)}</p>
                </div>
                <div className="text-right space-y-2">
                  <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold ${getOrderStatusColor(orderData.status)}`}>
                    {getOrderStatusText(orderData.status)}
                  </div>
                  <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold ${getPaymentStatusColor(orderData.paymentStatus)}`}>
                    <CreditCard className="w-4 h-4 mr-1" />
                    {orderData.paymentStatus}
                  </div>
                </div>
              </div>

              {/* Enhanced Order Progress */}
              <div className="mb-6 bg-slate-50 p-4 rounded-lg">
                <div className="flex items-center justify-between text-sm text-slate-700 mb-3">
                  <span className="font-semibold">Order Progress</span>
                  <span className="font-semibold text-blue-700">{getOrderStatusText(orderData.status)}</span>
                </div>
                <div className="w-full bg-slate-200 rounded-full h-3">
                  <div
                    className="bg-gradient-to-r from-blue-500 to-blue-600 h-3 rounded-full transition-all duration-500 shadow-sm"
                    style={{ width: `${(getStatusProgress(orderData.status) / 6) * 100}%` }}
                  ></div>
                </div>
                <div className="flex justify-between text-xs text-slate-600 mt-2 font-medium">
                  <span>Pending</span>
                  <span>Processing</span>
                  <span>Shipped</span>
                  <span>Delivered</span>
                </div>
              </div>

              {/* Enhanced Tracking Information */}
              {orderData.trackingNumber && (
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 mb-6 border border-blue-100">
                  <div className="flex items-center space-x-4">
                    <div className="p-3 bg-blue-100 rounded-lg">
                      <Truck className="w-6 h-6 text-blue-700" />
                    </div>
                    <div>
                      <p className="font-semibold text-blue-900">Tracking Number</p>
                      <p className="text-blue-800 font-mono bg-blue-100 px-3 py-1 rounded text-sm">{orderData.trackingNumber}</p>
                    </div>
                  </div>
                  {orderData.estimatedDelivery && (
                    <div className="flex items-center space-x-4 mt-4">
                      <div className="p-3 bg-blue-100 rounded-lg">
                        <Calendar className="w-6 h-6 text-blue-700" />
                      </div>
                      <div>
                        <p className="font-semibold text-blue-900">Estimated Delivery</p>
                        <p className="text-blue-800 font-semibold">{formatDate(orderData.estimatedDelivery)}</p>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Enhanced Delivery Address */}
              {orderData.shippingAddress && (
                <div className="border-t border-slate-200 pt-6">
                  <h4 className="font-semibold text-slate-900 mb-4 flex items-center">
                    <MapPin className="w-5 h-5 mr-2 text-slate-600" />
                    Delivery Address
                  </h4>
                  <div className="bg-slate-50 p-4 rounded-lg">
                    <p className="font-semibold text-slate-900">{orderData.shippingAddress.name || 'N/A'}</p>
                    <p className="text-slate-700 font-medium">{orderData.shippingAddress.addressLine1 || 'N/A'}</p>
                    <p className="text-slate-700 font-medium">
                      {orderData.shippingAddress.city || 'N/A'}, {orderData.shippingAddress.state || 'N/A'} - {orderData.shippingAddress.pincode || 'N/A'}
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* Enhanced Order Items */}
            <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-200">
              <h4 className="font-semibold text-slate-900 mb-4 flex items-center">
                <Package className="w-5 h-5 mr-2 text-slate-600" />
                Order Items
              </h4>
              <div className="space-y-3">
                {orderData.items.map((item, index) => (
                  <div key={index} className="flex items-center justify-between py-4 bg-slate-50 rounded-lg px-4 border border-slate-200">
                    <div className="flex-1">
                      <p className="font-semibold text-slate-900">{item.name}</p>
                      <p className="text-slate-700 font-medium">Quantity: {item.quantity}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-emerald-700">₹{item.price.toLocaleString()}</p>
                    </div>
                  </div>
                ))}
              </div>
              <div className="border-t border-slate-200 pt-6 mt-6 bg-slate-50 p-4 rounded-lg">
                <div className="flex items-center justify-between text-xl font-bold">
                  <span className="text-slate-900">Total Amount</span>
                  <span className="text-emerald-700">₹{orderData.total.toLocaleString()}</span>
                </div>
              </div>
            </div>

            {/* Enhanced Contact Support */}
            <div className="bg-gradient-to-r from-slate-100 to-slate-200 rounded-xl p-8 text-center border border-slate-300">
              <h4 className="font-bold text-slate-900 mb-3 text-lg">Need Help?</h4>
              <p className="text-slate-700 font-medium mb-6">Contact our support team for any queries about your order</p>
              <div className="flex flex-col sm:flex-row items-center justify-center space-y-3 sm:space-y-0 sm:space-x-8">
                <a
                  href="mailto:<EMAIL>"
                  className="flex items-center text-blue-700 hover:text-blue-900 font-semibold bg-blue-50 px-4 py-2 rounded-lg hover:bg-blue-100 transition-colors"
                >
                  <Mail className="w-5 h-5 mr-2" />
                  <EMAIL>
                </a>
                <a
                  href="tel:+919042671801"
                  className="flex items-center text-blue-700 hover:text-blue-900 font-semibold bg-blue-50 px-4 py-2 rounded-lg hover:bg-blue-100 transition-colors"
                >
                  <Phone className="w-5 h-5 mr-2" />
                  +91-90426 71801
                </a>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
