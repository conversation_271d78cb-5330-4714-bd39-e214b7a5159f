'use client';

import React, { useState, useEffect } from 'react';
import { User, MapPin, Building, Settings, Save, Camera } from 'lucide-react';
import { Amplify } from 'aws-amplify';
import { getCurrentUser } from 'aws-amplify/auth';
import { generateClient } from 'aws-amplify/api';
import { uploadData } from 'aws-amplify/storage';
import amplifyconfig from '../../../amplifyconfiguration.json';
import S3Image from '../../../components/S3Image';

// Import GraphQL operations
import { getUserProfile } from '../../../graphql/queries';
import { createUserProfile, updateUserProfile } from '../../../graphql/mutations';

// Configure Amplify
Amplify.configure(amplifyconfig);
const client = generateClient();

interface UserProfile {
  id?: string;
  email: string;
  name: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  alternatePhone?: string;
  address?: string;
  addressLine2?: string;
  city?: string;
  state?: string;
  pincode?: string;
  country?: string;
  landmark?: string;
  dateOfBirth?: string;
  gender?: string;
  maritalStatus?: string;
  anniversary?: string;
  occupation?: string;
  company?: string;
  avatar?: string;
  bio?: string;
  website?: string;
  socialMedia?: Record<string, string>;
  preferences?: Record<string, unknown>;
  communicationPreferences?: Record<string, boolean>;
  language?: string;
  currency?: string;
  timezone?: string;
  businessName?: string;
  businessType?: string;
  gstNumber?: string;
  panNumber?: string;
  businessAddress?: string;
  businessPhone?: string;
  businessEmail?: string;
  tags?: string[];
  notes?: string;
  customerType?: string;
  loyaltyPoints?: number;
  totalSpent?: number;
  isActive?: boolean;
  isVerified?: boolean;
  isVIP?: boolean;
  allowMarketing?: boolean;
  role?: string;
}

export default function AdminProfilePage() {
  const [profile, setProfile] = useState<UserProfile>({
    email: '',
    name: '',
    isActive: true,
    isVerified: false,
    isVIP: false,
    allowMarketing: true,
    role: 'user', // Default role for new profiles
    country: 'India',
    currency: 'INR',
    language: 'English',
    timezone: 'Asia/Kolkata',
    customerType: 'Individual'
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [currentUser, setCurrentUser] = useState<{ userId: string; signInDetails?: { loginId?: string } } | null>(null);
  const [activeTab, setActiveTab] = useState('personal');
  const [avatarFile, setAvatarFile] = useState<File | null>(null);
  const [avatarPreview, setAvatarPreview] = useState<string>('');

  useEffect(() => {
    loadUserProfile();
  }, []);

  const loadUserProfile = async () => {
    try {
      setLoading(true);
      
      // Get current authenticated user
      const user = await getCurrentUser();
      setCurrentUser(user);
      
      // Try to load existing profile
      const result = await client.graphql({
        query: getUserProfile,
        variables: { id: user.userId }
      });
      
      if (result.data?.getUserProfile) {
        const userProfile = result.data.getUserProfile;
        setProfile({
          ...userProfile,
          socialMedia: userProfile.socialMedia ? JSON.parse(userProfile.socialMedia as string) : {},
          preferences: userProfile.preferences ? JSON.parse(userProfile.preferences as string) : {},
          communicationPreferences: userProfile.communicationPreferences ? JSON.parse(userProfile.communicationPreferences as string) : {},
          tags: userProfile.tags || []
        });
        
        if (userProfile.avatar) {
          setAvatarPreview(userProfile.avatar);
        }
      } else {
        // Create default profile with user info
        setProfile(prev => ({
          ...prev,
          id: user.userId,
          email: user.signInDetails?.loginId || '',
          name: user.signInDetails?.loginId || ''
        }));
      }
    } catch (error) {
      console.error('Error loading profile:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string | number | boolean) => {
    setProfile(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Removed unused function


  const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setAvatarFile(file);
      const reader = new FileReader();
      reader.onload = () => {
        setAvatarPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const uploadAvatar = async (): Promise<string | null> => {
    if (!avatarFile) return null;

    try {
      const filename = `avatars/${currentUser.userId}-${Date.now()}-${avatarFile.name}`;
      await uploadData({
        key: filename,
        data: avatarFile,
        options: { contentType: avatarFile.type }
      }).result;

      return filename; // Return S3 key, not URL
    } catch (error) {
      console.error('Error uploading avatar:', error);
      throw error;
    }
  };

  // Upsert function to handle create or update
  const upsertProfile = async (profileData: Record<string, any>) => {
    const userId = currentUser.userId;

    // First, try to check if profile exists
    try {
      const existingProfile = await client.graphql({
        query: getUserProfile,
        variables: { id: userId }
      });

      if (existingProfile.data?.getUserProfile) {
        // Profile exists, update it
        console.log('Profile exists, updating with data:', profileData);
        console.log('Update payload:', {
          id: userId,
          ...profileData
        });
        try {
          const result = await client.graphql({
            query: updateUserProfile,
            variables: {
              input: {
                id: userId,
                ...profileData
              }
            }
          });
          console.log('Update successful:', result);
          return result;
        } catch (updateError: any) {
          console.error('Update error details:', updateError);
          if (updateError.errors) {
            updateError.errors.forEach((err: any, index: number) => {
              console.error(`Update Error ${index + 1}:`, err.message);
              console.error('Error locations:', err.locations);
              console.error('Error path:', err.path);
              console.error('Error extensions:', err.extensions);
            });
          }
          console.error('Update data that failed:', profileData);

          // Extract specific error message for user
          const errorMessage = updateError.errors?.[0]?.message || updateError.message || 'Unknown error';
          throw new Error(`Profile update failed: ${errorMessage}`);
        }
      }
    } catch (error) {
      console.log('Profile does not exist, will create new one');
    }

    // Profile doesn't exist, create it
    console.log('Creating new profile with data:', profileData);
    try {
      return await client.graphql({
        query: createUserProfile,
        variables: {
          input: {
            id: userId,
            ...profileData
          }
        }
      });
    } catch (createError: any) {
      console.error('Create error details:', createError);
      if (createError.errors) {
        createError.errors.forEach((err: any, index: number) => {
          console.error(`Create Error ${index + 1}:`, err.message);
          console.error('Error locations:', err.locations);
          console.error('Error path:', err.path);
          console.error('Error extensions:', err.extensions);
        });
      }
      console.error('Create data that failed:', profileData);

      // Extract specific error message for user
      const errorMessage = createError.errors?.[0]?.message || createError.message || 'Unknown error';
      throw new Error(`Profile creation failed: ${errorMessage}`);
    }
  };

  const saveProfile = async () => {
    try {
      setSaving(true);
      
      // Upload avatar if changed
      let avatarKey = profile.avatar;
      if (avatarFile) {
        avatarKey = await uploadAvatar();
      }
      
      // Build complete profile data with all fields
      const profileData: Record<string, any> = {
        // Required fields
        email: profile.email || '',
        name: profile.name || '',
        isActive: profile.isActive !== undefined ? profile.isActive : true,

        // Personal Information
        firstName: profile.firstName || '',
        lastName: profile.lastName || '',
        gender: profile.gender || '',
        maritalStatus: profile.maritalStatus || '',
        occupation: profile.occupation || '',
        company: profile.company || '',
        bio: profile.bio || '',
        website: profile.website || '',

        // Contact Information
        phone: profile.phone || '',
        alternatePhone: profile.alternatePhone || '',

        // Address Information
        address: profile.address || '',
        addressLine2: profile.addressLine2 || '',
        city: profile.city || '',
        state: profile.state || '',
        pincode: profile.pincode || '',
        country: profile.country || '',
        landmark: profile.landmark || '',

        // Business Information
        businessName: profile.businessName || '',
        businessType: profile.businessType || '',
        gstNumber: profile.gstNumber || '',
        panNumber: profile.panNumber || '',
        businessPhone: profile.businessPhone || '',
        businessEmail: profile.businessEmail || '',
        businessAddress: profile.businessAddress || '',

        // Preferences
        customerType: profile.customerType || '',
        language: profile.language || '',
        currency: profile.currency || '',
        loyaltyPoints: profile.loyaltyPoints || 0,
        totalSpent: profile.totalSpent || 0,

        // Account Settings
        isVerified: profile.isVerified !== undefined ? profile.isVerified : false,
        isVIP: profile.isVIP !== undefined ? profile.isVIP : false,
        allowMarketing: profile.allowMarketing !== undefined ? profile.allowMarketing : false,
        role: profile.role || 'user', // Default role for new profiles
        notes: profile.notes || '',

        // Avatar
        avatar: avatarKey || profile.avatar || ''
      };

      // Handle AWSDate fields separately - only include if they have valid values
      // AWSDate expects YYYY-MM-DD format and cannot be empty string
      if (profile.dateOfBirth && profile.dateOfBirth.trim() && profile.dateOfBirth !== '') {
        // Validate date format (YYYY-MM-DD)
        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
        if (dateRegex.test(profile.dateOfBirth)) {
          profileData.dateOfBirth = profile.dateOfBirth;
        } else {
          console.warn('Invalid dateOfBirth format:', profile.dateOfBirth);
        }
      }

      if (profile.anniversary && profile.anniversary.trim() && profile.anniversary !== '') {
        // Validate date format (YYYY-MM-DD)
        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
        if (dateRegex.test(profile.anniversary)) {
          profileData.anniversary = profile.anniversary;
        } else {
          console.warn('Invalid anniversary format:', profile.anniversary);
        }
      }

      console.log('Complete profile data to save:', profileData);
      console.log('Number of fields being sent:', Object.keys(profileData).length);

      // Use the upsert function to handle create or update
      await upsertProfile(profileData);
      
      alert('Profile saved successfully!');
      await loadUserProfile(); // Reload to get updated data
      
    } catch (error) {
      console.error('Error saving profile:', error);
      alert('Error saving profile: ' + (error instanceof Error ? error.message : 'Unknown error'));
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading profile...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Admin Profile</h1>
              <p className="text-gray-600">Manage your profile information and preferences</p>
            </div>
            <button
              onClick={saveProfile}
              disabled={saving}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center space-x-2"
            >
              <Save className="w-4 h-4" />
              <span>{saving ? 'Saving...' : 'Save Profile'}</span>
            </button>
          </div>
        </div>

        {/* Avatar Section */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center space-x-4">
            <div className="relative">
              {avatarPreview ? (
                <S3Image
                  src={avatarPreview}
                  alt="Profile Avatar"
                  className="w-20 h-20 rounded-full object-cover border-2 border-gray-200"
                />
              ) : (
                <div className="w-20 h-20 rounded-full bg-gray-200 flex items-center justify-center">
                  <User className="w-8 h-8 text-gray-400" />
                </div>
              )}
              <label className="absolute bottom-0 right-0 bg-blue-600 text-white p-1 rounded-full cursor-pointer hover:bg-blue-700">
                <Camera className="w-3 h-3" />
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleAvatarChange}
                  className="hidden"
                />
              </label>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">{profile.name || 'Admin User'}</h3>
              <p className="text-gray-600">{profile.email}</p>
              <p className="text-sm text-gray-500">
                {profile.customerType} • {profile.isVIP ? 'VIP' : 'Regular'} Customer
              </p>
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="px-6 border-b border-gray-200">
          <nav className="flex space-x-8">
            {[
              { id: 'personal', label: 'Personal Info', icon: User },
              { id: 'contact', label: 'Contact & Address', icon: MapPin },
              { id: 'business', label: 'Business Info', icon: Building },
              { id: 'preferences', label: 'Preferences', icon: Settings }
            ].map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                <tab.icon className="w-4 h-4" />
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="px-6 py-6">
          {activeTab === 'personal' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                  <input
                    type="text"
                    value={profile.firstName || ''}
                    onChange={(e) => handleInputChange('firstName', e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter first name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                  <input
                    type="text"
                    value={profile.lastName || ''}
                    onChange={(e) => handleInputChange('lastName', e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter last name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                  <input
                    type="text"
                    value={profile.name || ''}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter full name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
                  <input
                    type="email"
                    value={profile.email || ''}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter email address"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Date of Birth</label>
                  <input
                    type="date"
                    value={profile.dateOfBirth || ''}
                    onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Gender</label>
                  <select
                    value={profile.gender || ''}
                    onChange={(e) => handleInputChange('gender', e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Select Gender</option>
                    <option value="Male">Male</option>
                    <option value="Female">Female</option>
                    <option value="Other">Other</option>
                    <option value="Prefer not to say">Prefer not to say</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Marital Status</label>
                  <select
                    value={profile.maritalStatus || ''}
                    onChange={(e) => handleInputChange('maritalStatus', e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Select Status</option>
                    <option value="Single">Single</option>
                    <option value="Married">Married</option>
                    <option value="Divorced">Divorced</option>
                    <option value="Widowed">Widowed</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Anniversary</label>
                  <input
                    type="date"
                    value={profile.anniversary || ''}
                    onChange={(e) => handleInputChange('anniversary', e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Occupation</label>
                  <input
                    type="text"
                    value={profile.occupation || ''}
                    onChange={(e) => handleInputChange('occupation', e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter occupation"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Company</label>
                  <input
                    type="text"
                    value={profile.company || ''}
                    onChange={(e) => handleInputChange('company', e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter company name"
                  />
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Bio</label>
                <textarea
                  value={profile.bio || ''}
                  onChange={(e) => handleInputChange('bio', e.target.value)}
                  rows={4}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Tell us about yourself..."
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Website</label>
                <input
                  type="url"
                  value={profile.website || ''}
                  onChange={(e) => handleInputChange('website', e.target.value)}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="https://example.com"
                />
              </div>
            </div>
          )}

          {activeTab === 'contact' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                  <input
                    type="tel"
                    value={profile.phone || ''}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter phone number"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Alternate Phone</label>
                  <input
                    type="tel"
                    value={profile.alternatePhone || ''}
                    onChange={(e) => handleInputChange('alternatePhone', e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter alternate phone"
                  />
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Address Line 1</label>
                <input
                  type="text"
                  value={profile.address || ''}
                  onChange={(e) => handleInputChange('address', e.target.value)}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter address line 1"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Address Line 2</label>
                <input
                  type="text"
                  value={profile.addressLine2 || ''}
                  onChange={(e) => handleInputChange('addressLine2', e.target.value)}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter address line 2 (optional)"
                />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">City</label>
                  <input
                    type="text"
                    value={profile.city || ''}
                    onChange={(e) => handleInputChange('city', e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter city"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">State</label>
                  <input
                    type="text"
                    value={profile.state || ''}
                    onChange={(e) => handleInputChange('state', e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter state"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">PIN Code</label>
                  <input
                    type="text"
                    value={profile.pincode || ''}
                    onChange={(e) => handleInputChange('pincode', e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter PIN code"
                  />
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Country</label>
                  <input
                    type="text"
                    value={profile.country || ''}
                    onChange={(e) => handleInputChange('country', e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter country"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Landmark</label>
                  <input
                    type="text"
                    value={profile.landmark || ''}
                    onChange={(e) => handleInputChange('landmark', e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter landmark"
                  />
                </div>
              </div>
            </div>
          )}

          {activeTab === 'business' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Business Name</label>
                  <input
                    type="text"
                    value={profile.businessName || ''}
                    onChange={(e) => handleInputChange('businessName', e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter business name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Business Type</label>
                  <select
                    value={profile.businessType || ''}
                    onChange={(e) => handleInputChange('businessType', e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Select Type</option>
                    <option value="Sole Proprietorship">Sole Proprietorship</option>
                    <option value="Partnership">Partnership</option>
                    <option value="Private Limited">Private Limited</option>
                    <option value="Public Limited">Public Limited</option>
                    <option value="LLP">LLP</option>
                    <option value="NGO">NGO</option>
                    <option value="Other">Other</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">GST Number</label>
                  <input
                    type="text"
                    value={profile.gstNumber || ''}
                    onChange={(e) => handleInputChange('gstNumber', e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="22AAAAA0000A1Z5"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">PAN Number</label>
                  <input
                    type="text"
                    value={profile.panNumber || ''}
                    onChange={(e) => handleInputChange('panNumber', e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="**********"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Business Phone</label>
                  <input
                    type="tel"
                    value={profile.businessPhone || ''}
                    onChange={(e) => handleInputChange('businessPhone', e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter business phone"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Business Email</label>
                  <input
                    type="email"
                    value={profile.businessEmail || ''}
                    onChange={(e) => handleInputChange('businessEmail', e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter business email"
                  />
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Business Address</label>
                <textarea
                  value={profile.businessAddress || ''}
                  onChange={(e) => handleInputChange('businessAddress', e.target.value)}
                  rows={3}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter business address"
                />
              </div>
            </div>
          )}

          {activeTab === 'preferences' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Customer Type</label>
                  <select
                    value={profile.customerType || ''}
                    onChange={(e) => handleInputChange('customerType', e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="Individual">Individual</option>
                    <option value="Corporate">Corporate</option>
                    <option value="VIP">VIP</option>
                    <option value="Wholesale">Wholesale</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Language</label>
                  <select
                    value={profile.language || ''}
                    onChange={(e) => handleInputChange('language', e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="English">English</option>
                    <option value="Hindi">Hindi</option>
                    <option value="Tamil">Tamil</option>
                    <option value="Telugu">Telugu</option>
                    <option value="Kannada">Kannada</option>
                    <option value="Malayalam">Malayalam</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Currency</label>
                  <select
                    value={profile.currency || ''}
                    onChange={(e) => handleInputChange('currency', e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="INR">INR (₹)</option>
                    <option value="USD">USD ($)</option>
                    <option value="EUR">EUR (€)</option>
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Loyalty Points</label>
                  <input
                    type="number"
                    value={profile.loyaltyPoints || 0}
                    onChange={(e) => handleInputChange('loyaltyPoints', parseInt(e.target.value) || 0)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="0"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Total Spent (₹)</label>
                  <input
                    type="number"
                    step="0.01"
                    value={profile.totalSpent || 0}
                    onChange={(e) => handleInputChange('totalSpent', parseFloat(e.target.value) || 0)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="0.00"
                  />
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Account Settings</h3>
                <div className="space-y-3">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={profile.isActive || false}
                      onChange={(e) => handleInputChange('isActive', e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Account Active</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={profile.isVerified || false}
                      onChange={(e) => handleInputChange('isVerified', e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Verified Account</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={profile.isVIP || false}
                      onChange={(e) => handleInputChange('isVIP', e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">VIP Status</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={profile.allowMarketing || false}
                      onChange={(e) => handleInputChange('allowMarketing', e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Allow Marketing Communications</span>
                  </label>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Admin Notes</label>
                <textarea
                  value={profile.notes || ''}
                  onChange={(e) => handleInputChange('notes', e.target.value)}
                  rows={4}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Internal notes about this user..."
                />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
