"use client";

import React, { useState, useEffect } from 'react';
import { Star, Eye, Edit, Save, X } from 'lucide-react';
import { fetchProducts, updateProduct } from '@/utils/productAPI';

interface Product {
  id: string;
  name: string;
  price: number;
  isFeatured: boolean;
  isActive: boolean;
  sortOrder?: number;
  createdAt: string;
  images: string[];
}

export default function FeaturedProductsManagement() {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState<string | null>(null);

  const loadProducts = async () => {
    try {
      setLoading(true);
      const result = await fetchProducts({}, 100); // Fetch more products to manage
      setProducts(result.items);
    } catch (error) {
      console.error('Error loading products:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadProducts();
  }, []);

  const handleToggleFeatured = async (productId: string, currentFeatured: boolean) => {
    try {
      setUpdating(productId);
      
      await updateProduct(productId, {
        isFeatured: !currentFeatured
      });

      // Update local state
      setProducts(prev => prev.map(product => 
        product.id === productId 
          ? { ...product, isFeatured: !currentFeatured }
          : product
      ));

    } catch (error) {
      console.error('Error updating product:', error);
    } finally {
      setUpdating(null);
    }
  };

  const handleUpdateSortOrder = async (productId: string, sortOrder: number) => {
    try {
      setUpdating(productId);
      
      await updateProduct(productId, {
        sortOrder: sortOrder
      });

      // Update local state
      setProducts(prev => prev.map(product => 
        product.id === productId 
          ? { ...product, sortOrder }
          : product
      ));

    } catch (error) {
      console.error('Error updating sort order:', error);
    } finally {
      setUpdating(null);
    }
  };

  const featuredProducts = products.filter(p => p.isFeatured);
  const nonFeaturedProducts = products.filter(p => !p.isFeatured);

  if (loading) {
    return (
      <div className="space-y-6">
        <h1 className="text-3xl font-bold text-gray-900">Featured Products Management</h1>
        <div className="bg-white rounded-lg shadow p-6">
          <div className="animate-pulse space-y-4">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Featured Products Management</h1>
          <p className="text-gray-600 mt-1">Manage which products appear in the "Best Selling Ready-to-Ship" section</p>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
              <Star className="w-6 h-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Featured Products</p>
              <p className="text-2xl font-bold text-gray-900">{featuredProducts.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Eye className="w-6 h-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Showing on Homepage</p>
              <p className="text-2xl font-bold text-gray-900">4</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <Edit className="w-6 h-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Products</p>
              <p className="text-2xl font-bold text-gray-900">{products.length}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Featured Products Section */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">
            Featured Products ({featuredProducts.length})
          </h2>
          <p className="text-sm text-gray-600 mt-1">
            Top 4 products will be shown on homepage based on sort order
          </p>
        </div>

        <div className="divide-y divide-gray-200">
          {featuredProducts
            .sort((a, b) => (a.sortOrder || 999) - (b.sortOrder || 999))
            .map((product, index) => (
            <div key={product.id} className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                    {product.images && product.images.length > 0 ? (
                      <img 
                        src={product.images[0]} 
                        alt={product.name}
                        className="w-full h-full object-cover rounded-lg"
                      />
                    ) : (
                      <span className="text-gray-500 text-xs">No Image</span>
                    )}
                  </div>
                  
                  <div className="flex-1">
                    <h3 className="font-semibold text-gray-900">{product.name}</h3>
                    <p className="text-sm text-gray-600">₹{product.price}</p>
                    <div className="flex items-center space-x-4 mt-1">
                      <span className={`text-xs px-2 py-1 rounded-full ${
                        index < 4 ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {index < 4 ? 'Showing on Homepage' : 'Not in Top 4'}
                      </span>
                      <span className="text-xs text-gray-500">
                        Sort Order: {product.sortOrder || 'Not set'}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="number"
                    placeholder="Sort Order"
                    defaultValue={product.sortOrder || ''}
                    className="w-20 px-2 py-1 border border-gray-300 rounded text-sm"
                    onBlur={(e) => {
                      const value = parseInt(e.target.value);
                      if (!isNaN(value)) {
                        handleUpdateSortOrder(product.id, value);
                      }
                    }}
                  />
                  
                  <button
                    onClick={() => handleToggleFeatured(product.id, product.isFeatured)}
                    disabled={updating === product.id}
                    className="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700 disabled:opacity-50"
                  >
                    {updating === product.id ? '...' : 'Remove Featured'}
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Non-Featured Products */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">
            Other Products ({nonFeaturedProducts.length})
          </h2>
          <p className="text-sm text-gray-600 mt-1">
            Click "Make Featured" to add products to the featured section
          </p>
        </div>

        <div className="divide-y divide-gray-200 max-h-96 overflow-y-auto">
          {nonFeaturedProducts.slice(0, 20).map((product) => (
            <div key={product.id} className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                    {product.images && product.images.length > 0 ? (
                      <img 
                        src={product.images[0]} 
                        alt={product.name}
                        className="w-full h-full object-cover rounded-lg"
                      />
                    ) : (
                      <span className="text-gray-500 text-xs">No Image</span>
                    )}
                  </div>
                  
                  <div>
                    <h3 className="font-medium text-gray-900">{product.name}</h3>
                    <p className="text-sm text-gray-600">₹{product.price}</p>
                  </div>
                </div>

                <button
                  onClick={() => handleToggleFeatured(product.id, product.isFeatured)}
                  disabled={updating === product.id}
                  className="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700 disabled:opacity-50"
                >
                  {updating === product.id ? '...' : 'Make Featured'}
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
