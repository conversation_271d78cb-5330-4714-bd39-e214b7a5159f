"use client";

import React, { useState, useEffect, Suspense } from "react";
import { useSearchParams } from "next/navigation";
import Link from "next/link";
import {
  Heart,
  ShoppingCart,
  Minus,
  Plus,
  Share2,
  Loader2,
  Truck,
  Shield,
  RotateCcw,
  ArrowLeft,
} from "lucide-react";
import { useCart } from "../../context/CartContext";
import { useAuth } from "../../context/AuthContext";
import LoginModal from "@/components/LoginModal";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";
import { fetchProductById } from "../../utils/productAPI";
import { addToCart as trackAddToCart, viewItem } from "../../lib/gtag";
import CustomerReviews from "@/components/CustomerReviews";
import { StarRating } from "@/components/CustomerReviews";

interface Product {
  id: string;
  name: string;
  slug: string;
  price: number;
  originalPrice?: number;
  images: string[];
  category?: string;
  rating?: number;
  reviewCount?: number;
  badge?: string;
  description?: string;
  shortDescription?: string;
  longDescription?: string;
  narration?: string;
  luxuryDescription?: string;
  budgetDescription?: string;
  features?: string[];
  luxuryFeatures?: string[];
  budgetFeatures?: string[];
  specifications?: Record<string, string>;
  inStock?: boolean;
  stockQuantity?: number;
  stockCount?: number;
  shippingInfo?: string;
  returnPolicy?: string;
  sku?: string;
  weight?: string;
  dimensions?: string;
  materials?: string[];
  careInstructions?: string;
  warranty?: string;
  isActive?: boolean;
  isFeatured?: boolean;
}

function ProductPageContent() {
  const searchParams = useSearchParams();
  const productId = searchParams.get("id");
  const router = useRouter();

  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedImage, setSelectedImage] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [activeTab, setActiveTab] = useState("description");
  const [isWishlisted, setIsWishlisted] = useState(false);
  const [activeGiftTab, setActiveGiftTab] = useState("luxury");
  const [loginOpen, setLoginOpen] = useState(false);
  const [addingToCart, setAddingToCart] = useState(false);
  const [pendingAction, setPendingAction] = useState<"cart" | "buy" | null>(
    null
  );

  const { addToCart } = useCart();
  const { isLoggedIn, user } = useAuth();

  // Fetch product data by ID
  useEffect(() => {
    if (!productId) {
      setError("No product ID provided");
      setLoading(false);
      return;
    }

    const fetchProduct = async () => {
      try {
        const productData = await fetchProductById(productId);
        if (productData) {
          setProduct(productData);

          // Track product view
          viewItem({
            currency: "INR",
            value: productData.price,
            items: [
              {
                item_id: productData.id,
                item_name: productData.name,
                category: productData.category || "Gift Box",
                price: productData.price,
              },
            ],
          });
        } else {
          setError("Product not found");
        }
      } catch (err) {
        setError("Failed to load product");
      } finally {
        setLoading(false);
      }
    };

    fetchProduct();
  }, [productId]);

  // Helper function to get product image from API data
  const getProductImage = (
    product: ProductWithImages,
    index: number = 0
  ): string => {
    // For API data (has images array)
    if (product.images && product.images.length > 0) {
      return `https://customine4ae3bb77bc244f68885ca662b9bd1b4e1a04b-dev.s3.ap-south-1.amazonaws.com/public/${
        product.images[index] || product.images[0]
      }`;
    }
    // Fallback to placeholder
    return "/placeholder-image.jpg";
  };

  // Helper functions
  const handleQuantityChange = (change: number) => {
    const newQuantity = quantity + change;
    const maxStock = product?.stockQuantity || product?.stockCount || 999;
    if (newQuantity >= 1 && newQuantity <= maxStock) {
      setQuantity(newQuantity);
    }
  };

  const handleAddToCart = async () => {
    if (!product) return;

    setAddingToCart(true);
    try {
      const currentPrice =
        activeGiftTab === "luxury"
          ? product.price
          : Math.round(product.price * 0.6);

      // Create cart item with correct field names
      const cartItem = {
        productId: product.id, // Changed from 'id' to 'productId'
        name: product.name,
        price: currentPrice,
        image: localImages[0],
        quantity: quantity,
        giftType: activeGiftTab, // Add gift type (luxury/budget)
      };

      console.log("Adding to cart:", cartItem);

      await addToCart(cartItem);

      // Track add to cart event
      trackAddToCart({
        currency: "INR",
        value: currentPrice * quantity,
        items: [
          {
            item_id: product.id,
            item_name: product.name,
            category: product.category || "Gift Box",
            quantity: quantity,
            price: currentPrice,
          },
        ],
      });

      toast.success(
        `${product.name} added to cart! Continue shopping or go to cart.`,
        {
          icon: "🛒",
          duration: 4000,
          style: {
            background: "#10B981",
            color: "#fff",
          },
        }
      );
    } catch (err) {
      console.error("Error adding to cart:", err);
      toast.error("Failed to add to cart. Please try again.");
    } finally {
      setAddingToCart(false);
    }
  };

  const handleBuyNow = async () => {
    if (!product) return;

    try {
      const currentPrice =
        activeGiftTab === "luxury"
          ? product.price
          : Math.round(product.price * 0.6);

      // Create cart item with correct field names
      const cartItem = {
        productId: product.id,
        name: product.name,
        price: currentPrice,
        image: localImages[0],
        quantity: quantity,
        giftType: activeGiftTab,
      };

      console.log("Buy now - adding to cart:", cartItem);

      await addToCart(cartItem);

      // Immediately redirect to checkout (no loading state, no toast)
      router.push("/checkout");
    } catch (err) {
      console.error("Error in buy now:", err);
      toast.error("Failed to proceed to checkout. Please try again.");
    }
  };

  // Get product images
  const localImages =
    product?.images && product.images.length > 0
      ? product.images
      : ["/placeholder-image.jpg"];

  // Calculate discount
  const discount = product?.originalPrice
    ? Math.round(
        ((product.originalPrice - product.price) / product.originalPrice) * 100
      )
    : 0;

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading product details...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error || !product) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center max-w-md mx-auto px-4">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Product Not Found
          </h2>
          <p className="text-gray-600 mb-4">
            {error || "Product not available"}
          </p>
          <div className="space-y-3">
            <Link
              href="/collections/all"
              className="block bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Browse All Products
            </Link>
            <Link
              href="/"
              className="block bg-gray-100 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-200 transition-colors"
            >
              Back to Homepage
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
      {/* Login Modal */}
      <LoginModal
        isOpen={loginOpen}
        onClose={() => setLoginOpen(false)}
        onSuccess={() => {
          setLoginOpen(false);
          setPendingAction(null);
        }}
      />

      {/* Breadcrumb */}
      <div className="bg-white/80 backdrop-blur-sm border-b border-gray-100 sticky top-0 z-40">
        <div className="container mx-auto px-4 py-3">
          <nav className="text-sm">
            <Link
              href="/"
              className="text-gray-600 hover:text-gray-900 transition-colors"
            >
              Home
            </Link>
            <span className="mx-2 text-gray-400">/</span>
            <Link
              href="/collections/all"
              className="text-gray-600 hover:text-gray-900 transition-colors"
            >
              Products
            </Link>
            <span className="mx-2 text-gray-400">/</span>
            <span className="text-gray-900 font-medium">{product.name}</span>
          </nav>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
          {/* Product Images */}
          <div className="space-y-6">
            {/* Main Image */}
            <div className="relative aspect-square overflow-hidden rounded-2xl bg-gradient-to-br from-gray-100 to-gray-200 shadow-2xl group">
              {/* Product Images */}
              {/* eslint-disable-next-line @next/next/no-img-element */}
              <img
                src={getProductImage(product, 0)}
                alt={product.name}
                className="object-cover transition-transform duration-500 group-hover:scale-105"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
              />
              {getProductImage(product, 1) !== getProductImage(product, 0) && (
                /* eslint-disable-next-line @next/next/no-img-element */
                <img
                  src={getProductImage(product, 1)}
                  alt={`${product.name} - alternate view`}
                  className="object-cover opacity-0 transition-all duration-500 group-hover:opacity-100 group-hover:scale-105"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                />
              )}

              {/* Overlay gradient for better badge visibility */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/10 via-transparent to-black/10 pointer-events-none" />

              {product.badge && (
                <div className="absolute top-6 left-6 z-10">
                  <span className="bg-gradient-to-r from-purple-600 to-purple-700 text-white px-4 py-2 text-sm font-bold rounded-full shadow-lg backdrop-blur-sm">
                    {product.badge}
                  </span>
                </div>
              )}
              {discount > 0 && (
                <div className="absolute top-6 right-6 z-10">
                  <span className="bg-gradient-to-r from-red-500 to-red-600 text-white px-4 py-2 text-sm font-bold rounded-full shadow-lg animate-pulse">
                    {discount}% OFF
                  </span>
                </div>
              )}

              {/* Image navigation arrows */}
              {localImages.length > 1 && (
                <>
                  <button
                    onClick={() =>
                      setSelectedImage(
                        selectedImage > 0
                          ? selectedImage - 1
                          : localImages.length - 1
                      )
                    }
                    className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/90 hover:bg-white rounded-full p-3 shadow-xl transition-all duration-300 hover:scale-110 backdrop-blur-sm opacity-0 group-hover:opacity-100"
                  >
                    <svg
                      className="w-6 h-6 text-gray-700"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15 19l-7-7 7-7"
                      />
                    </svg>
                  </button>
                  <button
                    onClick={() =>
                      setSelectedImage(
                        selectedImage < localImages.length - 1
                          ? selectedImage + 1
                          : 0
                      )
                    }
                    className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/90 hover:bg-white rounded-full p-3 shadow-xl transition-all duration-300 hover:scale-110 backdrop-blur-sm opacity-0 group-hover:opacity-100"
                  >
                    <svg
                      className="w-6 h-6 text-gray-700"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 5l7 7-7 7"
                      />
                    </svg>
                  </button>
                </>
              )}

              {/* Image counter */}
              {localImages.length > 1 && (
                <div className="absolute bottom-6 right-6 bg-black/70 text-white px-3 py-1 rounded-full text-sm font-medium backdrop-blur-sm">
                  {selectedImage + 1} / {localImages.length}
                </div>
              )}
            </div>

            {/* Thumbnail Images */}
            <div className="grid grid-cols-4 gap-4">
              {localImages.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedImage(index)}
                  className={`relative aspect-square overflow-hidden rounded-xl border-2 transition-all duration-300 hover:shadow-lg ${
                    selectedImage === index
                      ? "border-gray-900 shadow-lg scale-105 ring-2 ring-gray-900/20"
                      : "border-gray-200 hover:border-gray-400 hover:scale-102"
                  }`}
                >
                  {/* Thumbnail Images */}
                  {/* eslint-disable-next-line @next/next/no-img-element */}
                  <img
                    src={getProductImage(product, 0)}
                    alt={product.name}
                    className="object-cover transition-transform duration-500 group-hover:scale-105"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                  />
                  {getProductImage(product, 1) !==
                    getProductImage(product, 0) && (
                    /* eslint-disable-next-line @next/next/no-img-element */
                    <img
                      src={getProductImage(product, 1)}
                      alt={`${product.name} - alternate view`}
                      className="object-cover opacity-0 transition-all duration-500 group-hover:opacity-100 group-hover:scale-105"
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                    />
                  )}
                  {selectedImage === index && (
                    <div className="absolute inset-0 bg-gray-900/10 pointer-events-none" />
                  )}
                </button>
              ))}
            </div>
          </div>

          {/* Product Info */}
          <div className="space-y-8">
            {/* Header */}
            <div className="space-y-4">
              <div>
                <h1 className="text-4xl font-bold text-gray-900 mb-3 leading-tight">
                  {product.name}
                </h1>
                <p className="text-xl text-gray-600 font-medium leading-relaxed">
                  {product.description}
                </p>
              </div>

              {/* Status and Rating */}
              <div className="flex flex-wrap items-center gap-4">
                <div className="flex items-center space-x-2 px-4 py-2 rounded-full text-sm font-semibold bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border border-green-200">
                  <div className="w-2 h-2 rounded-full bg-green-500 animate-pulse"></div>
                  <span>In Stock</span>
                </div>

                <div className="flex items-center space-x-3">
                  <div className="flex items-center space-x-2 bg-white rounded-full px-4 py-2 shadow-sm border border-gray-200">
                    <StarRating rating={Math.floor(product.rating || 4)} size="md" />
                    <span className="text-sm text-gray-600 font-medium">
                      ({product.reviewCount || 28} reviews)
                    </span>
                  </div>
                  <button
                    onClick={() => setActiveTab("reviews")}
                    className="text-sm text-purple-600 hover:text-purple-700 font-medium underline transition-colors"
                  >
                    Write a Review
                  </button>
                </div>

                <div className="px-4 py-2 bg-gradient-to-r from-emerald-50 to-green-50 rounded-full border border-emerald-200">
                  <span className="text-sm text-emerald-700 font-bold">
                    8 in stock
                  </span>
                </div>
              </div>
            </div>

            {/* Narration Section */}
            <div className="bg-gradient-to-br from-gray-50 to-gray-100/50 p-8 rounded-2xl border border-gray-200/50 shadow-sm">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full flex items-center justify-center">
                  <svg
                    className="w-4 h-4 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M7 8h10m0 0V6a2 2 0 00-2-2H9a2 2 0 00-2 2v2m10 0v10a2 2 0 01-2 2H9a2 2 0 01-2-2V8m10 0H7"
                    />
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-gray-900">Narration</h3>
              </div>
              <p className="text-gray-700 leading-relaxed text-lg">
                {product.narration ||
                  "Celebrate life's special moments with sparkling delights and elegant toasts."}
              </p>
            </div>

            {/* Gift Options Tabs */}
            <div className="bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden">
              {/* Tab Headers */}
              <div className="flex bg-gray-50">
                <button
                  onClick={() => setActiveGiftTab("luxury")}
                  className={`flex-1 py-4 px-6 text-center font-medium transition-all duration-200 ${
                    activeGiftTab === "luxury"
                      ? "bg-gray-800 text-white"
                      : "bg-gray-50 text-gray-600 hover:text-gray-900"
                  }`}
                >
                  Luxury Collection
                </button>
                <button
                  onClick={() => setActiveGiftTab("budget")}
                  className={`flex-1 py-4 px-6 text-center font-medium transition-all duration-200 ${
                    activeGiftTab === "budget"
                      ? "bg-gray-800 text-white"
                      : "bg-gray-50 text-gray-600 hover:text-gray-900"
                  }`}
                >
                  Budgeted Gift Examples
                </button>
              </div>

              {/* Tab Content */}
              <div className="p-6">
                {activeGiftTab === "luxury" && (
                  <div className="space-y-4">
                    <ul className="space-y-3">
                      {product.luxuryFeatures &&
                      product.luxuryFeatures.length > 0 ? (
                        product.luxuryFeatures.map((feature, index) => (
                          <li key={index} className="flex items-start">
                            <span className="text-blue-500 mr-3 text-sm">
                              •
                            </span>
                            <span className="text-gray-700">{feature}</span>
                          </li>
                        ))
                      ) : (
                        // Fallback content if no luxury features in API
                        <>
                          <li className="flex items-start">
                            <span className="text-blue-500 mr-3 text-sm">
                              •
                            </span>
                            <span className="text-gray-700">
                              Bottle of sparkling juice
                            </span>
                          </li>
                          <li className="flex items-start">
                            <span className="text-blue-500 mr-3 text-sm">
                              •
                            </span>
                            <span className="text-gray-700">
                              Simple glassware set
                            </span>
                          </li>
                          <li className="flex items-start">
                            <span className="text-blue-500 mr-3 text-sm">
                              •
                            </span>
                            <span className="text-gray-700">
                              Box of assorted chocolates
                            </span>
                          </li>
                          <li className="flex items-start">
                            <span className="text-blue-500 mr-3 text-sm">
                              •
                            </span>
                            <span className="text-gray-700">
                              DIY cocktail recipe cards
                            </span>
                          </li>
                        </>
                      )}
                    </ul>

                    <div className="space-y-2 border-t border-gray-200 pt-4 mt-4">
                      <div className="flex justify-between items-center text-sm">
                        <span className="text-gray-600">Same Day Pickup</span>
                        <span className="text-gray-900 font-medium">
                          Available
                        </span>
                      </div>
                      <div className="flex justify-between items-center text-sm">
                        <span className="text-gray-600">Express Delivery</span>
                        <span className="text-gray-900 font-medium">₹150</span>
                      </div>
                    </div>
                  </div>
                )}

                {activeGiftTab === "budget" && (
                  <div className="space-y-4">
                    <ul className="space-y-3">
                      {product.budgetFeatures &&
                      product.budgetFeatures.length > 0 ? (
                        product.budgetFeatures.map((feature, index) => (
                          <li key={index} className="flex items-start">
                            <span className="text-blue-500 mr-3 text-sm">
                              •
                            </span>
                            <span className="text-gray-700">{feature}</span>
                          </li>
                        ))
                      ) : (
                        // Fallback content if no budget features in API
                        <>
                          <li className="flex items-start">
                            <span className="text-blue-500 mr-3 text-sm">
                              •
                            </span>
                            <span className="text-gray-700">
                              Bottle of sparkling juice
                            </span>
                          </li>
                          <li className="flex items-start">
                            <span className="text-blue-500 mr-3 text-sm">
                              •
                            </span>
                            <span className="text-gray-700">
                              Simple glassware set
                            </span>
                          </li>
                          <li className="flex items-start">
                            <span className="text-blue-500 mr-3 text-sm">
                              •
                            </span>
                            <span className="text-gray-700">
                              Box of assorted chocolates
                            </span>
                          </li>
                          <li className="flex items-start">
                            <span className="text-blue-500 mr-3 text-sm">
                              •
                            </span>
                            <span className="text-gray-700">
                              DIY cocktail recipe cards
                            </span>
                          </li>
                        </>
                      )}
                    </ul>

                    <div className="space-y-2 border-t border-gray-200 pt-4 mt-4">
                      <div className="flex justify-between items-center text-sm">
                        <span className="text-gray-600">Same Day Pickup</span>
                        <span className="text-gray-900 font-medium">
                          Available
                        </span>
                      </div>
                      <div className="flex justify-between items-center text-sm">
                        <span className="text-gray-600">Express Delivery</span>
                        <span className="text-gray-900 font-medium">₹150</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Price and Quantity */}
            <div className="bg-white p-6 rounded-2xl border border-gray-200/50 shadow-sm">
              <div className="flex flex-wrap items-center gap-4 mb-6">
                <div className="flex items-center space-x-3">
                  <span className="text-4xl font-bold text-gray-900">
                    ₹
                    {activeGiftTab === "luxury"
                      ? product.price.toLocaleString()
                      : Math.round(product.price * 0.6).toLocaleString()}
                  </span>
                  {product.originalPrice && (
                    <span className="text-2xl text-gray-500 line-through">
                      ₹{product.originalPrice.toLocaleString()}
                    </span>
                  )}
                </div>
                {discount > 0 && (
                  <div className="bg-gradient-to-r from-green-100 to-emerald-100 px-4 py-2 rounded-full border border-green-200">
                    <span className="text-lg text-green-700 font-bold">
                      Save ₹
                      {(
                        product.originalPrice! - product.price
                      ).toLocaleString()}
                    </span>
                  </div>
                )}
              </div>

              <div className="flex flex-wrap items-center gap-6 mb-8">
                <div className="flex items-center space-x-3">
                  <span className="text-gray-700 font-medium">Quantity:</span>
                  <div className="flex items-center border-2 border-gray-200 rounded-xl overflow-hidden bg-gray-50">
                    <button
                      onClick={() => handleQuantityChange(-1)}
                      disabled={quantity <= 1}
                      className="p-3 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed text-gray-600 transition-colors"
                    >
                      <Minus className="w-4 h-4" />
                    </button>
                    <span className="px-6 py-3 text-center min-w-[60px] font-bold text-gray-900 bg-white">
                      {quantity}
                    </span>
                    <button
                      onClick={() => handleQuantityChange(1)}
                      disabled={
                        quantity >=
                        (product.stockQuantity || product.stockCount || 8)
                      }
                      className="p-3 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed text-gray-600 transition-colors"
                    >
                      <Plus className="w-4 h-4" />
                    </button>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <button
                    onClick={() => setIsWishlisted(!isWishlisted)}
                    className={`p-3 border rounded-lg transition-all duration-200 ${
                      isWishlisted
                        ? "text-red-500 border-red-300 bg-red-50"
                        : "text-gray-400 border-gray-300 hover:border-red-300 hover:text-red-400"
                    }`}
                  >
                    <Heart
                      className={
                        isWishlisted ? "w-5 h-5 fill-current" : "w-5 h-5"
                      }
                    />
                  </button>

                  <button className="p-3 border border-gray-300 rounded-lg text-gray-400 hover:border-gray-400 hover:text-gray-600 transition-all duration-200">
                    <Share2 className="w-5 h-5" />
                  </button>
                </div>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <button
                  onClick={handleAddToCart}
                  disabled={!product.inStock || addingToCart}
                  className="bg-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-blue-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
                >
                  {addingToCart ? (
                    <>
                      <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      <span>Adding...</span>
                    </>
                  ) : (
                    <>
                      <ShoppingCart className="w-5 h-5" />
                      <span>Add to Cart</span>
                    </>
                  )}
                </button>

                <button
                  onClick={handleBuyNow}
                  disabled={!product.inStock}
                  className="bg-[var(--color-navy)] text-white py-3 px-6 rounded-lg font-medium hover:bg-[var(--color-teal)] transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2 shadow-lg hover:shadow-xl"
                >
                  <span>Buy Now</span>
                </button>
              </div>
            </div>

            {/* Features */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
              <div className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
                <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                  <Truck className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Free Shipping</h4>
                  <p className="text-sm text-gray-600">Orders above ₹2000</p>
                </div>
              </div>

              <div className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
                <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                  <Shield className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Quality Assured</h4>
                  <p className="text-sm text-gray-600">Premium products</p>
                </div>
              </div>

              <div className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
                <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                  <RotateCcw className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Easy Returns</h4>
                  <p className="text-sm text-gray-600">30-day policy</p>
                </div>
              </div>
            </div>

            {/* Links */}
            <div className="mt-6 flex flex-wrap justify-center gap-6 text-sm">
              <Link
                href="/shipping"
                className="text-blue-600 hover:text-blue-800 hover:underline transition-colors font-medium"
              >
                How Shipping Works
              </Link>
              <Link
                href="/returns"
                className="text-blue-600 hover:text-blue-800 hover:underline transition-colors font-medium"
              >
                Return Policy
              </Link>
              <Link
                href="/pickup"
                className="text-blue-600 hover:text-blue-800 hover:underline transition-colors font-medium"
              >
                Same Day Pickup
              </Link>
            </div>
            <div className="grid grid-cols-3 gap-4 py-6 border-t border-gray-200">
              <div className="text-center">
                <Truck className="w-6 h-6 text-gray-600 mx-auto mb-2" />
                <p className="text-sm text-gray-600">Free Shipping</p>
                <p className="text-xs text-gray-500">Orders above ₹2000</p>
              </div>
              <div className="text-center">
                <Shield className="w-6 h-6 text-gray-600 mx-auto mb-2" />
                <p className="text-sm text-gray-600">Quality Assured</p>
                <p className="text-xs text-gray-500">Premium products</p>
              </div>
              <div className="text-center">
                <RotateCcw className="w-6 h-6 text-gray-600 mx-auto mb-2" />
                <p className="text-sm text-gray-600">Easy Returns</p>
                <p className="text-xs text-gray-500">30-day policy</p>
              </div>
            </div>
          </div>
        </div>
        {/* Product Details - Tab View */}
        <div className="mt-16 w-full">
          {/* Tab Navigation */}
          <div className="w-full bg-white rounded-t-lg border border-gray-200 border-b-0">
            <div className="flex flex-wrap border-b border-gray-200">
              {[
                { id: "description", label: "Description" },
                { id: "features", label: "What's Included" },
                { id: "specifications", label: "Specifications" },
                { id: "shipping", label: "Shipping & Returns" },
                { id: "reviews", label: "Reviews" },
                { id: "additional", label: "Additional Info" },
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex-1 min-w-0 py-3 px-4 text-center font-medium text-sm transition-all duration-200 ${
                    activeTab === tab.id
                      ? "bg-gray-800 text-white"
                      : "bg-gray-50 text-gray-600 hover:text-gray-900"
                  }`}
                >
                  <span className="hidden sm:block">{tab.label}</span>
                  <span className="sm:hidden text-xs">
                    {tab.label.split(" ")[0]}
                  </span>
                </button>
              ))}
            </div>
          </div>

          {/* Tab Content */}
          <div className="w-full bg-white rounded-b-lg border border-gray-200 border-t-0 min-h-[600px]">
            <div className="p-6">
              {/* Description Tab */}
              {activeTab === "description" && (
                <div className="space-y-6">
                  {/* Narration */}
                  {product.narration && (
                    <div className="bg-gray-50 p-6 rounded-lg border border-gray-200">
                      <h4 className="text-lg font-medium text-gray-900 mb-3">
                        Narration
                      </h4>
                      <p className="text-gray-700 leading-relaxed">
                        {product.narration}
                      </p>
                    </div>
                  )}

                  {/* Main Description */}
                  <div>
                    <h4 className="text-xl font-bold text-gray-900 mb-4">
                      About This Product
                    </h4>
                    <p className="text-gray-700 leading-relaxed text-lg">
                      {product.longDescription ||
                        product.description ||
                        "Celebrate life's special moments with sparkling delights and elegant toasts."}
                    </p>
                  </div>

                  {/* Luxury & Budget Descriptions */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {product.luxuryDescription && (
                      <div className="bg-gray-50 p-6 rounded-lg border border-gray-200">
                        <h4 className="font-medium text-gray-900 mb-3">
                          Luxury Collection
                        </h4>
                        <p className="text-gray-700 leading-relaxed">
                          {product.luxuryDescription}
                        </p>
                      </div>
                    )}

                    {product.budgetDescription && (
                      <div className="bg-gray-800 text-white p-6 rounded-lg">
                        <h4 className="font-medium mb-3">
                          Budgeted Gift Examples
                        </h4>
                        <p className="text-gray-200 leading-relaxed">
                          {product.budgetDescription}
                        </p>
                      </div>
                    )}
                  </div>

                  {/* Our Promise */}
                  <div className="bg-gray-50 p-6 rounded-lg border border-gray-200">
                    <h4 className="text-lg font-medium text-gray-900 mb-3">
                      Our Promise
                    </h4>
                    <p className="text-gray-700 leading-relaxed">
                      Each Customine gift box is carefully curated to create
                      meaningful moments and lasting memories. We work with
                      artisan makers and small businesses across India to bring
                      you the finest quality products that reflect our
                      commitment to excellence and sustainability.
                    </p>
                  </div>
                </div>
              )}

              {/* What's Included Tab */}
              {activeTab === "features" && (
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Luxury Items */}
                    <div className="bg-gray-50 p-6 rounded-lg border border-gray-200">
                      <h4 className="text-lg font-medium text-gray-900 mb-4">
                        Luxury Collection
                      </h4>
                      <ul className="space-y-3">
                        {product.luxuryFeatures &&
                        product.luxuryFeatures.length > 0 ? (
                          product.luxuryFeatures.map((feature, index) => (
                            <li key={index} className="flex items-start">
                              <span className="text-blue-500 mr-3 text-sm">
                                •
                              </span>
                              <span className="text-gray-700">{feature}</span>
                            </li>
                          ))
                        ) : (
                          // Fallback content if no luxury features in API
                          <>
                            <li className="flex items-start">
                              <span className="text-blue-500 mr-3 text-sm">
                                •
                              </span>
                              <span className="text-gray-700">
                                Bottle of sparkling juice
                              </span>
                            </li>
                            <li className="flex items-start">
                              <span className="text-blue-500 mr-3 text-sm">
                                •
                              </span>
                              <span className="text-gray-700">
                                Simple glassware set
                              </span>
                            </li>
                            <li className="flex items-start">
                              <span className="text-blue-500 mr-3 text-sm">
                                •
                              </span>
                              <span className="text-gray-700">
                                Box of assorted chocolates
                              </span>
                            </li>
                            <li className="flex items-start">
                              <span className="text-blue-500 mr-3 text-sm">
                                •
                              </span>
                              <span className="text-gray-700">
                                DIY cocktail recipe cards
                              </span>
                            </li>
                          </>
                        )}
                      </ul>
                    </div>

                    {/* Budget Items */}
                    <div className="bg-gray-800 text-white p-6 rounded-lg">
                      <h4 className="text-lg font-medium mb-4">
                        Budgeted Gift Examples
                      </h4>
                      <ul className="space-y-3">
                        {product.budgetFeatures &&
                        product.budgetFeatures.length > 0 ? (
                          product.budgetFeatures.map((feature, index) => (
                            <li key={index} className="flex items-start">
                              <span className="text-blue-400 mr-3 text-sm">
                                •
                              </span>
                              <span className="text-gray-200">{feature}</span>
                            </li>
                          ))
                        ) : (
                          // Fallback content if no budget features in API
                          <>
                            <li className="flex items-start">
                              <span className="text-blue-400 mr-3 text-sm">
                                •
                              </span>
                              <span className="text-gray-200">
                                Bottle of sparkling juice
                              </span>
                            </li>
                            <li className="flex items-start">
                              <span className="text-blue-400 mr-3 text-sm">
                                •
                              </span>
                              <span className="text-gray-200">
                                Simple glassware set
                              </span>
                            </li>
                            <li className="flex items-start">
                              <span className="text-blue-400 mr-3 text-sm">
                                •
                              </span>
                              <span className="text-gray-200">
                                Box of assorted chocolates
                              </span>
                            </li>
                            <li className="flex items-start">
                              <span className="text-blue-400 mr-3 text-sm">
                                •
                              </span>
                              <span className="text-gray-200">
                                DIY cocktail recipe cards
                              </span>
                            </li>
                          </>
                        )}
                      </ul>
                    </div>
                  </div>

                  {/* Packaging Info */}
                  <div className="bg-gray-50 p-6 rounded-lg border border-gray-200 mt-6">
                    <h4 className="text-lg font-medium text-gray-900 mb-3">
                      Beautiful Packaging
                    </h4>
                    <p className="text-gray-700 leading-relaxed">
                      All items come beautifully packaged in our signature gift
                      box with premium wrapping, ribbon, and a personalized
                      message card. Perfect for gifting or treating yourself.
                    </p>
                  </div>
                </div>
              )}

              {/* Specifications Tab */}
              {activeTab === "specifications" && (
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    {/* Product Details */}
                    <div className="space-y-4">
                      <h4 className="text-xl font-bold text-gray-900 mb-4">
                        Product Details
                      </h4>
                      <div className="space-y-3">
                        <div className="flex justify-between items-center py-2 border-b border-gray-100">
                          <span className="font-medium text-gray-600">
                            SKU:
                          </span>
                          <span className="text-gray-900">
                            {product.sku ||
                              `CUST-${
                                product.category
                                  ?.substring(0, 3)
                                  .toUpperCase() || "PRD"
                              }-${String(product.id)
                                .substring(0, 3)
                                .toUpperCase()}`}
                          </span>
                        </div>
                        <div className="flex justify-between items-center py-2 border-b border-gray-100">
                          <span className="font-medium text-gray-600">
                            Weight:
                          </span>
                          <span className="text-gray-900">
                            {product.weight || "2.5 kg"}
                          </span>
                        </div>
                        <div className="flex justify-between items-center py-2 border-b border-gray-100">
                          <span className="font-medium text-gray-600">
                            Dimensions:
                          </span>
                          <span className="text-gray-900">
                            {product.dimensions || "30 x 25 x 15 cm"}
                          </span>
                        </div>
                        <div className="flex justify-between items-center py-2 border-b border-gray-100">
                          <span className="font-medium text-gray-600">
                            Category:
                          </span>
                          <span className="text-gray-900">
                            {product.category || "Celebration Gifts"}
                          </span>
                        </div>
                        <div className="flex justify-between items-center py-2 border-b border-gray-100">
                          <span className="font-medium text-gray-600">
                            Warranty:
                          </span>
                          <span className="text-gray-900">
                            {product.warranty || "30 days"}
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Materials & Care */}
                    <div className="space-y-4">
                      <h4 className="text-xl font-bold text-gray-900 mb-4">
                        Materials & Care
                      </h4>
                      <div className="space-y-4">
                        <div>
                          <h5 className="font-semibold text-gray-800 mb-2">
                            Materials:
                          </h5>
                          <ul className="space-y-1">
                            {(
                              product.materials || [
                                "Premium glass",
                                "Food-grade packaging",
                                "Eco-friendly materials",
                              ]
                            ).map((material, index) => (
                              <li
                                key={index}
                                className="flex items-center text-gray-700"
                              >
                                <span className="w-2 h-2 bg-indigo-500 rounded-full mr-3"></span>
                                {material}
                              </li>
                            ))}
                          </ul>
                        </div>
                        <div>
                          <h5 className="font-semibold text-gray-800 mb-2">
                            Care Instructions:
                          </h5>
                          <p className="text-gray-700 leading-relaxed">
                            {product.careInstructions ||
                              "Handle with care. Clean glassware with warm soapy water. Store in a cool, dry place away from direct sunlight."}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Additional Specifications */}
                  <div className="bg-gray-50 p-6 rounded-lg border border-gray-200">
                    <h4 className="text-lg font-medium text-gray-900 mb-4">
                      Additional Information
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div className="text-center p-4 bg-white rounded-lg border border-gray-200">
                        <div className="text-2xl font-medium text-gray-900">
                          100%
                        </div>
                        <div className="text-gray-600">Authentic Products</div>
                      </div>
                      <div className="text-center p-4 bg-white rounded-lg border border-gray-200">
                        <div className="text-2xl font-medium text-gray-900">
                          Eco
                        </div>
                        <div className="text-gray-600">Friendly Packaging</div>
                      </div>
                      <div className="text-center p-4 bg-white rounded-lg border border-gray-200">
                        <div className="text-2xl font-medium text-gray-900">
                          24/7
                        </div>
                        <div className="text-gray-600">Customer Support</div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Shipping & Returns Tab */}
              {activeTab === "shipping" && (
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    {/* Shipping Information */}
                    <div className="space-y-6">
                      <h4 className="text-xl font-bold text-gray-900">
                        Shipping Information
                      </h4>

                      <div className="space-y-4">
                        <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                          <h5 className="font-medium text-gray-900 mb-2">
                            Free Shipping
                          </h5>
                          <p className="text-gray-700">
                            On orders above ₹2000 across India
                          </p>
                        </div>

                        {/* API Shipping Information */}
                        {product.shippingInfo ? (
                          <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                            <h5 className="font-medium text-gray-900 mb-2">
                              Shipping Details
                            </h5>
                            <p className="text-gray-700">
                              {product.shippingInfo}
                            </p>
                          </div>
                        ) : (
                          <div className="space-y-3">
                            <div className="flex justify-between items-center py-2 border-b border-gray-100">
                              <span className="font-medium text-gray-600">
                                Standard Delivery:
                              </span>
                              <span className="text-gray-900">
                                3-5 business days
                              </span>
                            </div>
                            <div className="flex justify-between items-center py-2 border-b border-gray-100">
                              <span className="font-medium text-gray-600">
                                Express Delivery:
                              </span>
                              <span className="text-gray-900">
                                1-2 business days
                              </span>
                            </div>
                            <div className="flex justify-between items-center py-2 border-b border-gray-100">
                              <span className="font-medium text-gray-600">
                                Same Day Pickup:
                              </span>
                              <span className="text-gray-900">
                                Available in select cities
                              </span>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Returns Information */}
                    <div className="space-y-6">
                      <h4 className="text-xl font-bold text-gray-900">
                        Returns & Exchanges
                      </h4>

                      <div className="space-y-4">
                        <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                          <h5 className="font-medium text-gray-900 mb-2">
                            30-Day Return Policy
                          </h5>
                          <p className="text-gray-700">
                            {product.returnPolicy ||
                              "Easy returns and exchanges within 30 days"}
                          </p>
                        </div>

                        <ul className="space-y-2">
                          <li className="flex items-start">
                            <svg
                              className="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M5 13l4 4L19 7"
                              />
                            </svg>
                            <span className="text-gray-700">
                              Free return pickup
                            </span>
                          </li>
                          <li className="flex items-start">
                            <svg
                              className="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M5 13l4 4L19 7"
                              />
                            </svg>
                            <span className="text-gray-700">
                              Full refund on unused items
                            </span>
                          </li>
                          <li className="flex items-start">
                            <svg
                              className="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M5 13l4 4L19 7"
                              />
                            </svg>
                            <span className="text-gray-700">
                              Quick processing within 3-5 days
                            </span>
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  {/* Contact Information */}
                  <div className="bg-gradient-to-r from-orange-50 to-red-50 p-6 rounded-xl">
                    <h4 className="text-lg font-bold text-gray-900 mb-4">
                      Need Help?
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="text-center">
                        <div className="text-orange-600 font-bold">Email</div>
                        <div className="text-gray-700"><EMAIL></div>
                      </div>
                      <div className="text-center">
                        <div className="text-orange-600 font-bold">Phone</div>
                        <div className="text-gray-700">+91-90426 71801</div>
                      </div>
                      <div className="text-center">
                        <div className="text-orange-600 font-bold">
                          WhatsApp
                        </div>
                        <div className="text-gray-700">+91-90426 71801</div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Reviews Tab */}
              {activeTab === "reviews" && (
                <div className="space-y-6">
                  <CustomerReviews
                    productId={product.id}
                    productName={product.name}
                  />
                </div>
              )}

              {/* Additional Info Tab */}
              {activeTab === "additional" && (
                <div className="space-y-8">
                  {/* Product Tags */}
                  <div>
                    <h4 className="text-xl font-bold text-gray-900 mb-4">
                      Product Tags
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {(product.tags && product.tags.length > 0
                        ? product.tags
                        : [
                            "Celebration",
                            "Gift Box",
                            "Premium",
                            "Luxury",
                            "Handcrafted",
                            "Eco-Friendly",
                            "Corporate Gifts",
                            "Wedding Gifts",
                          ]
                      ).map((tag, index) => (
                        <span
                          key={index}
                          className="bg-gradient-to-r from-purple-100 to-pink-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium border border-purple-200"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Occasions */}
                  <div>
                    <h4 className="text-xl font-bold text-gray-900 mb-4">
                      Perfect For
                    </h4>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      {[
                        { icon: "🎂", label: "Birthdays" },
                        { icon: "💍", label: "Weddings" },
                        { icon: "🏢", label: "Corporate Events" },
                        { icon: "🎉", label: "Celebrations" },
                        { icon: "🎁", label: "Anniversaries" },
                        { icon: "🏆", label: "Achievements" },
                        { icon: "🎊", label: "Festivals" },
                        { icon: "💝", label: "Special Occasions" },
                      ].map((occasion, index) => (
                        <div
                          key={index}
                          className="text-center p-4 bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl border border-purple-100"
                        >
                          <div className="text-2xl mb-2">{occasion.icon}</div>
                          <div className="text-sm font-medium text-gray-700">
                            {occasion.label}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* FAQ */}
                  <div>
                    <h4 className="text-xl font-bold text-gray-900 mb-4">
                      Frequently Asked Questions
                    </h4>
                    <div className="space-y-4">
                      {[
                        {
                          question:
                            "Can I customize the items in the gift box?",
                          answer:
                            "Yes! We offer customization options for both luxury and budget collections. Contact us to discuss your specific requirements.",
                        },
                        {
                          question: "Is same-day delivery available?",
                          answer:
                            "Same-day pickup is available in select cities. Express delivery (1-2 days) is available across India.",
                        },
                        {
                          question:
                            "Do you offer bulk discounts for corporate orders?",
                          answer:
                            "Yes, we provide special pricing for bulk orders above 10 units. Contact our corporate sales team for a custom quote.",
                        },
                      ].map((faq, index) => (
                        <div key={index} className="bg-gray-50 p-6 rounded-xl">
                          <h5 className="font-semibold text-gray-900 mb-2">
                            {faq.question}
                          </h5>
                          <p className="text-gray-700">{faq.answer}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function ProductPage() {
  return (
    <Suspense
      fallback={
        <div className="min-h-screen bg-white flex items-center justify-center">
          <div className="text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mb-4"></div>
            <p className="text-gray-600">Loading product...</p>
          </div>
        </div>
      }
    >
      <ProductPageContent />
    </Suspense>
  );
}
