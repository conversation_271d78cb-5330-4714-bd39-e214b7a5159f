import { Metadata } from 'next';
import Link from 'next/link';
import { generateBreadcrumbStructuredData } from '@/utils/structuredData';

export const metadata: Metadata = {
  title: "Wedding Gift Boxes India | Elegant Wedding Gifts | Customine",
  description: "Elegant wedding gift boxes for couples, guests, and wedding parties. Thoughtfully curated gifts for Indian weddings with custom packaging and pan-India shipping.",
  keywords: "wedding gifts India, wedding gift boxes, bridal gifts, groom gifts, wedding favors, Indian wedding gifts, custom wedding gifts, elegant wedding gifts",
  openGraph: {
    title: "Wedding Gift Boxes India | Elegant Wedding Gifts | Customine",
    description: "Elegant wedding gift boxes for couples, guests, and wedding parties. Custom packaging available with pan-India shipping.",
    url: "https://customine.in/wedding",
    images: [
      {
        url: "https://customine.in/wedding-gifts-og.jpg",
        width: 1200,
        height: 630,
        alt: "Wedding Gift Boxes by Customine",
      },
    ],
  },
  alternates: {
    canonical: "https://customine.in/wedding",
  },
};

export default function WeddingPage() {
  const breadcrumbStructuredData = generateBreadcrumbStructuredData([
    { name: 'Home', url: 'https://customine.in' },
    { name: 'Wedding Gifts', url: 'https://customine.in/wedding' }
  ]);

  return (
    <>
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(breadcrumbStructuredData) }}
      />

      <div className="min-h-screen bg-gray-50">
        {/* Hero Section */}
        <section className="bg-gradient-to-r from-pink-900 to-purple-700 text-white py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h1 className="text-4xl md:text-6xl font-bold mb-6">
                Wedding Gift Boxes
              </h1>
              <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
                Celebrate love with elegant wedding gift boxes. Perfect for couples, 
                wedding parties, and guests. Make every moment memorable.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  href="/products"
                  className="bg-white text-pink-900 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
                >
                  Browse Wedding Gifts
                </Link>
                <Link
                  href="/pages/special-occasion-inquiry"
                  className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-pink-900 transition-colors"
                >
                  Custom Wedding Gifts
                </Link>
              </div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 className="text-3xl font-bold text-center mb-12">Perfect for Every Wedding Moment</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="bg-pink-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-pink-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold mb-2">For the Couple</h3>
                <p className="text-gray-600">Thoughtful gifts to celebrate the newlyweds and their new journey together.</p>
              </div>
              <div className="text-center">
                <div className="bg-pink-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-pink-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold mb-2">Wedding Party</h3>
                <p className="text-gray-600">Special gifts for bridesmaids, groomsmen, and family members.</p>
              </div>
              <div className="text-center">
                <div className="bg-pink-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-pink-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 15.546c-.523 0-1.046.151-1.5.454a2.704 2.704 0 01-3 0 2.704 2.704 0 00-3 0 2.704 2.704 0 01-3 0 2.704 2.704 0 00-3 0 2.704 2.704 0 01-3 0A2.704 2.704 0 004.5 16c-.523 0-1.046-.151-1.5-.454M9 6v2m3-2v2m3-2v2M9 3h.01M12 3h.01M15 3h.01M21 21v-7a2 2 0 00-2-2H5a2 2 0 00-2 2v7h18zM3 10h18M9 10v8m3-8v8m3-8v8" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold mb-2">Wedding Favors</h3>
                <p className="text-gray-600">Beautiful favors for guests to remember your special day.</p>
              </div>
            </div>
          </div>
        </section>

        {/* Popular Categories */}
        <section className="py-16 bg-gray-100">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 className="text-3xl font-bold text-center mb-12">Popular Wedding Gift Categories</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-white rounded-lg p-6 text-center shadow-sm hover:shadow-md transition-shadow">
                <h3 className="font-semibold mb-2">Home & Living</h3>
                <p className="text-sm text-gray-600">Perfect for setting up their new home together</p>
              </div>
              <div className="bg-white rounded-lg p-6 text-center shadow-sm hover:shadow-md transition-shadow">
                <h3 className="font-semibold mb-2">Luxury Items</h3>
                <p className="text-sm text-gray-600">Premium gifts for special celebrations</p>
              </div>
              <div className="bg-white rounded-lg p-6 text-center shadow-sm hover:shadow-md transition-shadow">
                <h3 className="font-semibold mb-2">Personalized</h3>
                <p className="text-sm text-gray-600">Custom gifts with names and dates</p>
              </div>
              <div className="bg-white rounded-lg p-6 text-center shadow-sm hover:shadow-md transition-shadow">
                <h3 className="font-semibold mb-2">Traditional</h3>
                <p className="text-sm text-gray-600">Classic Indian wedding gifts</p>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="bg-pink-600 text-white py-16">
          <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
            <h2 className="text-3xl font-bold mb-4">Make Their Special Day Even More Special</h2>
            <p className="text-xl mb-8">
              Create custom wedding gift boxes that perfectly capture the joy and love of the occasion.
            </p>
            <Link
              href="/pages/special-occasion-inquiry"
              className="bg-white text-pink-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors inline-block"
            >
              Plan Custom Wedding Gifts
            </Link>
          </div>
        </section>
      </div>
    </>
  );
}
