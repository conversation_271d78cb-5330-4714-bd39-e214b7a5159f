import { Metadata } from 'next';
import HeroSlider from '@/components/HeroSlider';
import ProductGrid from '@/components/ProductGrid';
import Link from 'next/link';

export const metadata: Metadata = {
  title: "Customine | Curated & Custom Gift Boxes in India",
  description: "Thoughtfully designed gift boxes for weddings, corporate events, client appreciation, festive celebrations, and personal milestones. At Customine, we take gifting off your plate and deliver beauty, elegance, and care in every box. Pan-India shipping. Custom options available.",
  keywords: "gift boxes India, custom gift boxes, corporate gifts, wedding gifts, festive gifts, curated gifts, personalized gifts, luxury gift boxes, gift hampers, pan India shipping, thoughtful gifts, elegant gift boxes",
  openGraph: {
    title: "Customine | Curated & Custom Gift Boxes in India",
    description: "Thoughtfully designed gift boxes for weddings, corporate events, client appreciation, festive celebrations, and personal milestones. Pan-India shipping available.",
    url: "https://customine.in",
    images: [
      {
        url: "https://customine.in/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Customine - Curated Gift Boxes India",
      },
    ],
  },
  alternates: {
    canonical: "https://customine.in",
  },
};

export default function Home() {
  return (
    <div className="min-h-screen">
      <main>
        {/* Hero Section */}
        <section className="h-[400px] md:h-[500px]">
          <HeroSlider useGalleryAPI={true} onlyImages={true} rounded={false} />
        </section>

        {/* Why Customine Features Section */}
        <section className="bg-gray-50 border-b border-gray-200 py-6 px-4">
          <div className="container mx-auto">
            <div className="flex items-center justify-center space-x-6 text-base font-medium text-gray-700 overflow-x-auto">
              <div className="flex items-center space-x-2 whitespace-nowrap">
                <span className="bg-gray-900 text-white px-2 py-1 rounded text-xs font-bold">
                  Why Customine
                </span>
              </div>
              <div className="flex items-center space-x-2 whitespace-nowrap">
                <span className="text-lg" style={{ color: '#666666' }}>✓</span>
                <span>Fully Customizable</span>
              </div>
              <div className="flex items-center space-x-2 whitespace-nowrap">
                <span className="text-lg" style={{ color: '#666666' }}>🚚</span>
                <span>Pan-India Delivery</span>
              </div>
              <div className="flex items-center space-x-2 whitespace-nowrap">
                <span className="text-lg" style={{ color: '#666666' }}>👋</span>
                <span>Handcrafted with Care</span>
              </div>
              <div className="flex items-center space-x-2 whitespace-nowrap">
                <span className="text-lg" style={{ color: '#666666' }}>🏷️</span>
                <span>Brand Integration Available</span>
              </div>
            </div>
          </div>
        </section>

        {/* About Customine Section */}
        <section className="py-16 bg-white">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12 mt-[-30px]">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Welcome to Customine
              </h2>
              <p className="text-xl text-gray-700 leading-relaxed max-w-4xl mx-auto mb-8">
                Your modern gifting studio where thoughtful curation meets seamless delivery. We bring joy to recipients and peace of mind to senders through expertly crafted gift experiences.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
                <div>
                  <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-2xl">💜</span>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Bringing Joy</h3>
                  <p className="text-gray-600">Every gift is curated to create memorable moments</p>
                </div>
                <div>
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-2xl">✨</span>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Peace of Mind</h3>
                  <p className="text-gray-600">Seamless delivery and professional service</p>
                </div>
                <div>
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-2xl">⭐</span>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Expert Curation</h3>
                  <p className="text-gray-600">Thoughtfully designed gift experiences</p>
                </div>
              </div>
            </div>
          </div>
        </section>

        <ProductGrid />


        {/* Our Services Overview */}
        <section className="py-16 bg-gray-50">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Complete Gifting Solutions
              </h2>
              <p className="text-lg text-gray-600">
                From everyday celebrations to special occasions, we have the perfect gift for every moment
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">

              {/* Special Occasions */}
              <Link href="/pages/special-occasions" className="group">
                <div className="bg-white rounded-lg p-6 hover:shadow-lg transition-shadow">
                  <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mb-4">
                    <span className="text-xl">🎁</span>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3 group-hover:text-purple-600">
                    Special Occasions
                  </h3>
                  <p className="text-gray-600 text-sm mb-4">
                    Corporate gifting, weddings, events, and milestone celebrations with professional curation
                  </p>
                  <div className="flex items-center text-purple-600 text-sm font-medium">
                    Explore Options →
                  </div>
                </div>
              </Link>

              {/* Everyday Occasions */}
              <Link href="/pages/everyday-occasions" className="group">
                <div className="bg-white rounded-lg p-6 hover:shadow-lg transition-shadow">
                  <div className="w-12 h-12 bg-pink-100 rounded-full flex items-center justify-center mb-4">
                    <span className="text-xl">📅</span>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3 group-hover:text-pink-600">
                    Everyday Occasions
                  </h3>
                  <p className="text-gray-600 text-sm mb-4">
                    Birthdays, housewarmings, festivals, and spontaneous celebrations for life&apos;s special moments
                  </p>
                  <div className="flex items-center text-pink-600 text-sm font-medium">
                    Celebrate in Style →
                  </div>
                </div>
              </Link>

              {/* In-House Products */}
              <Link href="/pages/in-house-products" className="group">
                <div className="bg-white rounded-lg p-6 hover:shadow-lg transition-shadow">
                  <div className="w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center mb-4">
                    <span className="text-xl">🏠</span>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3 group-hover:text-indigo-600">
                    In-House Products
                  </h3>
                  <p className="text-gray-600 text-sm mb-4">
                    Exclusively crafted room & car fresheners and handmade gift sets unavailable online
                  </p>
                  <div className="flex items-center text-indigo-600 text-sm font-medium">
                    Discover Exclusives →
                  </div>
                </div>
              </Link>

              {/* Luxury Cards */}
              <Link href="/pages/luxury-cards" className="group">
                <div className="bg-white rounded-lg p-6 hover:shadow-lg transition-shadow">
                  <div className="w-12 h-12 bg-amber-100 rounded-full flex items-center justify-center mb-4">
                    <span className="text-xl">👑</span>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3 group-hover:text-amber-600">
                    Luxury Cards
                  </h3>
                  <p className="text-gray-600 text-sm mb-4">
                    Where words meet elegance - boutique-style cards for weddings, proposals, and special messages
                  </p>
                  <div className="flex items-center text-amber-600 text-sm font-medium">
                    View Collection →
                  </div>
                </div>
              </Link>

              {/* Seasonal Gifting */}
              <Link href="/pages/diwali-gifting" className="group">
                <div className="bg-white rounded-lg p-6 hover:shadow-lg transition-shadow">
                  <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mb-4">
                    <span className="text-xl">🎄</span>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3 group-hover:text-orange-600">
                    Seasonal Gifting
                  </h3>
                  <p className="text-gray-600 text-sm mb-4">
                    Diwali, Christmas, New Year, and Valentine&apos;s Day collections for festive celebrations
                  </p>
                  <div className="flex items-center text-orange-600 text-sm font-medium">
                    Shop Seasonal →
                  </div>
                </div>
              </Link>

              {/* Custom Solutions */}
              <Link href="/pages/add-your-own-logo" className="group">
                <div className="bg-white rounded-lg p-6 hover:shadow-lg transition-shadow">
                  <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
                    <span className="text-xl">⭐</span>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3 group-hover:text-green-600">
                    Custom Solutions
                  </h3>
                  <p className="text-gray-600 text-sm mb-4">
                    Add your branding to ready-to-ship gifts or create fully custom experiences
                  </p>
                  <div className="flex items-center text-green-600 text-sm font-medium">
                    Get Started →
                  </div>
                </div>
              </Link>
            </div>
          </div>
        </section>

        {/* Why Choose Customine */}
        <section className="py-16 bg-white">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Why Choose Customine?
              </h2>
              <p className="text-lg text-gray-600">
                Your trusted partner for memorable gifting experiences
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">💜</span>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Thoughtful Curation</h3>
                <p className="text-gray-600 text-sm">Every item is carefully selected to create meaningful experiences</p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">✨</span>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Seamless Service</h3>
                <p className="text-gray-600 text-sm">From consultation to delivery, we handle every detail professionally</p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">⭐</span>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Exclusive Products</h3>
                <p className="text-gray-600 text-sm">Unique in-house products and limited edition items not found elsewhere</p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-amber-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">👑</span>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Premium Quality</h3>
                <p className="text-gray-600 text-sm">Luxury presentation and high-quality products for lasting impressions</p>
              </div>
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <section
          className="py-20"
          style={{ background: 'var(--background)', color: 'var(--foreground)' }}
        >
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Ready to Create Something Special?
            </h2>
            <p className="text-xl mb-8" style={{ color: 'var(--foreground)' }}>
              Let&apos;s design the perfect gift experience for your occasion
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6">
              <Link
                href="/pages/special-occasion-inquiry"
                className="inline-flex items-center bg-white text-purple-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-200"
              >
                Start Your Inquiry →
              </Link>
              <Link
                href="/collections/all"
                className="inline-flex items-center border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-purple-600 transition-colors duration-200"
                style={{ borderColor: 'var(--foreground)', color: 'var(--foreground)' }}
              >
                Browse Ready-to-Ship 🎁
              </Link>
            </div>
          </div>
        </section>


      </main>
    </div>
  );
}
