@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
  /* Custom palette */
  --color-navy: #2F4156;
  --color-teal: #567C8D;
  --color-sky-blue: #C8D9E6;
  --color-beige: #F5EFEB;
  --color-white: #FFFFFF;
  --color-text-primary: #2F4156;
  --color-text-secondary: #567C8D;
  --color-bg-main: #F5EFEB;
  --color-bg-alt: #C8D9E6;
  --color-bg-card: #FFFFFF;
  --color-gold: #D6BC7E;
  --color-info-link: #2F4156;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
    --color-gold: #F5E7B2;
    --color-info-link: #C8D9E6;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Custom animations for modals */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.4s ease-out;
}

/* User dropdown animation */
@keyframes dropdownSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.user-dropdown-animate {
  animation: dropdownSlideIn 0.2s ease-out;
}

/* Fix for dropdown menus in production */
.dropdown-menu {
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.2s ease-out;
  pointer-events: none;
}

.dropdown-parent:hover .dropdown-menu,
.dropdown-parent:focus-within .dropdown-menu,
.dropdown-menu:hover {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  pointer-events: auto;
}

/* Ensure dropdowns work on touch devices */
@media (hover: none) {
  .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
    pointer-events: auto;
    position: static;
    box-shadow: none;
    background: transparent;
    width: auto;
    margin: 0;
    padding: 0;
  }
}
