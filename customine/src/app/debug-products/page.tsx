'use client';

import React, { useState } from 'react';
import { generateClient } from 'aws-amplify/api';
import { listProducts } from '../../graphql/queries';

const client = generateClient();

export default function DebugProductsPage() {
  const [products, setProducts] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchAllProducts = async () => {
    setLoading(true);
    try {
      const result = await client.graphql({
        query: listProducts,
        variables: { limit: 100 }
      });

      const items = result.data?.listProducts?.items || [];
      setProducts(items);
      
      console.log('📊 All Products:', items);
      
      // Find the specific products
      const creativeCanvas = items.find(p => p.slug === 'creative-canvas');
      const creativeCanvasTest = items.find(p => p.slug === 'creative-canvas-test');
      
      console.log('🎨 creative-canvas:', creativeCanvas);
      console.log('🧪 creative-canvas-test:', creativeCanvasTest);
      
    } catch (error) {
      console.error('❌ Error:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Product Debug Tool</h1>
        
        <button
          onClick={fetchAllProducts}
          disabled={loading}
          className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 disabled:opacity-50 mb-6"
        >
          {loading ? 'Loading...' : 'Fetch All Products'}
        </button>

        {products.length > 0 && (
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Slug
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Active
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Price
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Created By
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Images
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {products.map((product, index) => (
                  <tr 
                    key={product.id} 
                    className={`${
                      product.slug === 'creative-canvas' ? 'bg-green-50' : 
                      product.slug === 'creative-canvas-test' ? 'bg-red-50' : ''
                    }`}
                  >
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {product.name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <code className="bg-gray-100 px-2 py-1 rounded">{product.slug}</code>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        product.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {product.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      ₹{product.price}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <code className="text-xs">{product.createdBy || 'N/A'}</code>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {product.images?.length || 0} images
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {products.length > 0 && (
          <div className="mt-8 bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">Product Comparison</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Creative Canvas */}
              {(() => {
                const product = products.find(p => p.slug === 'creative-canvas');
                return product ? (
                  <div className="border border-green-200 rounded-lg p-4 bg-green-50">
                    <h3 className="font-semibold text-green-800 mb-2">✅ creative-canvas (Working)</h3>
                    <pre className="text-xs overflow-auto bg-white p-2 rounded">
                      {JSON.stringify(product, null, 2)}
                    </pre>
                  </div>
                ) : (
                  <div className="border border-gray-200 rounded-lg p-4">
                    <h3 className="font-semibold text-gray-600 mb-2">creative-canvas (Not Found)</h3>
                  </div>
                );
              })()}

              {/* Creative Canvas Test */}
              {(() => {
                const product = products.find(p => p.slug === 'creative-canvas-test');
                return product ? (
                  <div className="border border-red-200 rounded-lg p-4 bg-red-50">
                    <h3 className="font-semibold text-red-800 mb-2">❌ creative-canvas-test (Not Working)</h3>
                    <pre className="text-xs overflow-auto bg-white p-2 rounded">
                      {JSON.stringify(product, null, 2)}
                    </pre>
                  </div>
                ) : (
                  <div className="border border-gray-200 rounded-lg p-4">
                    <h3 className="font-semibold text-gray-600 mb-2">creative-canvas-test (Not Found)</h3>
                  </div>
                );
              })()}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
