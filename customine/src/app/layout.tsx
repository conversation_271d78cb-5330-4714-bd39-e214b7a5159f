import type { Metadata } from "next";
import "./globals.css";
import Analytics from "@/components/Analytics";
import GoogleAnalytics from "@/components/GoogleAnalytics";
import StructuredData from "@/components/StructuredData";
import { CartProvider } from '../context/CartContext';
import { AuthProvider } from '../context/AuthContext';
import ConditionalLayout from '@/components/ConditionalLayout';
import { Toaster } from 'react-hot-toast';
import NoSSR from '@/components/NoSSR';
import HydrationDebug from '@/components/HydrationDebug';
import StaticExportWrapper from '@/components/StaticExportWrapper';
import NavigationErrorBoundary from '@/components/NavigationErrorBoundary';
import ServiceWorkerRegistration from '@/components/ServiceWorkerRegistration';

export const metadata: Metadata = {
  title: "Customine | Curated & Custom Gift Boxes in India",
  description: "Thoughtfully designed gift boxes for weddings, corporate events, client appreciation, festive celebrations, and personal milestones. At Customine, we take gifting off your plate and deliver beauty, elegance, and care in every box. Pan-India shipping. Custom options available.",
  keywords: "gift boxes India, custom gift boxes, corporate gifts, wedding gifts, festive gifts, curated gifts, personalized gifts, luxury gift boxes, gift hampers, pan India shipping",
  authors: [{ name: "Customine" }],
  creator: "Customine",
  publisher: "Customine",
  robots: "index, follow",
  openGraph: {
    type: "website",
    locale: "en_IN",
    url: "https://customine.in",
    siteName: "Customine",
    title: "Customine | Curated & Custom Gift Boxes in India",
    description: "Thoughtfully designed gift boxes for weddings, corporate events, client appreciation, festive celebrations, and personal milestones. Pan-India shipping available.",
    images: [
      {
        url: "https://customine.in/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Customine - Curated Gift Boxes India",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    site: "@customine",
    creator: "@customine",
    title: "Customine | Curated & Custom Gift Boxes in India",
    description: "Thoughtfully designed gift boxes for weddings, corporate events, client appreciation, festive celebrations, and personal milestones.",
    images: ["https://customine.in/og-image.jpg"],
  },
  alternates: {
    canonical: "https://customine.in",
  },
  category: "E-commerce",
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Customine",
    "url": "https://customine.in",
    "logo": "https://customine.in/logo.png",
    "description": "Thoughtfully designed gift boxes for weddings, corporate events, client appreciation, festive celebrations, and personal milestones in India.",
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "IN"
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "availableLanguage": ["English", "Hindi"]
    },
    "sameAs": [
      "https://www.facebook.com/customine",
      "https://www.instagram.com/customine",
      "https://www.pinterest.com/customine/"
    ],
    "offers": {
      "@type": "Offer",
      "category": "Gift Boxes",
      "areaServed": "IN",
      "availability": "https://schema.org/InStock"
    }
  };

  return (
    <html lang="en-IN">
      <head>
        <link rel="icon" type="image/png" href="/favicon.png" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />
        <meta name="theme-color" content="#2F4156" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="geo.region" content="IN" />
        <meta name="geo.placename" content="India" />
        <meta name="ICBM" content="20.5937, 78.9629" />
        {process.env.NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION && (
          <meta name="google-site-verification" content={process.env.NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION} />
        )}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
        />
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // Initialize navigation fixes as early as possible
              (function() {
                if (typeof window !== 'undefined') {
                  // Override console.error to handle navigation errors
                  const originalError = console.error;
                  console.error = function(...args) {
                    const message = args[0]?.toString() || '';
                    if (message.includes('Failed to fetch RSC payload') ||
                        message.includes('e[o] is not a function')) {
                      console.warn('Navigation error handled:', message);
                      return;
                    }
                    originalError(...args);
                  };

                  // Handle unhandled promise rejections
                  window.addEventListener('unhandledrejection', function(event) {
                    const message = event.reason?.message || '';
                    if (message.includes('RSC payload') || message.includes('e[o] is not a function')) {
                      console.warn('Navigation promise rejection handled:', message);
                      event.preventDefault();
                    }
                  });
                }
              })();
            `
          }}
        />
      </head>
      <body>
        <Analytics
          googleAnalyticsId={process.env.NEXT_PUBLIC_GA_ID}
          // facebookPixelId={process.env.NEXT_PUBLIC_FB_PIXEL_ID}
          // clarityId={process.env.NEXT_PUBLIC_CLARITY_ID}
          // hotjarId={process.env.NEXT_PUBLIC_HOTJAR_ID ? parseInt(process.env.NEXT_PUBLIC_HOTJAR_ID) : undefined}
        />
        <StaticExportWrapper>
          <NavigationErrorBoundary>
            <AuthProvider>
              <CartProvider>
                <ConditionalLayout>
                  {children}
                </ConditionalLayout>
              </CartProvider>
            </AuthProvider>
          </NavigationErrorBoundary>
        </StaticExportWrapper>
        <NoSSR>
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#363636',
                color: '#fff',
              },
              success: {
                duration: 3000,
                style: {
                  background: '#10B981',
                  color: '#fff',
                },
              },
              error: {
                duration: 5000,
                style: {
                  background: '#EF4444',
                  color: '#fff',
                },
              },
            }}
          />
        </NoSSR>
        <GoogleAnalytics />
        <StructuredData type="organization" />
        <StructuredData type="local-business" />
        <StructuredData type="website" />
        <ServiceWorkerRegistration />
        <HydrationDebug />
      </body>
    </html>
  );
}
