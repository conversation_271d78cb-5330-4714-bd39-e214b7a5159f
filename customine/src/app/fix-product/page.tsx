'use client';

import React, { useState } from 'react';
import { generateClient } from 'aws-amplify/api';
import { listProducts, updateProduct } from '../../graphql/queries';

const client = generateClient();

export default function FixProductPage() {
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const fixProduct = async () => {
    setLoading(true);
    try {
      // First, find the creative-canvas-test product
      const listResult = await client.graphql({
        query: listProducts,
        variables: {
          filter: {
            slug: { eq: 'creative-canvas-test' }
          },
          limit: 1
        }
      });

      const products = listResult.data?.listProducts?.items || [];
      
      if (products.length === 0) {
        setResult({ error: 'Product creative-canvas-test not found' });
        return;
      }

      const product = products[0];
      console.log('Found product:', product);

      // Update the product to be active
      const updateResult = await client.graphql({
        query: updateProduct,
        variables: {
          input: {
            id: product.id,
            isActive: true
          }
        }
      });

      console.log('Update result:', updateResult);
      
      setResult({
        success: true,
        message: 'Product updated successfully!',
        before: { isActive: product.isActive },
        after: { isActive: true },
        product: updateResult.data?.updateProduct
      });

    } catch (error) {
      console.error('Error:', error);
      setResult({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const checkProduct = async () => {
    setLoading(true);
    try {
      // Check the current status
      const result = await client.graphql({
        query: listProducts,
        variables: {
          filter: {
            slug: { eq: 'creative-canvas-test' }
          },
          limit: 1
        }
      });

      const products = result.data?.listProducts?.items || [];
      
      setResult({
        found: products.length > 0,
        product: products[0] || null,
        message: products.length > 0 ? 'Product found' : 'Product not found'
      });

    } catch (error) {
      console.error('Error:', error);
      setResult({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Fix Product Tool</h1>
        
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Fix creative-canvas-test Product</h2>
          
          <div className="space-x-4 mb-6">
            <button
              onClick={checkProduct}
              disabled={loading}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 disabled:opacity-50"
            >
              {loading ? 'Checking...' : 'Check Product Status'}
            </button>
            
            <button
              onClick={fixProduct}
              disabled={loading}
              className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 disabled:opacity-50"
            >
              {loading ? 'Fixing...' : 'Fix Product (Set isActive: true)'}
            </button>
          </div>

          {result && (
            <div className="bg-gray-100 p-4 rounded-lg">
              <h3 className="font-semibold mb-2">Result:</h3>
              <pre className="text-sm overflow-auto">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          )}
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">What This Does</h2>
          <div className="space-y-2 text-gray-600">
            <p>1. <strong>Check Product Status:</strong> Shows the current isActive status of creative-canvas-test</p>
            <p>2. <strong>Fix Product:</strong> Updates the product to set isActive: true</p>
            <p>3. After fixing, the product should appear in collections and product details pages</p>
            <p>4. The issue is that fetchProducts() filters for isActive: true only</p>
          </div>
        </div>
      </div>
    </div>
  );
}
