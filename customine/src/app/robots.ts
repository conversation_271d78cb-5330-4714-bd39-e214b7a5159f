import { MetadataRoute } from 'next'

export const dynamic = 'force-static'

export default function robots(): MetadataRoute.Robots {
  return {
    rules: [
      {
        userAgent: '*',
        allow: '/',
        disallow: [
          '/dashboard/',
          '/api/',
          '/private/',
          '/_next/',
          '/checkout/success',
          '/checkout/cancel',
        ],
      },
      {
        userAgent: 'Googlebot',
        allow: '/',
        disallow: [
          '/dashboard/',
          '/api/',
          '/private/',
        ],
      },
    ],
    sitemap: 'https://customine.in/sitemap.xml',
    host: 'https://customine.in',
  }
}
