"use client";
import React from 'react';
import { useCart } from '../../context/CartContext';
import Link from 'next/link';
import { ShoppingBag, Minus, Plus, Trash2, ArrowLeft, ShoppingCart, Package } from 'lucide-react';
import toast from 'react-hot-toast';
import LoadingSpinner, { CartItemSkeleton, OrderSummarySkeleton } from '../../components/LoadingSpinner';

export default function CartPage() {
  const { items, updateQuantity, removeFromCart, clearCart, loading, initialLoading, hasDataLoaded } = useCart();
  const subtotal = items.reduce((sum, item) => sum + item.price * item.quantity, 0);
  const shipping = subtotal > 2000 ? 0 : 150; // Free shipping over ₹2000
  const total = subtotal + shipping;

  const handleRemoveItem = async (id: string, name: string) => {
    try {
      await removeFromCart(id);
      toast.success(`${name} removed from cart`, { icon: '🗑️' });
    } catch (error) {
      toast.error('Failed to remove item');
    }
  };

  const handleClearCart = async () => {
    try {
      await clearCart();
      toast.success('Cart cleared successfully', { icon: '🧹' });
    } catch (error) {
      toast.error('Failed to clear cart');
    }
  };

  // Show initial loading spinner
  if (initialLoading) {
    return <LoadingSpinner type="cart" />;
  }

  // Only show empty cart after data has been loaded and cart is actually empty
  if (!initialLoading && hasDataLoaded && items.length === 0) {
    return (
      <div className="min-h-screen" style={{ background: 'var(--color-bg-main)' }}>
        <div className="container mx-auto px-4 py-12">
          <div className="max-w-2xl mx-auto text-center">
            <div className="mb-8">
              <ShoppingBag className="w-24 h-24 mx-auto text-[var(--color-teal)] mb-6" />
              <h1 className="text-4xl font-bold mb-4 text-[var(--color-navy)]">Your Cart is Empty</h1>
              <p className="text-[var(--color-text-secondary)] text-lg mb-8">
                Discover our curated collection of premium gift boxes and add something special to your cart!
              </p>
            </div>
            <div className="space-y-4">
              <Link
                href="/collections/all"
                className="inline-flex items-center space-x-2 px-8 py-4 rounded-xl bg-[var(--color-navy)] text-white font-semibold hover:bg-[var(--color-teal)] transition-all duration-300 shadow-lg hover:shadow-xl"
              >
                <ShoppingCart className="w-5 h-5" />
                <span>Start Shopping</span>
              </Link>
              <div className="text-sm text-[var(--color-text-secondary)]">
                <Link href="/" className="hover:text-[var(--color-navy)] transition-colors">
                  ← Back to Home
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen" style={{ background: 'var(--color-bg-main)' }}>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="max-w-6xl mx-auto">
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center space-x-4">
              <Link
                href="/collections/all"
                className="flex items-center space-x-2 text-[var(--color-text-secondary)] hover:text-[var(--color-navy)] transition-colors"
              >
                <ArrowLeft className="w-5 h-5" />
                <span>Continue Shopping</span>
              </Link>
            </div>
            <h1 className="text-3xl font-bold text-[var(--color-navy)] flex items-center space-x-3">
              <ShoppingBag className="w-8 h-8" />
              <span>Shopping Cart ({items.length})</span>
            </h1>
          </div>

          <div className="grid lg:grid-cols-3 gap-8">
            {/* Cart Items */}
            <div className="lg:col-span-2 space-y-4">
              {loading ? (
                // Show skeleton loaders during operations
                <>
                  <CartItemSkeleton />
                  <CartItemSkeleton />
                  <CartItemSkeleton />
                </>
              ) : (
                items.map(item => (
                <div key={item.id} className="bg-[var(--color-bg-card)] rounded-xl shadow-sm border border-[var(--color-sky-blue)]/20 p-6 hover:shadow-md transition-shadow">
                  <div className="flex items-start gap-6">
                    {/* Product Image */}
                    <div className="flex-shrink-0">
                      {item.image && item.image.trim() !== '' ? (
                        <img
                          src={item.image}
                          alt={item.name}
                          className="w-24 h-24 object-cover rounded-lg border-2 border-[var(--color-sky-blue)]/30"
                        />
                      ) : (
                        <div className="w-24 h-24 bg-[var(--color-bg-alt)] rounded-lg border-2 border-[var(--color-sky-blue)]/30 flex items-center justify-center">
                          <Package className="w-8 h-8 text-[var(--color-text-secondary)]" />
                        </div>
                      )}
                    </div>

                    {/* Product Details */}
                    <div className="flex-1 min-w-0">
                      <h3 className="font-semibold text-lg text-[var(--color-navy)] mb-2">{item.name}</h3>
                      {item.giftType && (
                        <div className="inline-block px-3 py-1 bg-[var(--color-sky-blue)]/30 text-[var(--color-navy)] text-sm rounded-full mb-3 capitalize">
                          {item.giftType} Gift
                        </div>
                      )}
                      <div className="text-[var(--color-text-secondary)] text-lg font-medium mb-4">
                        ₹{item.price.toLocaleString()} each
                      </div>

                      {/* Quantity Controls */}
                      <div className="flex items-center space-x-3">
                        <span className="text-sm text-[var(--color-text-secondary)] font-medium">Quantity:</span>
                        <div className="flex items-center border border-[var(--color-sky-blue)]/40 rounded-lg overflow-hidden">
                          <button
                            className="p-2 hover:bg-[var(--color-sky-blue)]/20 text-[var(--color-navy)] transition-colors disabled:opacity-50"
                            onClick={() => updateQuantity(item.id, Math.max(1, item.quantity - 1))}
                            disabled={loading || item.quantity <= 1}
                            aria-label="Decrease quantity"
                          >
                            <Minus className="w-4 h-4" />
                          </button>
                          <span className="px-4 py-2 font-semibold text-[var(--color-navy)] min-w-[3rem] text-center">
                            {item.quantity}
                          </span>
                          <button
                            className="p-2 hover:bg-[var(--color-sky-blue)]/20 text-[var(--color-navy)] transition-colors disabled:opacity-50"
                            onClick={() => updateQuantity(item.id, item.quantity + 1)}
                            disabled={loading}
                            aria-label="Increase quantity"
                          >
                            <Plus className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </div>

                    {/* Price and Remove */}
                    <div className="flex flex-col items-end space-y-4">
                      <div className="text-right">
                        <div className="text-2xl font-bold text-[var(--color-navy)]">
                          ₹{(item.price * item.quantity).toLocaleString()}
                        </div>
                        {item.quantity > 1 && (
                          <div className="text-sm text-[var(--color-text-secondary)]">
                            {item.quantity} × ₹{item.price.toLocaleString()}
                          </div>
                        )}
                      </div>
                      <button
                        className="flex items-center space-x-2 text-red-500 hover:text-red-600 transition-colors p-2 hover:bg-red-50 rounded-lg"
                        onClick={() => handleRemoveItem(item.id, item.name)}
                        disabled={loading}
                        aria-label="Remove from cart"
                      >
                        <Trash2 className="w-4 h-4" />
                        <span className="text-sm font-medium">Remove</span>
                      </button>
                    </div>
                  </div>
                </div>
                ))
              )}
            </div>
            {/* Order Summary */}
            <div className="lg:col-span-1">
              {loading ? (
                <OrderSummarySkeleton />
              ) : (
                <div className="bg-[var(--color-bg-card)] rounded-xl shadow-sm border border-[var(--color-sky-blue)]/20 p-6 sticky top-8">
                  <h2 className="text-xl font-bold text-[var(--color-navy)] mb-6">Order Summary</h2>

                <div className="space-y-4 mb-6">
                  <div className="flex justify-between text-[var(--color-text-primary)]">
                    <span>Subtotal ({items.length} {items.length === 1 ? 'item' : 'items'})</span>
                    <span className="font-semibold">₹{subtotal.toLocaleString()}</span>
                  </div>

                  <div className="flex justify-between text-[var(--color-text-primary)]">
                    <span>Shipping</span>
                    <span className="font-semibold">
                      {shipping === 0 ? (
                        <span className="text-green-600">Free</span>
                      ) : (
                        `₹${shipping.toLocaleString()}`
                      )}
                    </span>
                  </div>

                  {shipping > 0 && (
                    <div className="text-sm text-[var(--color-text-secondary)] bg-[var(--color-sky-blue)]/20 p-3 rounded-lg">
                      💡 Add ₹{(2000 - subtotal).toLocaleString()} more for free shipping!
                    </div>
                  )}

                  <div className="border-t border-[var(--color-sky-blue)]/30 pt-4">
                    <div className="flex justify-between text-lg font-bold text-[var(--color-navy)]">
                      <span>Total</span>
                      <span>₹{total.toLocaleString()}</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <Link
                    href="/checkout"
                    className="w-full bg-[var(--color-navy)] text-white py-4 px-6 rounded-xl font-bold hover:bg-[var(--color-teal)] transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center space-x-2"
                  >
                    <ShoppingCart className="w-5 h-5" />
                    <span>Proceed to Checkout</span>
                  </Link>

                  <button
                    className="w-full text-[var(--color-text-secondary)] hover:text-red-500 py-2 px-4 rounded-lg hover:bg-red-50 transition-all duration-200 text-sm font-medium"
                    onClick={handleClearCart}
                    disabled={loading}
                  >
                    Clear Cart
                  </button>
                </div>

                {/* Trust Badges */}
                <div className="mt-6 pt-6 border-t border-[var(--color-sky-blue)]/30">
                  <div className="text-sm text-[var(--color-text-secondary)] space-y-2">
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span>Secure checkout</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span>Free returns within 7 days</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span>Pan-India delivery</span>
                    </div>
                  </div>
                </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}