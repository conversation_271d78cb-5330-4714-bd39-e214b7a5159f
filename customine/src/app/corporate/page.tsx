import { Metadata } from 'next';
import Link from 'next/link';
import { generateBreadcrumbStructuredData } from '@/utils/structuredData';

export const metadata: Metadata = {
  title: "Corporate Gift Boxes India | Custom Business Gifts | Customine",
  description: "Premium corporate gift boxes for client appreciation, employee recognition, and business events. Thoughtfully curated professional gifts with pan-India shipping. Custom branding available.",
  keywords: "corporate gifts India, business gifts, client appreciation gifts, employee recognition gifts, corporate gift boxes, custom business gifts, professional gifts, branded gifts",
  openGraph: {
    title: "Corporate Gift Boxes India | Custom Business Gifts | Customine",
    description: "Premium corporate gift boxes for client appreciation, employee recognition, and business events. Custom branding available with pan-India shipping.",
    url: "https://customine.in/corporate",
    images: [
      {
        url: "https://customine.in/corporate-gifts-og.jpg",
        width: 1200,
        height: 630,
        alt: "Corporate Gift Boxes by Customine",
      },
    ],
  },
  alternates: {
    canonical: "https://customine.in/corporate",
  },
};

export default function CorporatePage() {
  const breadcrumbStructuredData = generateBreadcrumbStructuredData([
    { name: 'Home', url: 'https://customine.in' },
    { name: 'Corporate Gifts', url: 'https://customine.in/corporate' }
  ]);

  const corporateStructuredData = {
    "@context": "https://schema.org",
    "@type": "Service",
    "name": "Corporate Gift Boxes",
    "description": "Premium corporate gift boxes for client appreciation, employee recognition, and business events",
    "provider": {
      "@type": "Organization",
      "name": "Customine"
    },
    "areaServed": "IN",
    "serviceType": "Corporate Gifting Solutions",
    "offers": {
      "@type": "Offer",
      "category": "Corporate Gifts",
      "areaServed": "IN",
      "availability": "https://schema.org/InStock"
    }
  };

  return (
    <>
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(breadcrumbStructuredData) }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(corporateStructuredData) }}
      />

      <div className="min-h-screen bg-gray-50">
        {/* Hero Section */}
        <section className="bg-gradient-to-r from-blue-900 to-blue-700 text-white py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h1 className="text-4xl md:text-6xl font-bold mb-6">
                Corporate Gift Boxes
              </h1>
              <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
                Strengthen business relationships with thoughtfully curated corporate gift boxes. 
                Perfect for client appreciation, employee recognition, and business events.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  href="/products"
                  className="bg-white text-blue-900 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
                >
                  Browse Corporate Gifts
                </Link>
                <Link
                  href="/pages/special-occasion-inquiry"
                  className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-900 transition-colors"
                >
                  Custom Quote
                </Link>
              </div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 className="text-3xl font-bold text-center mb-12">Why Choose Our Corporate Gifts?</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold mb-2">Professional Quality</h3>
                <p className="text-gray-600">Premium products that reflect your brand&apos;s excellence and attention to detail.</p>
              </div>
              <div className="text-center">
                <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold mb-2">Custom Branding</h3>
                <p className="text-gray-600">Add your company logo and personalized messages to create memorable gifts.</p>
              </div>
              <div className="text-center">
                <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold mb-2">Bulk Orders</h3>
                <p className="text-gray-600">Special pricing for bulk orders with reliable pan-India delivery.</p>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="bg-gray-100 py-16">
          <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
            <h2 className="text-3xl font-bold mb-4">Ready to Impress Your Clients?</h2>
            <p className="text-xl text-gray-600 mb-8">
              Get a custom quote for your corporate gifting needs. We&apos;ll help you create the perfect impression.
            </p>
            <Link
              href="/special-occasion-inquiry"
              className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors inline-block"
            >
              Get Custom Quote
            </Link>
          </div>
        </section>
      </div>
    </>
  );
}
