'use client';

import React, { useState } from 'react';
import { generateClient } from 'aws-amplify/api';
import { listProducts } from '../../graphql/queries';

const client = generateClient();

export default function DebugAPIPage() {
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testAPI = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      console.log('🔍 Testing API with listProducts query...');
      
      // Test 1: Get all products without any filter
      const allProductsResult = await client.graphql({
        query: listProducts,
        variables: {
          limit: 10
        }
      });

      console.log('📡 Raw API Response:', allProductsResult);
      
      const products = allProductsResult.data?.listProducts?.items || [];
      
      setResult({
        success: true,
        totalProducts: products.length,
        products: products.map(p => ({
          id: p.id,
          name: p.name,
          slug: p.slug,
          price: p.price,
          isActive: p.isActive,
          hasImages: p.images?.length > 0,
          imageCount: p.images?.length || 0
        })),
        rawResponse: allProductsResult
      });

      console.log('✅ API Test Results:', {
        totalProducts: products.length,
        products: products.map(p => ({ name: p.name, slug: p.slug }))
      });

    } catch (err) {
      console.error('❌ API Test Failed:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
      setResult({
        success: false,
        error: err
      });
    } finally {
      setLoading(false);
    }
  };

  const testSpecificSlug = async () => {
    setLoading(true);
    setError(null);

    try {
      console.log('🔍 Testing both slugs: creative-canvas vs creative-canvas-test');

      // Test creative-canvas (working)
      const workingResult = await client.graphql({
        query: listProducts,
        variables: {
          filter: {
            slug: { eq: 'creative-canvas' }
          },
          limit: 1
        }
      });

      // Test creative-canvas-test (not working)
      const testResult = await client.graphql({
        query: listProducts,
        variables: {
          filter: {
            slug: { eq: 'creative-canvas-test' }
          },
          limit: 1
        }
      });

      console.log('📡 Working Slug Response:', workingResult);
      console.log('📡 Test Slug Response:', testResult);

      const workingProducts = workingResult.data?.listProducts?.items || [];
      const testProducts = testResult.data?.listProducts?.items || [];

      setResult({
        success: true,
        comparison: {
          'creative-canvas': {
            found: workingProducts.length > 0,
            product: workingProducts[0] || null
          },
          'creative-canvas-test': {
            found: testProducts.length > 0,
            product: testProducts[0] || null
          }
        }
      });

    } catch (err) {
      console.error('❌ Slug Comparison Failed:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">API Debug Tool</h1>
        
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Test API Connection</h2>
          
          <div className="space-x-4 mb-6">
            <button
              onClick={testAPI}
              disabled={loading}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 disabled:opacity-50"
            >
              {loading ? 'Testing...' : 'Test All Products'}
            </button>
            
            <button
              onClick={testSpecificSlug}
              disabled={loading}
              className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 disabled:opacity-50"
            >
              {loading ? 'Testing...' : 'Compare Slugs'}
            </button>
          </div>

          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              <strong>Error:</strong> {error}
            </div>
          )}

          {result && (
            <div className="bg-gray-100 p-4 rounded-lg">
              <h3 className="font-semibold mb-2">API Test Results:</h3>
              <pre className="text-sm overflow-auto">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          )}
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Instructions</h2>
          <div className="space-y-2 text-gray-600">
            <p>1. Click "Test All Products" to see if any products exist in the database</p>
            <p>2. Click "Test Specific Slug" to test fetching a specific product</p>
            <p>3. Check the browser console for detailed logs</p>
            <p>4. If no products are found, you need to import products using the bulk import tool</p>
          </div>
        </div>
      </div>
    </div>
  );
}
