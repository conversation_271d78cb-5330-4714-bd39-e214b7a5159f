import { Plane, MapPin, Gift, Compass, ArrowRight, Phone, Mail } from 'lucide-react';

export default function TravelRetreatGifting() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-teal-50 to-cyan-100 py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="flex items-center justify-center mb-6">
            <Plane className="w-12 h-12 text-teal-600 mr-4" />
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900">
              Travel & Retreat Gifting
            </h1>
          </div>
          <h2 className="text-2xl md:text-3xl font-semibold text-teal-700 mb-6">
            Curated Comforts for Every Journey
          </h2>
          <p className="text-xl text-gray-700 leading-relaxed max-w-3xl mx-auto">
            Perfect for destination events, team retreats, or incentive trips — our travel-inspired gift boxes are practical, stylish, and memorable.
          </p>
        </div>
      </section>

      {/* Box Ideas Section */}
      <section className="py-16 bg-white">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">Travel Gift Box Ideas</h3>
            <p className="text-lg text-gray-600">Essential comforts and conveniences for travelers</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="bg-gray-50 rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-teal-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Compass className="w-8 h-8 text-teal-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">Travel Essentials</h4>
              <p className="text-gray-600 text-sm">Travel pouches, eye masks, hydration kits, and comfort items</p>
            </div>

            <div className="bg-gray-50 rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Gift className="w-8 h-8 text-green-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">Wellness & Energy</h4>
              <p className="text-gray-600 text-sm">Healthy snacks, vitamin boosters, and wellness supplements</p>
            </div>

            <div className="bg-gray-50 rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Plane className="w-8 h-8 text-blue-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">Tech Accessories</h4>
              <p className="text-gray-600 text-sm">Portable chargers, travel adapters, and tech conveniences</p>
            </div>

            <div className="bg-gray-50 rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <MapPin className="w-8 h-8 text-purple-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">Location-Themed Gifts</h4>
              <p className="text-gray-600 text-sm">Destination-specific welcome gifts and local specialties</p>
            </div>
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">Our Travel Gifting Process</h3>
            <p className="text-lg text-gray-600">Creating memorable experiences for every journey</p>
          </div>

          <div className="space-y-12">
            {[
              {
                step: "01",
                title: "Travel Planning Session",
                description: "Understand your destination, travel purpose, group size, and the experience you want to create."
              },
              {
                step: "02", 
                title: "Journey-Focused Design",
                description: "Create gifts that enhance the travel experience and reflect the destination or purpose."
              },
              {
                step: "03",
                title: "Practical & Memorable",
                description: "Balance useful travel items with memorable keepsakes that extend the experience."
              },
              {
                step: "04",
                title: "Destination Coordination",
                description: "Handle logistics for delivery to hotels, venues, or direct to travelers' homes."
              }
            ].map((item, index) => (
              <div key={index} className="flex items-start space-x-6">
                <div className="flex-shrink-0">
                  <div className="w-16 h-16 bg-teal-600 text-white rounded-full flex items-center justify-center font-bold text-xl">
                    {item.step}
                  </div>
                </div>
                <div className="flex-1">
                  <h4 className="text-xl font-semibold text-gray-900 mb-2">{item.title}</h4>
                  <p className="text-gray-600 leading-relaxed">{item.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h3 className="text-3xl font-bold text-gray-900 mb-4">Plan Your Travel Gifting</h3>
          <p className="text-lg text-gray-600 mb-8">Let&apos;s create travel gifts that enhance every journey</p>

          <div className="bg-gradient-to-r from-teal-500 to-cyan-500 rounded-2xl p-8 text-white">
            <h4 className="text-2xl font-bold mb-4">Ready to Enhance Your Travel Experience?</h4>
            <p className="text-lg mb-6 opacity-90">
              Share your travel plans and we&apos;ll create practical, stylish gifts that make every journey memorable.
            </p>
            <a
              href="/pages/special-occasion-inquiry"
              className="inline-flex items-center bg-white text-teal-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-200"
            >
              Submit Your Travel Gift Inquiry
              <ArrowRight className="w-5 h-5 ml-2" />
            </a>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 bg-gray-900 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h3 className="text-2xl font-bold mb-6">Ready to Enhance Your Travel Experience?</h3>
          <p className="text-lg text-gray-300 mb-8">
            Let&apos;s create travel gifts that make every journey memorable
          </p>
          <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-8">
            <a
              href="mailto:<EMAIL>"
              className="flex items-center text-teal-400 hover:text-teal-300 transition-colors"
            >
              <Mail className="w-5 h-5 mr-2" />
              <EMAIL>
            </a>
            <a
              href="tel:+919876543210"
              className="flex items-center text-teal-400 hover:text-teal-300 transition-colors"
            >
              <Phone className="w-5 h-5 mr-2" />
              +91 98765 43210
            </a>
          </div>
        </div>
      </section>
    </div>
  );
}
