import { Star, Gift, Heart, Sparkles, Calendar, Trophy, Home, ArrowRight, Phone, Mail } from 'lucide-react';

export default function EverydayOccasions() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-purple-50 to-pink-100 py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="flex items-center justify-center mb-6">
            <Star className="w-12 h-12 text-purple-600 mr-4" />
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900">
              Everyday Occasions
            </h1>
          </div>
          <h2 className="text-2xl md:text-3xl font-semibold text-purple-700 mb-6">
            Celebrate in Style with Surprise & Delight
          </h2>
          <p className="text-xl text-gray-700 leading-relaxed max-w-3xl mx-auto mb-6">
            Not every moment needs a calendar date to be special. Whether it&apos;s a small win, a thoughtful gesture, or &quot;just because,&quot; our Everyday Occasion boxes are made to turn ordinary days into extraordinary ones.
          </p>
          <p className="text-lg text-gray-600 leading-relaxed max-w-3xl mx-auto">
            Thoughtfully curated for spontaneous joy, these gift boxes are perfect for birthdays, housewarmings, promotions, congratulations, or simple acts of love and kindness.
          </p>
        </div>
      </section>

      {/* Comprehensive Occasion Categories Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">Complete Everyday Occasions Guide</h3>
            <p className="text-lg text-gray-600">From life milestones to cultural celebrations - we have the perfect gift for every moment</p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-4 gap-8">

            {/* Life Events & Personal Milestones */}
            <div className="bg-gradient-to-br from-pink-50 to-purple-50 rounded-lg p-6 hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-pink-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Heart className="w-8 h-8 text-pink-600" />
              </div>
              <h4 className="text-xl font-bold text-gray-900 mb-4 text-center">Life Events & Personal Milestones</h4>
              <div className="space-y-3">
                <div className="border-b border-pink-200 pb-2">
                  <h5 className="font-semibold text-gray-800">Engagement</h5>
                  <p className="text-sm text-gray-600">Customized gifts, sweets, couple hampers</p>
                </div>
                <div className="border-b border-pink-200 pb-2">
                  <h5 className="font-semibold text-gray-800">Housewarming</h5>
                  <p className="text-sm text-gray-600">Kitchenware, plants, idols, home decor</p>
                </div>
                <div className="border-b border-pink-200 pb-2">
                  <h5 className="font-semibold text-gray-800">Baby Shower</h5>
                  <p className="text-sm text-gray-600">Maternity gifts, baby products, clothes</p>
                </div>
                <div className="border-b border-pink-200 pb-2">
                  <h5 className="font-semibold text-gray-800">Naming Ceremony</h5>
                  <p className="text-sm text-gray-600">Silverware, baby clothes, keepsakes</p>
                </div>
                <div className="border-b border-pink-200 pb-2">
                  <h5 className="font-semibold text-gray-800">Birthday</h5>
                  <p className="text-sm text-gray-600">Cakes, personalized gifts, gadgets</p>
                </div>
                <div className="border-b border-pink-200 pb-2">
                  <h5 className="font-semibold text-gray-800">Anniversary</h5>
                  <p className="text-sm text-gray-600">Romantic gifts, couple experiences</p>
                </div>
                <div className="border-b border-pink-200 pb-2">
                  <h5 className="font-semibold text-gray-800">Retirement</h5>
                  <p className="text-sm text-gray-600">Mementos, books, hobby kits</p>
                </div>
                <div>
                  <h5 className="font-semibold text-gray-800">Graduation</h5>
                  <p className="text-sm text-gray-600">Motivational books, electronics, stationery</p>
                </div>
              </div>
            </div>

            {/* Traditional Festivals */}
            <div className="bg-gradient-to-br from-orange-50 to-yellow-50 rounded-lg p-6 hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Sparkles className="w-8 h-8 text-orange-600" />
              </div>
              <h4 className="text-xl font-bold text-gray-900 mb-4 text-center">Traditional Festivals</h4>
              <div className="space-y-3">
                <div className="border-b border-orange-200 pb-2">
                  <h5 className="font-semibold text-gray-800">Easter</h5>
                  <p className="text-sm text-gray-600">Chocolates, Easter eggs, greeting cards</p>
                </div>
                <div className="border-b border-orange-200 pb-2">
                  <h5 className="font-semibold text-gray-800">Diwali</h5>
                  <p className="text-sm text-gray-600">Sweets, home decor, diyas, electronics, clothes</p>
                </div>
                <div className="border-b border-orange-200 pb-2">
                  <h5 className="font-semibold text-gray-800">Holi</h5>
                  <p className="text-sm text-gray-600">Organic colors, dry fruits, clothes, sweets</p>
                </div>
                <div className="border-b border-orange-200 pb-2">
                  <h5 className="font-semibold text-gray-800">Raksha Bandhan</h5>
                  <p className="text-sm text-gray-600">Rakhis, sweets, chocolates, sibling hampers</p>
                </div>
                <div className="border-b border-orange-200 pb-2">
                  <h5 className="font-semibold text-gray-800">Janmashtami</h5>
                  <p className="text-sm text-gray-600">Baby Krishna idols, silver gifts, traditional clothes</p>
                </div>
                <div className="border-b border-orange-200 pb-2">
                  <h5 className="font-semibold text-gray-800">Navratri / Durga Puja</h5>
                  <p className="text-sm text-gray-600">Puja kits, ethnic wear, jewelry, decor</p>
                </div>
                <div className="border-b border-orange-200 pb-2">
                  <h5 className="font-semibold text-gray-800">Karwa Chauth</h5>
                  <p className="text-sm text-gray-600">Cosmetics, jewelry, sarees, fasting kits</p>
                </div>
                <div className="border-b border-orange-200 pb-2">
                  <h5 className="font-semibold text-gray-800">Ganesh Chaturthi</h5>
                  <p className="text-sm text-gray-600">Eco-friendly idols, pooja items, modaks</p>
                </div>
                <div className="border-b border-orange-200 pb-2">
                  <h5 className="font-semibold text-gray-800">Dussehra</h5>
                  <p className="text-sm text-gray-600">Spiritual gifts, books, home decor</p>
                </div>
                <div className="border-b border-orange-200 pb-2">
                  <h5 className="font-semibold text-gray-800">Makar Sankranti / Pongal / Lohri</h5>
                  <p className="text-sm text-gray-600">Til-gud, sweets, homeware</p>
                </div>
                <div className="border-b border-orange-200 pb-2">
                  <h5 className="font-semibold text-gray-800">Gudi Padwa / Ugadi</h5>
                  <p className="text-sm text-gray-600">Fruits, sweets, spiritual gifts, home decor</p>
                </div>
                <div className="border-b border-orange-200 pb-2">
                  <h5 className="font-semibold text-gray-800">Onam</h5>
                  <p className="text-sm text-gray-600">Flower arrangements, Kerala sarees, food hampers</p>
                </div>
                <div className="border-b border-orange-200 pb-2">
                  <h5 className="font-semibold text-gray-800">Eid-ul-Fitr</h5>
                  <p className="text-sm text-gray-600">Dry fruits, perfumes, clothes, sweets</p>
                </div>
                <div className="border-b border-orange-200 pb-2">
                  <h5 className="font-semibold text-gray-800">Eid-ul-Adha</h5>
                  <p className="text-sm text-gray-600">Premium hampers, religious gifts, sweets</p>
                </div>
                <div className="border-b border-orange-200 pb-2">
                  <h5 className="font-semibold text-gray-800">Guru Nanak Jayanti</h5>
                  <p className="text-sm text-gray-600">Spiritual books, devotional music, lamps</p>
                </div>
                <div className="border-b border-orange-200 pb-2">
                  <h5 className="font-semibold text-gray-800">Baisakhi</h5>
                  <p className="text-sm text-gray-600">Traditional clothes, sweets, celebration hampers</p>
                </div>
                <div className="border-b border-orange-200 pb-2">
                  <h5 className="font-semibold text-gray-800">Mahavir Jayanti</h5>
                  <p className="text-sm text-gray-600">Spiritual books, incense kits, minimalistic gifts</p>
                </div>
                <div>
                  <h5 className="font-semibold text-gray-800">Buddha Purnima</h5>
                  <p className="text-sm text-gray-600">Ethical gifts, Buddha idols, meditation tools</p>
                </div>
              </div>
            </div>

            {/* Others (Regional & Misc.) */}
            <div className="bg-gradient-to-br from-green-50 to-teal-50 rounded-lg p-6 hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Calendar className="w-8 h-8 text-green-600" />
              </div>
              <h4 className="text-xl font-bold text-gray-900 mb-4 text-center">Others (Regional & Misc.)</h4>
              <div className="space-y-3">
                <div className="border-b border-green-200 pb-2">
                  <h5 className="font-semibold text-gray-800">Lakshmi Pooja</h5>
                  <p className="text-sm text-gray-600">Silver coins, diyas, spiritual items</p>
                </div>
                <div className="border-b border-green-200 pb-2">
                  <h5 className="font-semibold text-gray-800">Purnima / Amavasya</h5>
                  <p className="text-sm text-gray-600">Incense, devotional books, pooja kits</p>
                </div>
                <div className="border-b border-green-200 pb-2">
                  <h5 className="font-semibold text-gray-800">Akshaya Tritiya</h5>
                  <p className="text-sm text-gray-600">Gold jewelry, silver items</p>
                </div>
                <div className="border-b border-green-200 pb-2">
                  <h5 className="font-semibold text-gray-800">Chhath Puja</h5>
                  <p className="text-sm text-gray-600">Religious baskets, steel utensils</p>
                </div>
                <div className="border-b border-green-200 pb-2">
                  <h5 className="font-semibold text-gray-800">Teej Festivals</h5>
                  <p className="text-sm text-gray-600">Ethnic wear, cosmetics, sweets</p>
                </div>
                <div className="border-b border-green-200 pb-2">
                  <h5 className="font-semibold text-gray-800">Ram Navami / Hanuman Jayanti</h5>
                  <p className="text-sm text-gray-600">Spiritual books, idols</p>
                </div>
                <div className="border-b border-green-200 pb-2">
                  <h5 className="font-semibold text-gray-800">Independence / Republic Day</h5>
                  <p className="text-sm text-gray-600">Flags, patriotic gifts</p>
                </div>
                <div className="border-b border-green-200 pb-2">
                  <h5 className="font-semibold text-gray-800">Guru Purnima</h5>
                  <p className="text-sm text-gray-600">Books, flowers, gratitude notes</p>
                </div>
              </div>

              <div className="border-t border-green-200 pt-4">
                <h4 className="text-lg font-bold text-gray-900 mb-3">Institutions</h4>
                <div className="space-y-3">
                  <div className="border-b border-green-200 pb-2">
                    <h5 className="font-semibold text-gray-800">Independence / Republic Day</h5>
                    <p className="text-sm text-gray-600">Flags, patriotic gifts</p>
                  </div>
                  <div className="border-b border-green-200 pb-2">
                    <h5 className="font-semibold text-gray-800">Back to School</h5>
                    <p className="text-sm text-gray-600">Stationery kits, school bags</p>
                  </div>
                  <div className="border-b border-green-200 pb-2">
                    <h5 className="font-semibold text-gray-800">Exam Results</h5>
                    <p className="text-sm text-gray-600">Books, gadgets, gift cards</p>
                  </div>
                  <div>
                    <h5 className="font-semibold text-gray-800">New Year</h5>
                    <p className="text-sm text-gray-600">Calendars, planners, party kits</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Modern & Professional + Love & Relationships */}
            <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg p-6 hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Trophy className="w-8 h-8 text-blue-600" />
              </div>
              <h4 className="text-xl font-bold text-gray-900 mb-4 text-center">Modern & Professional</h4>
              <div className="space-y-3 mb-6">
                <div className="border-b border-blue-200 pb-2">
                  <h5 className="font-semibold text-gray-800">Corporate Gifting</h5>
                  <p className="text-sm text-gray-600">Branded gifts, hampers, planners</p>
                </div>
                <div className="border-b border-blue-200 pb-2">
                  <h5 className="font-semibold text-gray-800">Boss&apos;s Day</h5>
                  <p className="text-sm text-gray-600">Thank-you cards, office accessories</p>
                </div>
                <div className="border-b border-blue-200 pb-2">
                  <h5 className="font-semibold text-gray-800">Startup Launch</h5>
                  <p className="text-sm text-gray-600">Customized branding gifts, plants</p>
                </div>
              </div>

              <div className="border-t border-blue-200 pt-4">
                <h4 className="text-lg font-bold text-gray-900 mb-3">Love & Relationships</h4>
                <div className="space-y-3">
                  <div className="border-b border-blue-200 pb-2">
                    <h5 className="font-semibold text-gray-800">Valentine&apos;s Day</h5>
                    <p className="text-sm text-gray-600">Romantic gifts, flowers, perfumes</p>
                  </div>
                  <div className="border-b border-blue-200 pb-2">
                    <h5 className="font-semibold text-gray-800">Propose/Promise/Rose Day</h5>
                    <p className="text-sm text-gray-600">Rings, roses, couple items</p>
                  </div>
                  <div className="border-b border-blue-200 pb-2">
                    <h5 className="font-semibold text-gray-800">Mother&apos;s/Father&apos;s/Parents&apos; Day</h5>
                    <p className="text-sm text-gray-600">Appreciation gifts, grooming kits</p>
                  </div>
                  <div>
                    <h5 className="font-semibold text-gray-800">Siblings Day / Friendship Day</h5>
                    <p className="text-sm text-gray-600">Friendship bands, mugs, frames</p>
                  </div>
                </div>
              </div>
            </div>

          </div>
        </div>
      </section>

      {/* Gift Box Themes Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">Everyday Gift Box Themes</h3>
            <p className="text-lg text-gray-600">Curated collections for life&apos;s spontaneous moments</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow">
              <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mb-4">
                <Sparkles className="w-6 h-6 text-yellow-600" />
              </div>
              <h4 className="text-xl font-semibold text-gray-900 mb-3">Surprise & Delight Box</h4>
              <p className="text-gray-600 mb-4">A mix of fun surprises, treats, and small luxuries perfect for brightening someone&apos;s day</p>
              <ul className="text-sm text-gray-500 space-y-1">
                <li>• Gourmet snacks & treats</li>
                <li>• Small luxury items</li>
                <li>• Fun surprises</li>
                <li>• Personalized note</li>
              </ul>
            </div>

            <div className="bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
                <Gift className="w-6 h-6 text-green-600" />
              </div>
              <h4 className="text-xl font-semibold text-gray-900 mb-3">Celebration Essentials</h4>
              <p className="text-gray-600 mb-4">Everything needed for an instant celebration, from party treats to festive accessories</p>
              <ul className="text-sm text-gray-500 space-y-1">
                <li>• Celebration treats</li>
                <li>• Party accessories</li>
                <li>• Festive decorations</li>
                <li>• Congratulatory items</li>
              </ul>
            </div>

            <div className="bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                <Heart className="w-6 h-6 text-blue-600" />
              </div>
              <h4 className="text-xl font-semibold text-gray-900 mb-3">Comfort & Care Package</h4>
              <p className="text-gray-600 mb-4">Thoughtful items focused on wellness, comfort, and showing you care</p>
              <ul className="text-sm text-gray-500 space-y-1">
                <li>• Wellness products</li>
                <li>• Comfort items</li>
                <li>• Self-care essentials</li>
                <li>• Encouraging notes</li>
              </ul>
            </div>

            <div className="bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mb-4">
                <Home className="w-6 h-6 text-purple-600" />
              </div>
              <h4 className="text-xl font-semibold text-gray-900 mb-3">New Home Starter Kit</h4>
              <p className="text-gray-600 mb-4">Practical and beautiful items to help make a new house feel like home</p>
              <ul className="text-sm text-gray-500 space-y-1">
                <li>• Home essentials</li>
                <li>• Decorative items</li>
                <li>• Kitchen basics</li>
                <li>• Cozy comforts</li>
              </ul>
            </div>

            <div className="bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow">
              <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4">
                <Trophy className="w-6 h-6 text-red-600" />
              </div>
              <h4 className="text-xl font-semibold text-gray-900 mb-3">Achievement Celebration</h4>
              <p className="text-gray-600 mb-4">Professional and personal items to commemorate successes and milestones</p>
              <ul className="text-sm text-gray-500 space-y-1">
                <li>• Success-themed items</li>
                <li>• Professional accessories</li>
                <li>• Motivational products</li>
                <li>• Celebration treats</li>
              </ul>
            </div>

            <div className="bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow">
              <div className="w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center mb-4">
                <Star className="w-6 h-6 text-indigo-600" />
              </div>
              <h4 className="text-xl font-semibold text-gray-900 mb-3">Random Acts of Kindness</h4>
              <p className="text-gray-600 mb-4">Simple, thoughtful gifts perfect for spreading joy and showing appreciation</p>
              <ul className="text-sm text-gray-500 space-y-1">
                <li>• Thoughtful gestures</li>
                <li>• Small luxuries</li>
                <li>• Appreciation items</li>
                <li>• Kindness tokens</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">Our Everyday Gifting Process</h3>
            <p className="text-lg text-gray-600">Making every day special with thoughtful curation</p>
          </div>

          <div className="space-y-12">
            {[
              {
                step: "01",
                title: "Occasion Understanding",
                description: "Tell us about the moment you want to celebrate, the person you&apos;re gifting, and the feeling you want to create."
              },
              {
                step: "02", 
                title: "Spontaneous Curation",
                description: "We select items that capture the spirit of everyday joy and match the recipient&apos;s personality and preferences."
              },
              {
                step: "03",
                title: "Personal Touch Addition",
                description: "Add custom messages, personal elements, and special touches that make the ordinary moment extraordinary."
              },
              {
                step: "04",
                title: "Surprise Delivery",
                description: "Perfect timing and presentation to maximize the surprise and delight factor of your thoughtful gesture."
              }
            ].map((item, index) => (
              <div key={index} className="flex items-start space-x-6">
                <div className="flex-shrink-0">
                  <div className="w-16 h-16 bg-purple-600 text-white rounded-full flex items-center justify-center font-bold text-xl">
                    {item.step}
                  </div>
                </div>
                <div className="flex-1">
                  <h4 className="text-xl font-semibold text-gray-900 mb-2">{item.title}</h4>
                  <p className="text-gray-600 leading-relaxed">{item.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h3 className="text-3xl font-bold text-gray-900 mb-4">Ready to Make Today Special?</h3>
          <p className="text-lg text-gray-600 mb-8">Let&apos;s create everyday magic with thoughtful, spontaneous gifting</p>
          
          <div className="bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl p-8 text-white">
            <h4 className="text-2xl font-bold mb-4">Turn Ordinary into Extraordinary</h4>
            <p className="text-lg mb-6 opacity-90">
              Share the moment you want to celebrate and we&apos;ll create the perfect gift to make it unforgettable.
            </p>
            <a
              href="/pages/special-occasion-inquiry"
              className="inline-flex items-center bg-white text-purple-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-200"
            >
              Submit Your Everyday Occasion Inquiry
              <ArrowRight className="w-5 h-5 ml-2" />
            </a>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 bg-gray-900 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h3 className="text-2xl font-bold mb-6">Ready to Spread Everyday Joy?</h3>
          <p className="text-lg text-gray-300 mb-8">
            Let&apos;s create gifts that turn ordinary moments into extraordinary memories
          </p>
          <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-8">
            <a
              href="mailto:<EMAIL>"
              className="flex items-center text-purple-400 hover:text-purple-300 transition-colors"
            >
              <Mail className="w-5 h-5 mr-2" />
              <EMAIL>
            </a>
            <a
              href="tel:+919876543210"
              className="flex items-center text-purple-400 hover:text-purple-300 transition-colors"
            >
              <Phone className="w-5 h-5 mr-2" />
              +91 98765 43210
            </a>
          </div>
        </div>
      </section>
    </div>
  );
}
