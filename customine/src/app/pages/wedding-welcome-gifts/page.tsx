import { <PERSON>, <PERSON>, Sparkles, <PERSON>, <PERSON>R<PERSON>, Phone, Mail } from 'lucide-react';

export default function WeddingWelcomeGifts() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-pink-50 to-rose-100 py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="flex items-center justify-center mb-6">
            <Heart className="w-12 h-12 text-rose-600 mr-4" />
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900">
              Wedding Guest Welcome Gifts
            </h1>
          </div>
          <h2 className="text-2xl md:text-3xl font-semibold text-rose-700 mb-6">
            Make Every Guest Feel Cherished
          </h2>
          <p className="text-xl text-gray-700 leading-relaxed max-w-3xl mx-auto">
            Set the tone for your wedding celebration with elegant welcome boxes that delight your guests and reflect your unique love story.
          </p>
        </div>
      </section>

      {/* Box Ideas Section */}
      <section className="py-16 bg-white">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">Wedding Welcome Box Ideas</h3>
            <p className="text-lg text-gray-600">Thoughtful touches that welcome your guests with love</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="bg-gray-50 rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-pink-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Gift className="w-8 h-8 text-pink-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">Local Treats & Essentials</h4>
              <p className="text-gray-600 text-sm">Itinerary cards, hydration kits, and local delicacies that showcase your venue</p>
            </div>

            <div className="bg-gray-50 rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Sparkles className="w-8 h-8 text-purple-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">Celebration Beverages</h4>
              <p className="text-gray-600 text-sm">Mini champagne bottles, artisanal beverages, and toast-worthy treats</p>
            </div>

            <div className="bg-gray-50 rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-rose-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Heart className="w-8 h-8 text-rose-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">Custom Keepsakes</h4>
              <p className="text-gray-600 text-sm">Personalized candles, luggage tags, and hangover recovery kits</p>
            </div>

            <div className="bg-gray-50 rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-amber-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Star className="w-8 h-8 text-amber-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">Themed Snacks</h4>
              <p className="text-gray-600 text-sm">Sweet and savory treats curated to match your wedding theme and colors</p>
            </div>
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">Our Wedding Gifting Process</h3>
            <p className="text-lg text-gray-600">Creating magical moments for your special day</p>
          </div>

          <div className="space-y-12">
            {[
              {
                step: "01",
                title: "Wedding Consultation",
                description: "Share your wedding theme, colors, venue details, and guest count. We&apos;ll discuss your vision and budget."
              },
              {
                step: "02", 
                title: "Custom Design Creation",
                description: "Our team designs welcome gifts that reflect your love story and complement your wedding aesthetic."
              },
              {
                step: "03",
                title: "Sample & Approval",
                description: "Review a sample box, make any adjustments, and approve the final design before production."
              },
              {
                step: "04",
                title: "Venue Delivery",
                description: "We coordinate with your venue for seamless delivery and setup of all welcome gifts."
              }
            ].map((item, index) => (
              <div key={index} className="flex items-start space-x-6">
                <div className="flex-shrink-0">
                  <div className="w-16 h-16 bg-rose-600 text-white rounded-full flex items-center justify-center font-bold text-xl">
                    {item.step}
                  </div>
                </div>
                <div className="flex-1">
                  <h4 className="text-xl font-semibold text-gray-900 mb-2">{item.title}</h4>
                  <p className="text-gray-600 leading-relaxed">{item.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h3 className="text-3xl font-bold text-gray-900 mb-4">Plan Your Wedding Welcome Gifts</h3>
          <p className="text-lg text-gray-600 mb-8">Let&apos;s create beautiful welcome gifts that your guests will treasure</p>

          <div className="bg-gradient-to-r from-rose-500 to-pink-500 rounded-2xl p-8 text-white">
            <h4 className="text-2xl font-bold mb-4">Ready to Create Magic?</h4>
            <p className="text-lg mb-6 opacity-90">
              Share your wedding vision with us and we&apos;ll design welcome gifts that perfectly capture your love story.
            </p>
            <a
              href="/pages/special-occasion-inquiry"
              className="inline-flex items-center bg-white text-rose-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-200"
            >
              Submit Your Wedding Inquiry
              <ArrowRight className="w-5 h-5 ml-2" />
            </a>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 bg-gray-900 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h3 className="text-2xl font-bold mb-6">Ready to Create Magic?</h3>
          <p className="text-lg text-gray-300 mb-8">
            Let&apos;s design welcome gifts that make your wedding unforgettable
          </p>
          <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-8">
            <a
              href="mailto:<EMAIL>"
              className="flex items-center text-rose-400 hover:text-rose-300 transition-colors"
            >
              <Mail className="w-5 h-5 mr-2" />
              <EMAIL>
            </a>
            <a
              href="tel:+919876543210"
              className="flex items-center text-rose-400 hover:text-rose-300 transition-colors"
            >
              <Phone className="w-5 h-5 mr-2" />
              +91 98765 43210
            </a>
          </div>
        </div>
      </section>
    </div>
  );
}
