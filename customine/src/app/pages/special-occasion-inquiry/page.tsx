'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { Calendar, Users, MapPin, DollarSign, MessageSquare, Send, CheckCircle, Gift, Target } from 'lucide-react';
import { inquiryAPI, SpecialOccasionInquiry } from '../../../services/inquiryApi';
import toast from 'react-hot-toast';

function SpecialOccasionInquiryForm() {
  const searchParams = useSearchParams();
  
  const [formData, setFormData] = useState<Partial<SpecialOccasionInquiry>>({
    type: 'SPECIAL_OCCASION',
    name: '',
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    company: '',
    subject: '',
    message: '',
    eventType: '',
    giftingType: '',
    eventDate: '',
    eventLocation: '',
    guestCount: 0,
    totalGifts: '',
    totalBudget: '',
    budget: '',
    location: '',
    eventDetails: '',
    source: '',
    referral: '',
    priority: 'medium'
  });
  const [loading, setLoading] = useState(false);
  const [submitted, setSubmitted] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Populate form data from URL parameters
  useEffect(() => {
    if (searchParams) {
      const urlData: Partial<SpecialOccasionInquiry> = {};
      
      // Extract all URL parameters
      const firstName = searchParams.get('firstName');
      const lastName = searchParams.get('lastName');
      const email = searchParams.get('email');
      const phone = searchParams.get('phone');
      const company = searchParams.get('company');
      const giftingType = searchParams.get('giftingType');
      const eventDate = searchParams.get('eventDate');
      const eventLocation = searchParams.get('eventLocation');
      const totalGifts = searchParams.get('totalGifts');
      const totalBudget = searchParams.get('totalBudget');
      const eventDetails = searchParams.get('eventDetails');
      const source = searchParams.get('source');
      const referral = searchParams.get('referral');

      // Populate form data
      if (firstName) urlData.firstName = decodeURIComponent(firstName);
      if (lastName) urlData.lastName = decodeURIComponent(lastName);
      if (email) urlData.email = decodeURIComponent(email);
      if (phone) urlData.phone = decodeURIComponent(phone);
      if (company) urlData.company = decodeURIComponent(company);
      if (giftingType) urlData.giftingType = decodeURIComponent(giftingType);
      if (eventDate) urlData.eventDate = decodeURIComponent(eventDate);
      if (eventLocation) urlData.eventLocation = decodeURIComponent(eventLocation);
      if (totalGifts) urlData.totalGifts = decodeURIComponent(totalGifts);
      if (totalBudget) urlData.totalBudget = decodeURIComponent(totalBudget);
      if (eventDetails) urlData.eventDetails = decodeURIComponent(eventDetails);
      if (source) urlData.source = decodeURIComponent(source);
      if (referral) urlData.referral = decodeURIComponent(referral);

      // Construct full name from firstName and lastName
      if (firstName && lastName) {
        urlData.name = `${decodeURIComponent(firstName)} ${decodeURIComponent(lastName)}`;
      }

      // Set default subject based on gifting type
      if (giftingType) {
        const giftingTypeFormatted = decodeURIComponent(giftingType).replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase());
        urlData.subject = `${giftingTypeFormatted} Inquiry`;
      }

      // Update form data with URL parameters
      setFormData(prev => ({
        ...prev,
        ...urlData
      }));
    }
  }, [searchParams]);

  const handleInputChange = (field: string, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Basic validation
    if (!formData.firstName || !formData.lastName || !formData.email || !formData.subject || !formData.message) {
      toast.error('Please fill in all required fields (First Name, Last Name, Email, Subject, Message)', {
        duration: 4000,
        icon: '⚠️',
      });
      setError('Please fill in all required fields (First Name, Last Name, Email, Subject, Message)');
      return;
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email || '')) {
      toast.error('Please enter a valid email address', {
        duration: 4000,
        icon: '📧',
      });
      setError('Please enter a valid email address');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Show loading toast
      const loadingToast = toast.loading('Submitting your inquiry...', {
        icon: '⏳',
      });

      const result = await inquiryAPI.createSpecialOccasionInquiry(formData as SpecialOccasionInquiry);

      // Dismiss loading toast
      toast.dismiss(loadingToast);
      
      if (result.success) {
        setSubmitted(true);
        toast.success('Your inquiry has been submitted successfully! We\'ll get back to you soon.', {
          duration: 6000,
          icon: '🎉',
        });
      } else {
        const errorMessage = result.error || 'Failed to submit inquiry';
        toast.error(errorMessage, {
          duration: 5000,
          icon: '❌',
        });
        setError(errorMessage);
      }
    } catch (err) {
      console.error('Error submitting inquiry:', err);
      const errorMessage = 'Failed to submit inquiry. Please try again.';
      toast.error(errorMessage, {
        duration: 5000,
        icon: '❌',
      });
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  if (submitted) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
        <div className="bg-white rounded-2xl shadow-2xl p-8 max-w-md w-full text-center">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <CheckCircle className="w-8 h-8 text-green-600" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Inquiry Submitted!</h2>
          <p className="text-gray-600 mb-6">
            Thank you for your special occasion inquiry. Our team will review your request and get back to you within 24 hours.
          </p>
          <button
            onClick={() => {
              setSubmitted(false);
              setFormData({
                type: 'SPECIAL_OCCASION',
                name: '',
                firstName: '',
                lastName: '',
                email: '',
                phone: '',
                company: '',
                subject: '',
                message: '',
                eventType: '',
                giftingType: '',
                eventDate: '',
                eventLocation: '',
                guestCount: 0,
                totalGifts: '',
                totalBudget: '',
                budget: '',
                location: '',
                eventDetails: '',
                source: '',
                referral: '',
                priority: 'medium'
              });
            }}
            className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-all duration-200"
          >
            Submit Another Inquiry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Special Occasion Inquiry</h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Planning a special event? Let us help you create the perfect custom gift boxes for your wedding, 
            corporate event, or celebration.
          </p>
        </div>

        {/* Form */}
        <div className="bg-white rounded-2xl shadow-2xl overflow-hidden">
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 px-8 py-6">
            <h2 className="text-2xl font-bold text-white">Tell Us About Your Event</h2>
            <p className="text-blue-100">Fill out the form below and we'll get back to you with a custom proposal</p>
          </div>

          <form onSubmit={handleSubmit} className="p-8">
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6">
                {error}
              </div>
            )}

            <div className="space-y-8">
              {/* Contact Information */}
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <MessageSquare className="w-5 h-5 mr-2 text-blue-600" />
                  Contact Information
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">First Name *</label>
                    <input
                      type="text"
                      value={formData.firstName || ''}
                      onChange={(e) => {
                        handleInputChange('firstName', e.target.value);
                        // Auto-update full name
                        const fullName = `${e.target.value} ${formData.lastName || ''}`.trim();
                        handleInputChange('name', fullName);
                      }}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                      placeholder="Enter your first name"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Last Name *</label>
                    <input
                      type="text"
                      value={formData.lastName || ''}
                      onChange={(e) => {
                        handleInputChange('lastName', e.target.value);
                        // Auto-update full name
                        const fullName = `${formData.firstName || ''} ${e.target.value}`.trim();
                        handleInputChange('name', fullName);
                      }}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                      placeholder="Enter your last name"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
                    <input
                      type="email"
                      value={formData.email || ''}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                      placeholder="Enter your email address"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                    <input
                      type="tel"
                      value={formData.phone || ''}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                      placeholder="Enter your phone number"
                    />
                  </div>
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Company (Optional)</label>
                    <input
                      type="text"
                      value={formData.company || ''}
                      onChange={(e) => handleInputChange('company', e.target.value)}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                      placeholder="Enter company name"
                    />
                  </div>
                </div>
              </div>

              {/* Event Details */}
              <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-6 border border-green-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <Calendar className="w-5 h-5 mr-2 text-green-600" />
                  Event Details
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Event/Occasion Type</label>
                    <select
                      value={formData.eventType || ''}
                      onChange={(e) => handleInputChange('eventType', e.target.value)}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200"
                    >
                      <option value="">Select Event Type</option>

                      {/* Special Occasions */}
                      <optgroup label="Special Occasions">
                        <option value="corporate-gifting-solutions">Corporate Gifting Solutions</option>
                        <option value="wedding-guest-welcome-gifts">Wedding Guest Welcome Gifts</option>
                        <option value="event-conference-gifting">Event & Conference Gifting</option>
                        <option value="premium-client-thank-you-gifts">Premium Client Thank You Gifts</option>
                        <option value="bridal-party-groomsmen-keepsakes">Bridal Party & Groomsmen Keepsakes</option>
                        <option value="milestone-celebration-gifts">Milestone Celebration Gifts</option>
                        <option value="travel-retreat-gifting">Travel & Retreat Gifting</option>
                      </optgroup>

                      {/* Seasonal/Exclusive Items */}
                      <optgroup label="Seasonal/Exclusive Items">
                        <option value="diwali-gifting">Diwali Gifting</option>
                        <option value="christmas-gifting">Christmas Gifting</option>
                        <option value="new-year-gifting">New Year Gifting</option>
                        <option value="valentines-day-gifting">Valentine's Day Gifting</option>
                      </optgroup>

                      {/* Everyday Occasions - Life Events */}
                      <optgroup label="Everyday Occasions - Life Events">
                        <option value="engagement">Engagement</option>
                        <option value="housewarming">Housewarming</option>
                        <option value="baby-shower">Baby Shower</option>
                        <option value="naming-ceremony">Naming Ceremony</option>
                        <option value="birthday-celebration">Birthday Celebration</option>
                        <option value="anniversary">Anniversary</option>
                        <option value="retirement">Retirement</option>
                        <option value="graduation">Graduation</option>
                      </optgroup>

                      {/* Everyday Occasions - Traditional Festivals */}
                      <optgroup label="Everyday Occasions - Traditional Festivals">
                        <option value="diwali-traditional">Diwali (Traditional)</option>
                        <option value="holi">Holi</option>
                        <option value="raksha-bandhan">Raksha Bandhan</option>
                        <option value="janmashtami">Janmashtami</option>
                        <option value="navratri-durga-puja">Navratri / Durga Puja</option>
                        <option value="karva-chauth">Karva Chauth</option>
                      </optgroup>

                      <option value="other">Other</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Gift Style/Category</label>
                    <select
                      value={formData.giftingType || ''}
                      onChange={(e) => handleInputChange('giftingType', e.target.value)}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200"
                    >
                      <option value="">Select Gift Style/Category</option>
                      <option value="luxury-premium">Luxury & Premium</option>
                      <option value="eco-friendly-sustainable">Eco-Friendly & Sustainable</option>
                      <option value="personalized-customized">Personalized & Customized</option>
                      <option value="traditional-cultural">Traditional & Cultural</option>
                      <option value="modern-contemporary">Modern & Contemporary</option>
                      <option value="artisanal-handcrafted">Artisanal & Handcrafted</option>
                      <option value="gourmet-food-beverages">Gourmet Food & Beverages</option>
                      <option value="wellness-self-care">Wellness & Self-Care</option>
                      <option value="tech-gadgets">Tech & Gadgets</option>
                      <option value="home-decor">Home & Decor</option>
                      <option value="fashion-accessories">Fashion & Accessories</option>
                      <option value="books-stationery">Books & Stationery</option>
                      <option value="experience-vouchers">Experience & Vouchers</option>
                      <option value="bulk-corporate">Bulk & Corporate</option>
                      <option value="budget-friendly">Budget-Friendly</option>
                      <option value="other">Other</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Event Date</label>
                    <input
                      type="date"
                      value={formData.eventDate || ''}
                      onChange={(e) => handleInputChange('eventDate', e.target.value)}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Event Location</label>
                    <input
                      type="text"
                      value={formData.eventLocation || ''}
                      onChange={(e) => handleInputChange('eventLocation', e.target.value)}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200"
                      placeholder="Enter event location (city, venue, etc.)"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Total Gifts Needed</label>
                    <select
                      value={formData.totalGifts || ''}
                      onChange={(e) => handleInputChange('totalGifts', e.target.value)}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200"
                    >
                      <option value="">Select Quantity Range</option>
                      <option value="1-25">1-25 gifts</option>
                      <option value="26-50">26-50 gifts</option>
                      <option value="51-100">51-100 gifts</option>
                      <option value="101-250">101-250 gifts</option>
                      <option value="250+">250+ gifts</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Total Budget</label>
                    <select
                      value={formData.totalBudget || ''}
                      onChange={(e) => handleInputChange('totalBudget', e.target.value)}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200"
                    >
                      <option value="">Select Budget Range</option>
                      <option value="under-10k">Under ₹10,000</option>
                      <option value="10k-25k">₹10,000 - ₹25,000</option>
                      <option value="25k-50k">₹25,000 - ₹50,000</option>
                      <option value="50k-100k">₹50,000 - ₹1,00,000</option>
                      <option value="100k+">Above ₹1,00,000</option>
                    </select>
                  </div>
                </div>
              </div>
   {/* Your Requirements */}
   <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-6 border border-purple-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <MessageSquare className="w-5 h-5 mr-2 text-purple-600" />
                  Your Requirements
                </h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Subject *</label>
                    <input
                      type="text"
                      value={formData.subject || ''}
                      onChange={(e) => handleInputChange('subject', e.target.value)}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                      placeholder="Brief description of your inquiry"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Detailed Message *</label>
                    <textarea
                      value={formData.message || ''}
                      onChange={(e) => handleInputChange('message', e.target.value)}
                      rows={6}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                      placeholder="Please describe your requirements in detail. Include any specific themes, preferences, dietary restrictions, or special requests..."
                      required
                    />
                  </div>
                </div>
              </div>

              {/* Additional Information */}
              <div className="bg-gradient-to-r from-orange-50 to-amber-50 rounded-xl p-6 border border-orange-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <Target className="w-5 h-5 mr-2 text-orange-600" />
                  Additional Information
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">How did you hear about us?</label>
                    <select
                      value={formData.source || ''}
                      onChange={(e) => handleInputChange('source', e.target.value)}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200"
                    >
                      <option value="">Select Source</option>
                      <option value="google-search">Google Search</option>
                      <option value="social-media">Social Media</option>
                      <option value="referral">Referral</option>
                      <option value="website">Website</option>
                      <option value="advertisement">Advertisement</option>
                      <option value="other">Other</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Referral Details (if applicable)</label>
                    <input
                      type="text"
                      value={formData.referral || ''}
                      onChange={(e) => handleInputChange('referral', e.target.value)}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200"
                      placeholder="Enter referral source or person"
                    />
                  </div>
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Event Details</label>
                    <textarea
                      value={formData.eventDetails || ''}
                      onChange={(e) => handleInputChange('eventDetails', e.target.value)}
                      rows={3}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200"
                      placeholder="Additional event details, special requirements, themes, etc."
                    />
                  </div>
                </div>
              </div>
           
            </div>

            {/* Submit Button */}
            <div className="mt-8 text-center">
              <button
                type="submit"
                disabled={loading}
                className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 flex items-center space-x-2 font-medium shadow-lg mx-auto disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Submitting...</span>
                  </>
                ) : (
                  <>
                    <Send className="w-4 h-4" />
                    <span>Submit Inquiry</span>
                  </>
                )}
              </button>
              <p className="text-sm text-gray-500 mt-3">
                We&apos;ll review your inquiry and get back to you within 24 hours with a custom proposal.
              </p>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

export default function SpecialOccasionInquiryPage() {
  return (
    <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Loading...</div>}>
      <SpecialOccasionInquiryForm />
    </Suspense>
  );
}
