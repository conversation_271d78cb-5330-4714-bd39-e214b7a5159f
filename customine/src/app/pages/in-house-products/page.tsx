import { Home, Sparkles, Gift, Car, ArrowRight, Phone, Mail, Star, Heart } from 'lucide-react';

export default function InHouseProducts() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-indigo-50 to-purple-100 py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="flex items-center justify-center mb-6">
            <Home className="w-12 h-12 text-indigo-600 mr-4" />
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900">
              In-House Products
            </h1>
          </div>
          <h2 className="text-2xl md:text-3xl font-semibold text-indigo-700 mb-6">
            Exclusively Crafted, Only at Customine
          </h2>
          <p className="text-xl text-gray-700 leading-relaxed max-w-4xl mx-auto mb-6">
            Discover our signature collection of in-house products — uniquely designed and handcrafted with love, only available through Customine. From refreshing room and car fragrances to artisanal gift sets you won&apos;t find anywhere online, every item is made to elevate everyday living and gifting.
          </p>
          <p className="text-lg text-gray-600 leading-relaxed max-w-3xl mx-auto">
            Perfect as standalone delights or thoughtful additions to your custom gift boxes.
          </p>
        </div>
      </section>

      {/* Product Highlights Section */}
      <section className="py-16 bg-white">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">Product Highlights</h3>
            <p className="text-lg text-gray-600">Exclusively crafted items you won&apos;t find anywhere else</p>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            
            {/* Room & Car Fresheners */}
            <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-8 hover:shadow-lg transition-shadow">
              <div className="flex items-center mb-6">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                  <Car className="w-8 h-8 text-blue-600" />
                </div>
                <h4 className="text-2xl font-bold text-gray-900">Room & Car Fresheners</h4>
              </div>
              <p className="text-gray-700 leading-relaxed mb-6">
                Invigorate your spaces with our exclusive range of mood-lifting sprays and long-lasting diffusers. Carefully blended scents for homes, offices, and on-the-go luxury.
              </p>
              
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <Sparkles className="w-5 h-5 text-blue-600 mt-1 flex-shrink-0" />
                  <div>
                    <h5 className="font-semibold text-gray-800">Premium Sprays</h5>
                    <p className="text-sm text-gray-600">Instant mood-lifting fragrances for any space</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <Sparkles className="w-5 h-5 text-blue-600 mt-1 flex-shrink-0" />
                  <div>
                    <h5 className="font-semibold text-gray-800">Long-lasting Diffusers</h5>
                    <p className="text-sm text-gray-600">Continuous fragrance for weeks of enjoyment</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <Sparkles className="w-5 h-5 text-blue-600 mt-1 flex-shrink-0" />
                  <div>
                    <h5 className="font-semibold text-gray-800">Car Fresheners</h5>
                    <p className="text-sm text-gray-600">On-the-go luxury for your daily commute</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <Sparkles className="w-5 h-5 text-blue-600 mt-1 flex-shrink-0" />
                  <div>
                    <h5 className="font-semibold text-gray-800">Office & Home Use</h5>
                    <p className="text-sm text-gray-600">Perfect for any environment you want to enhance</p>
                  </div>
                </div>
              </div>

              {/* View More Button */}
              <div className="mt-8">
                <a
                  href="/pages/handmade-gift-sets"
                  className="inline-flex items-center bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors duration-200 shadow-lg hover:shadow-xl"
                >
                  View More
                  <ArrowRight className="w-5 h-5 ml-2" />
                </a>
              </div>
            </div>

            {/* Handmade Gift Sets */}
            <div className="bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl p-8 hover:shadow-lg transition-shadow">
              <div className="flex items-center mb-6">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mr-4">
                  <Gift className="w-8 h-8 text-purple-600" />
                </div>
                <h4 className="text-2xl font-bold text-gray-900">Handmade Gift Sets</h4>
              </div>
              <p className="text-gray-700 leading-relaxed mb-6">
                Artisan-crafted, limited-edition gift sets lovingly made by our partner artisans and curated with a designer&apos;s touch. Each box is thoughtfully assembled to offer a unique, meaningful, and memorable gifting experience.
              </p>
              
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <Heart className="w-5 h-5 text-purple-600 mt-1 flex-shrink-0" />
                  <div>
                    <h5 className="font-semibold text-gray-800">Artisan-Crafted</h5>
                    <p className="text-sm text-gray-600">Made by skilled partner artisans with love and care</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <Heart className="w-5 h-5 text-purple-600 mt-1 flex-shrink-0" />
                  <div>
                    <h5 className="font-semibold text-gray-800">Limited Edition</h5>
                    <p className="text-sm text-gray-600">Exclusive designs not available anywhere else</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <Heart className="w-5 h-5 text-purple-600 mt-1 flex-shrink-0" />
                  <div>
                    <h5 className="font-semibold text-gray-800">Designer Curation</h5>
                    <p className="text-sm text-gray-600">Thoughtfully assembled with a designer&apos;s touch</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <Heart className="w-5 h-5 text-purple-600 mt-1 flex-shrink-0" />
                  <div>
                    <h5 className="font-semibold text-gray-800">Meaningful Experience</h5>
                    <p className="text-sm text-gray-600">Each set tells a story and creates lasting memories</p>
                  </div>
                </div>
              </div>

              {/* View More Button */}
              <div className="mt-8">
                <a
                  href="/pages/artisan-series"
                  className="inline-flex items-center bg-purple-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-purple-700 transition-colors duration-200 shadow-lg hover:shadow-xl"
                >
                  View More
                  <ArrowRight className="w-5 h-5 ml-2" />
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Exclusive Banner */}
      <section className="py-16 bg-gradient-to-r from-indigo-600 to-purple-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="flex items-center justify-center mb-6">
            <Star className="w-12 h-12 text-yellow-300 mr-4" />
            <h3 className="text-3xl font-bold text-white">Only at Customine</h3>
            <Star className="w-12 h-12 text-yellow-300 ml-4" />
          </div>
          <p className="text-xl text-indigo-100 mb-8">
            Not found anywhere else. These exclusive products are available only through Customine, making your gifts truly one-of-a-kind.
          </p>
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 max-w-2xl mx-auto">
            <h4 className="text-lg font-semibold text-white mb-3">Why Choose Our In-House Products?</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-indigo-100">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-yellow-300 rounded-full mr-3"></div>
                Exclusively designed for Customine
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-yellow-300 rounded-full mr-3"></div>
                Handcrafted with premium materials
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-yellow-300 rounded-full mr-3"></div>
                Limited quantities ensure uniqueness
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-yellow-300 rounded-full mr-3"></div>
                Perfect for standalone or gift box additions
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Usage Ideas Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">Perfect For</h3>
            <p className="text-lg text-gray-600">Versatile products for every occasion and space</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Gift className="w-6 h-6 text-green-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">Standalone Gifts</h4>
              <p className="text-gray-600 text-sm">Perfect as thoughtful individual presents for any occasion</p>
            </div>

            <div className="bg-white rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Sparkles className="w-6 h-6 text-blue-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">Gift Box Additions</h4>
              <p className="text-gray-600 text-sm">Enhance your custom gift boxes with exclusive touches</p>
            </div>

            <div className="bg-white rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Home className="w-6 h-6 text-purple-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">Personal Use</h4>
              <p className="text-gray-600 text-sm">Elevate your own living spaces with premium products</p>
            </div>
          </div>
        </div>
      </section>

      {/* Product Showcase - Amazon Style */}
      <section className="py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-8">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">Our Exclusive Products</h3>
            <p className="text-lg text-gray-600">Handcrafted items available only at Customine</p>
          </div>

          {/* Product Grid Container with Border */}
          <div className="border-2 border-blue-500 rounded-lg p-6 bg-white">
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">

            {/* Product 1 - Aromatherapy Gel Wick Hanging Car Perfume */}
            <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-lg transition-shadow">
              <div className="aspect-square bg-gray-100 rounded-lg mb-3 relative overflow-hidden">
                <img
                  src="/placeholder-product.png"
                  alt="Aromatherapy Gel Wick Hanging Car Perfume - Berry Lavender"
                  className="w-full h-full object-cover"
                />
                <div className="absolute top-2 left-2 bg-red-600 text-white text-xs px-2 py-1 rounded">
                  Amazon's Choice
                </div>
              </div>
              <div className="space-y-2">
                <p className="text-sm text-gray-600">Customine Fresh</p>
                <h3 className="text-sm font-medium text-gray-900 line-clamp-2">
                  Aromatherapy Gel Wick Hanging Car Perfume - Berry Lavender, Lasting Gel Based Car Air...
                </h3>
                <div className="flex items-center space-x-1">
                  <div className="flex text-yellow-400">
                    {[...Array(4)].map((_, i) => (
                      <Star key={i} className="w-3 h-3 fill-current" />
                    ))}
                    <Star className="w-3 h-3" />
                  </div>
                  <span className="text-xs text-gray-600">752</span>
                </div>
                <div className="space-y-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg font-bold text-gray-900">₹224</span>
                    <span className="text-xs text-gray-500 line-through">₹299</span>
                    <span className="text-xs text-green-600">(25% off)</span>
                  </div>
                  <p className="text-xs text-gray-600">Save extra with Customine offers</p>
                  <p className="text-xs text-blue-600">FREE delivery Mon, 23 Jul</p>
                  <p className="text-xs text-gray-600">Or fastest delivery Mon, 23 Jul</p>
                </div>
                <button className="w-full bg-yellow-400 hover:bg-yellow-500 text-black text-sm font-medium py-2 px-4 rounded-lg transition-colors">
                  Add to cart
                </button>
              </div>
            </div>

            {/* Product 2 - Godrej aer Twist, Car Air Freshener */}
            <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-lg transition-shadow">
              <div className="aspect-square bg-gray-100 rounded-lg mb-3 relative overflow-hidden">
                <img
                  src="/placeholder-product.png"
                  alt="Godrej aer Twist, Car Air Freshener"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="space-y-2">
                <p className="text-sm text-gray-600">Customine Fresh</p>
                <h3 className="text-sm font-medium text-gray-900 line-clamp-2">
                  Godrej aer Twist, Car Air Freshener - Cool Surf Blue (45ml)
                </h3>
                <div className="flex items-center space-x-1">
                  <div className="flex text-yellow-400">
                    {[...Array(4)].map((_, i) => (
                      <Star key={i} className="w-3 h-3 fill-current" />
                    ))}
                    <Star className="w-3 h-3" />
                  </div>
                  <span className="text-xs text-gray-600">1,876</span>
                </div>
                <div className="space-y-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg font-bold text-gray-900">₹368</span>
                    <span className="text-xs text-gray-500 line-through">₹450</span>
                    <span className="text-xs text-green-600">(18% off)</span>
                  </div>
                  <p className="text-xs text-gray-600">Save extra with Customine offers</p>
                  <p className="text-xs text-blue-600">FREE delivery Mon, 23 Jul</p>
                  <p className="text-xs text-gray-600">Or fastest delivery Mon, 23 Jul</p>
                </div>
                <button className="w-full bg-yellow-400 hover:bg-yellow-500 text-black text-sm font-medium py-2 px-4 rounded-lg transition-colors">
                  Add to cart
                </button>
              </div>
            </div>

            {/* Product 3 - Involve Your Senses One Musk */}
            <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-lg transition-shadow">
              <div className="aspect-square bg-gray-100 rounded-lg mb-3 relative overflow-hidden">
                <img
                  src="/placeholder-product.png"
                  alt="Involve Your Senses One Musk Potpourri Car Air Freshener"
                  className="w-full h-full object-cover"
                />
                <div className="absolute top-2 left-2 bg-orange-600 text-white text-xs px-2 py-1 rounded">
                  Best Seller
                </div>
              </div>
              <div className="space-y-2">
                <p className="text-sm text-gray-600">Customine Fresh</p>
                <h3 className="text-sm font-medium text-gray-900 line-clamp-2">
                  Involve Your Senses One Musk Potpourri Car Air Freshener for Freshening Your...
                </h3>
                <div className="flex items-center space-x-1">
                  <div className="flex text-yellow-400">
                    {[...Array(4)].map((_, i) => (
                      <Star key={i} className="w-3 h-3 fill-current" />
                    ))}
                    <Star className="w-3 h-3" />
                  </div>
                  <span className="text-xs text-gray-600">156</span>
                </div>
                <div className="space-y-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg font-bold text-gray-900">₹369</span>
                    <span className="text-xs text-gray-500 line-through">₹499</span>
                    <span className="text-xs text-green-600">(26% off)</span>
                  </div>
                  <p className="text-xs text-gray-600">Save extra with Customine offers</p>
                  <p className="text-xs text-blue-600">FREE delivery Mon, 23 Jul</p>
                  <p className="text-xs text-gray-600">Or fastest delivery Mon, 23 Jul</p>
                </div>
                <button className="w-full bg-yellow-400 hover:bg-yellow-500 text-black text-sm font-medium py-2 px-4 rounded-lg transition-colors">
                  Add to cart
                </button>
              </div>
            </div>

            {/* Product 4 - ARGANICE Aroma Swing */}
            <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-lg transition-shadow">
              <div className="aspect-square bg-gray-100 rounded-lg mb-3 relative overflow-hidden">
                <img
                  src="/placeholder-product.png"
                  alt="ARGANICE Aroma Swing - Pack of 3 Pocket Car Air Freshener"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="space-y-2">
                <p className="text-sm text-gray-600">Customine Fresh</p>
                <h3 className="text-sm font-medium text-gray-900 line-clamp-2">
                  ARGANICE Aroma Swing - Pack of 3 Pocket Car Air Freshener, Aromatic Essential Oils in...
                </h3>
                <div className="flex items-center space-x-1">
                  <div className="flex text-yellow-400">
                    {[...Array(4)].map((_, i) => (
                      <Star key={i} className="w-3 h-3 fill-current" />
                    ))}
                    <Star className="w-3 h-3" />
                  </div>
                  <span className="text-xs text-gray-600">203</span>
                </div>
                <div className="space-y-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg font-bold text-gray-900">₹239</span>
                    <span className="text-xs text-gray-500 line-through">₹299</span>
                    <span className="text-xs text-green-600">(20% off)</span>
                  </div>
                  <p className="text-xs text-gray-600">Save extra with Customine offers</p>
                  <p className="text-xs text-blue-600">FREE delivery Mon, 23 Jul</p>
                  <p className="text-xs text-gray-600">Or fastest delivery Mon, 23 Jul</p>
                </div>
                <button className="w-full bg-yellow-400 hover:bg-yellow-500 text-black text-sm font-medium py-2 px-4 rounded-lg transition-colors">
                  Add to cart
                </button>
              </div>
            </div>

            {/* Product 5 - Aromatherapy Gel Wick Hanging Car Perfume (Row 1 End) */}
            <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-lg transition-shadow">
              <div className="aspect-square bg-gray-100 rounded-lg mb-3 relative overflow-hidden">
                <img
                  src="/placeholder-product.png"
                  alt="Aromatherapy Gel Wick Hanging Car Perfume - Berry Lavender"
                  className="w-full h-full object-cover"
                />
                <div className="absolute top-2 left-2 bg-red-600 text-white text-xs px-2 py-1 rounded">
                  Limited Deal
                </div>
              </div>
              <div className="space-y-2">
                <p className="text-sm text-gray-600">Customine Fresh</p>
                <h3 className="text-sm font-medium text-gray-900 line-clamp-2">
                  Aromatherapy Gel Wick Hanging Car Perfume - Berry Lavender, Lasting Gel Based Car Air...
                </h3>
                <div className="flex items-center space-x-1">
                  <div className="flex text-yellow-400">
                    {[...Array(4)].map((_, i) => (
                      <Star key={i} className="w-3 h-3 fill-current" />
                    ))}
                    <Star className="w-3 h-3" />
                  </div>
                  <span className="text-xs text-gray-600">752</span>
                </div>
                <div className="space-y-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg font-bold text-gray-900">₹224</span>
                    <span className="text-xs text-gray-500 line-through">₹299</span>
                    <span className="text-xs text-green-600">(25% off)</span>
                  </div>
                  <p className="text-xs text-gray-600">Save extra with Customine offers</p>
                  <p className="text-xs text-blue-600">FREE delivery Mon, 23 Jul</p>
                  <p className="text-xs text-gray-600">Or fastest delivery Mon, 23 Jul</p>
                </div>
                <button className="w-full bg-yellow-400 hover:bg-yellow-500 text-black text-sm font-medium py-2 px-4 rounded-lg transition-colors">
                  Add to cart
                </button>
              </div>
            </div>

            {/* Product 6 - Godrej aer Twist (Row 2) */}
            <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-lg transition-shadow">
              <div className="aspect-square bg-gray-100 rounded-lg mb-3 relative overflow-hidden">
                <img
                  src="/placeholder-product.png"
                  alt="Godrej aer Twist, Car Air Freshener"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="space-y-2">
                <p className="text-sm text-gray-600">Customine Fresh</p>
                <h3 className="text-sm font-medium text-gray-900 line-clamp-2">
                  Godrej aer Twist, Car Air Freshener - Cool Surf Blue (45ml)
                </h3>
                <div className="flex items-center space-x-1">
                  <div className="flex text-yellow-400">
                    {[...Array(4)].map((_, i) => (
                      <Star key={i} className="w-3 h-3 fill-current" />
                    ))}
                    <Star className="w-3 h-3" />
                  </div>
                  <span className="text-xs text-gray-600">1,876</span>
                </div>
                <div className="space-y-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg font-bold text-gray-900">₹368</span>
                    <span className="text-xs text-gray-500 line-through">₹450</span>
                    <span className="text-xs text-green-600">(18% off)</span>
                  </div>
                  <p className="text-xs text-gray-600">Save extra with Customine offers</p>
                  <p className="text-xs text-blue-600">FREE delivery Mon, 23 Jul</p>
                  <p className="text-xs text-gray-600">Or fastest delivery Mon, 23 Jul</p>
                </div>
                <button className="w-full bg-yellow-400 hover:bg-yellow-500 text-black text-sm font-medium py-2 px-4 rounded-lg transition-colors">
                  Add to cart
                </button>
              </div>
            </div>

            {/* Product 7 - Involve Your Senses One Musk (Row 2) */}
            <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-lg transition-shadow">
              <div className="aspect-square bg-gray-100 rounded-lg mb-3 relative overflow-hidden">
                <img
                  src="/placeholder-product.png"
                  alt="Involve Your Senses One Musk Potpourri Car Air Freshener"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="space-y-2">
                <p className="text-sm text-gray-600">Customine Fresh</p>
                <h3 className="text-sm font-medium text-gray-900 line-clamp-2">
                  Involve Your Senses One Musk Potpourri Car Air Freshener for Freshening Your...
                </h3>
                <div className="flex items-center space-x-1">
                  <div className="flex text-yellow-400">
                    {[...Array(4)].map((_, i) => (
                      <Star key={i} className="w-3 h-3 fill-current" />
                    ))}
                    <Star className="w-3 h-3" />
                  </div>
                  <span className="text-xs text-gray-600">156</span>
                </div>
                <div className="space-y-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg font-bold text-gray-900">₹369</span>
                    <span className="text-xs text-gray-500 line-through">₹499</span>
                    <span className="text-xs text-green-600">(26% off)</span>
                  </div>
                  <p className="text-xs text-gray-600">Save extra with Customine offers</p>
                  <p className="text-xs text-blue-600">FREE delivery Mon, 23 Jul</p>
                  <p className="text-xs text-gray-600">Or fastest delivery Mon, 23 Jul</p>
                </div>
                <button className="w-full bg-yellow-400 hover:bg-yellow-500 text-black text-sm font-medium py-2 px-4 rounded-lg transition-colors">
                  Add to cart
                </button>
              </div>
            </div>

            {/* Product 8 - ARGANICE Aroma Swing (Row 2) */}
            <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-lg transition-shadow">
              <div className="aspect-square bg-gray-100 rounded-lg mb-3 relative overflow-hidden">
                <img
                  src="/placeholder-product.png"
                  alt="ARGANICE Aroma Swing - Pack of 3 Pocket Car Air Freshener"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="space-y-2">
                <p className="text-sm text-gray-600">Customine Fresh</p>
                <h3 className="text-sm font-medium text-gray-900 line-clamp-2">
                  ARGANICE Aroma Swing - Pack of 3 Pocket Car Air Freshener, Aromatic Essential Oils in...
                </h3>
                <div className="flex items-center space-x-1">
                  <div className="flex text-yellow-400">
                    {[...Array(4)].map((_, i) => (
                      <Star key={i} className="w-3 h-3 fill-current" />
                    ))}
                    <Star className="w-3 h-3" />
                  </div>
                  <span className="text-xs text-gray-600">203</span>
                </div>
                <div className="space-y-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg font-bold text-gray-900">₹239</span>
                    <span className="text-xs text-gray-500 line-through">₹299</span>
                    <span className="text-xs text-green-600">(20% off)</span>
                  </div>
                  <p className="text-xs text-gray-600">Save extra with Customine offers</p>
                  <p className="text-xs text-blue-600">FREE delivery Mon, 23 Jul</p>
                  <p className="text-xs text-gray-600">Or fastest delivery Mon, 23 Jul</p>
                </div>
                <button className="w-full bg-yellow-400 hover:bg-yellow-500 text-black text-sm font-medium py-2 px-4 rounded-lg transition-colors">
                  Add to cart
                </button>
              </div>
            </div>

            {/* Product 9 - Airpaper Aroma Swing */}
            <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-lg transition-shadow">
              <div className="aspect-square bg-gray-100 rounded-lg mb-3 relative overflow-hidden">
                <img
                  src="/placeholder-product.png"
                  alt="Airpaper Aroma Swing - Pack of 3 Pocket Car Air Freshener"
                  className="w-full h-full object-cover"
                />
                <div className="absolute top-2 left-2 bg-green-600 text-white text-xs px-2 py-1 rounded">
                  New Launch
                </div>
              </div>
              <div className="space-y-2">
                <p className="text-sm text-gray-600">Customine Fresh</p>
                <h3 className="text-sm font-medium text-gray-900 line-clamp-2">
                  Airpaper Aroma Swing - Pack of 3 Pocket Car Air Freshener, Lavender & Black Quilt Car...
                </h3>
                <div className="flex items-center space-x-1">
                  <div className="flex text-yellow-400">
                    {[...Array(4)].map((_, i) => (
                      <Star key={i} className="w-3 h-3 fill-current" />
                    ))}
                    <Star className="w-3 h-3" />
                  </div>
                  <span className="text-xs text-gray-600">89</span>
                </div>
                <div className="space-y-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg font-bold text-gray-900">₹259</span>
                    <span className="text-xs text-gray-500 line-through">₹329</span>
                    <span className="text-xs text-green-600">(21% off)</span>
                  </div>
                  <p className="text-xs text-gray-600">Save extra with Customine offers</p>
                  <p className="text-xs text-blue-600">FREE delivery Mon, 23 Jul</p>
                  <p className="text-xs text-gray-600">Or fastest delivery Mon, 23 Jul</p>
                </div>
                <button className="w-full bg-yellow-400 hover:bg-yellow-500 text-black text-sm font-medium py-2 px-4 rounded-lg transition-colors">
                  Add to cart
                </button>
              </div>
            </div>

            {/* Product 10 - Premium Car Air Freshener Collection */}
            <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-lg transition-shadow">
              <div className="aspect-square bg-gray-100 rounded-lg mb-3 relative overflow-hidden">
                <img
                  src="/placeholder-product.png"
                  alt="Premium Car Air Freshener Collection"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="space-y-2">
                <p className="text-sm text-gray-600">Customine Fresh</p>
                <h3 className="text-sm font-medium text-gray-900 line-clamp-2">
                  Premium Car Air Freshener Collection - Multi-Pack Variety Set with Essential Oils...
                </h3>
                <div className="flex items-center space-x-1">
                  <div className="flex text-yellow-400">
                    {[...Array(4)].map((_, i) => (
                      <Star key={i} className="w-3 h-3 fill-current" />
                    ))}
                    <Star className="w-3 h-3" />
                  </div>
                  <span className="text-xs text-gray-600">324</span>
                </div>
                <div className="space-y-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg font-bold text-gray-900">₹399</span>
                    <span className="text-xs text-gray-500 line-through">₹499</span>
                    <span className="text-xs text-green-600">(20% off)</span>
                  </div>
                  <p className="text-xs text-gray-600">Save extra with Customine offers</p>
                  <p className="text-xs text-blue-600">FREE delivery Mon, 23 Jul</p>
                  <p className="text-xs text-gray-600">Or fastest delivery Mon, 23 Jul</p>
                </div>
                <button className="w-full bg-yellow-400 hover:bg-yellow-500 text-black text-sm font-medium py-2 px-4 rounded-lg transition-colors">
                  Add to cart
                </button>
              </div>
            </div>

            </div>

            {/* Shop Now Button */}
            <div className="mt-8 text-center">
              <button className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-8 rounded-lg transition-colors">
                Shop Now
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h3 className="text-3xl font-bold text-gray-900 mb-4">Ready to Experience Exclusivity?</h3>
          <p className="text-lg text-gray-600 mb-8">Discover our unique in-house products and add that special touch to your gifting</p>
          
          <div className="bg-gradient-to-r from-indigo-500 to-purple-500 rounded-2xl p-8 text-white">
            <h4 className="text-2xl font-bold mb-4">Get Your Exclusive Products</h4>
            <p className="text-lg mb-6 opacity-90">
              Contact us to learn more about our in-house products and how to include them in your custom gift experience.
            </p>
            <a
              href="/pages/special-occasion-inquiry"
              className="inline-flex items-center bg-white text-indigo-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-200"
            >
              Inquire About In-House Products
              <ArrowRight className="w-5 h-5 ml-2" />
            </a>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 bg-gray-900 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h3 className="text-2xl font-bold mb-6">Questions About Our Exclusive Products?</h3>
          <p className="text-lg text-gray-300 mb-8">
            We&apos;d love to tell you more about our unique in-house collection
          </p>
          <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-8">
            <a
              href="mailto:<EMAIL>"
              className="flex items-center text-indigo-400 hover:text-indigo-300 transition-colors"
            >
              <Mail className="w-5 h-5 mr-2" />
              <EMAIL>
            </a>
            <a
              href="tel:+919876543210"
              className="flex items-center text-indigo-400 hover:text-indigo-300 transition-colors"
            >
              <Phone className="w-5 h-5 mr-2" />
              +91 98765 43210
            </a>
          </div>
        </div>
      </section>
    </div>
  );
}
