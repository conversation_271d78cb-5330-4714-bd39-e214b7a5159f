import { Heart, Users, Gift, Sparkles, ArrowR<PERSON>, Phone, Mail } from 'lucide-react';

export default function BridalPartyGifts() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-pink-50 to-purple-100 py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="flex items-center justify-center mb-6">
            <Users className="w-12 h-12 text-pink-600 mr-4" />
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900">
              Bridal Party & Groomsmen Keepsakes
            </h1>
          </div>
          <h2 className="text-2xl md:text-3xl font-semibold text-pink-700 mb-6">
            Celebrate Your Inner Circle in Style
          </h2>
          <p className="text-xl text-gray-700 leading-relaxed max-w-3xl mx-auto">
            Say &quot;thank you&quot; to your bridesmaids and groomsmen with thoughtful, personalized gifts that celebrate your shared memories.
          </p>
        </div>
      </section>

      {/* Box Ideas Section */}
      <section className="py-16 bg-white">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">Bridal Party Gift Ideas</h3>
            <p className="text-lg text-gray-600">Meaningful keepsakes for your wedding party</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="bg-gray-50 rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-pink-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Heart className="w-8 h-8 text-pink-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">Bridesmaids Essentials</h4>
              <p className="text-gray-600 text-sm">Robes, jewelry, champagne, and getting-ready day essentials</p>
            </div>

            <div className="bg-gray-50 rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="w-8 h-8 text-blue-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">Groomsmen Classics</h4>
              <p className="text-gray-600 text-sm">Flasks, socks, cufflinks, and gentleman&apos;s accessories</p>
            </div>

            <div className="bg-gray-50 rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Sparkles className="w-8 h-8 text-purple-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">Grooming & Wellness</h4>
              <p className="text-gray-600 text-sm">Mini grooming kits, fragrance sets, and self-care items</p>
            </div>

            <div className="bg-gray-50 rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-rose-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Gift className="w-8 h-8 text-rose-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">Memory Keepsakes</h4>
              <p className="text-gray-600 text-sm">Keepsake boxes with heartfelt notes and personalized mementos</p>
            </div>
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">Our Bridal Party Gifting Process</h3>
            <p className="text-lg text-gray-600">Creating special moments for your wedding party</p>
          </div>

          <div className="space-y-12">
            {[
              {
                step: "01",
                title: "Wedding Party Consultation",
                description: "Learn about your wedding party members, their personalities, and your relationship with each person."
              },
              {
                step: "02", 
                title: "Personalized Gift Design",
                description: "Create unique gifts for bridesmaids and groomsmen that reflect your appreciation and their individual styles."
              },
              {
                step: "03",
                title: "Custom Personalization",
                description: "Add names, initials, wedding dates, or special messages to make each gift truly personal."
              },
              {
                step: "04",
                title: "Perfect Timing Delivery",
                description: "Coordinate delivery for the perfect moment - whether it&apos;s the proposal, rehearsal dinner, or wedding day."
              }
            ].map((item, index) => (
              <div key={index} className="flex items-start space-x-6">
                <div className="flex-shrink-0">
                  <div className="w-16 h-16 bg-pink-600 text-white rounded-full flex items-center justify-center font-bold text-xl">
                    {item.step}
                  </div>
                </div>
                <div className="flex-1">
                  <h4 className="text-xl font-semibold text-gray-900 mb-2">{item.title}</h4>
                  <p className="text-gray-600 leading-relaxed">{item.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h3 className="text-3xl font-bold text-gray-900 mb-4">Plan Your Bridal Party Gifts</h3>
          <p className="text-lg text-gray-600 mb-8">Let&apos;s create meaningful keepsakes for your wedding party</p>

          <div className="bg-gradient-to-r from-pink-500 to-purple-500 rounded-2xl p-8 text-white">
            <h4 className="text-2xl font-bold mb-4">Ready to Thank Your Wedding Party?</h4>
            <p className="text-lg mb-6 opacity-90">
              Share details about your wedding party and we&apos;ll create personalized keepsakes that celebrate your special people.
            </p>
            <a
              href="/pages/special-occasion-inquiry"
              className="inline-flex items-center bg-white text-pink-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-200"
            >
              Submit Your Bridal Party Inquiry
              <ArrowRight className="w-5 h-5 ml-2" />
            </a>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 bg-gray-900 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h3 className="text-2xl font-bold mb-6">Ready to Thank Your Wedding Party?</h3>
          <p className="text-lg text-gray-300 mb-8">
            Let&apos;s create beautiful keepsakes that celebrate your special people
          </p>
          <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-8">
            <a
              href="mailto:<EMAIL>"
              className="flex items-center text-pink-400 hover:text-pink-300 transition-colors"
            >
              <Mail className="w-5 h-5 mr-2" />
              <EMAIL>
            </a>
            <a
              href="tel:+919876543210"
              className="flex items-center text-pink-400 hover:text-pink-300 transition-colors"
            >
              <Phone className="w-5 h-5 mr-2" />
              +91 98765 43210
            </a>
          </div>
        </div>
      </section>
    </div>
  );
}
