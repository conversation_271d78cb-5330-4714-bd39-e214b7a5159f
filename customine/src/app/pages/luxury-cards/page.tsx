"use client";

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Star, Gift, ArrowRight, Phone, Mail, Crown, Feather, Diamond } from 'lucide-react';
import GallerySlider from '@/components/GallerySlider';
import { fetchGalleryByCategory, GalleryImage } from '@/utils/galleryAPI';

// Add an array of luxury card images
const luxuryCardImages = [
  '/logo_notecard.png',
  '/logo.png',
  '/file.svg'
];

// Available gradient styles for sliders
const GRADIENT_STYLES = [
  'from-purple-600 to-pink-600',
  'from-rose-600 to-red-600',
  'from-blue-600 to-indigo-600',
  'from-green-600 to-teal-600',
  'from-orange-600 to-red-600',
  'from-indigo-600 to-purple-600',
  'from-teal-600 to-blue-600',
  'from-pink-600 to-rose-600'
];

// Interface for subcategory data
interface SubcategoryData {
  subcategory: string;
  displayName: string;
  category: 'HOME' | 'LUXURY' | 'CORPORATE' | 'WEDDING' | 'FESTIVAL' | 'PERSONAL';
  gradient: string;
  imageCount: number;
}

// Component to display dynamic gallery sliders based on actual subcategories in database
// Aspect ratio options: aspect-[4/3] (current), aspect-[3/2], aspect-[16/9], aspect-video, aspect-square
function DynamicGallerySliders() {
  const [subcategories, setSubcategories] = useState<SubcategoryData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Convert subcategory value to display name
  const getSubcategoryDisplayName = (subcategory: string) => {
    const subcategoryMap: { [key: string]: string } = {
      'ACRYLIC_CARDS': 'Acrylic Cards',
      'LASERCUT_CARDS': 'Lasercut Cards',
      'BOXED_CARDS': 'Boxed Cards',
      'ECO_FRIENDLY_CARDS': 'Eco-Friendly Cards',
      'HANDMADE_ARTISAN_CARDS': 'Handmade Artisan Cards',
      'TRANSPARENT_GLASS_CARDS': 'Transparent Glass Cards',
      'THEME_BASED_CARDS': 'Theme-Based Cards'
    };

    return subcategoryMap[subcategory] || subcategory.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
  };

  useEffect(() => {
    const fetchSubcategories = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch from multiple categories to get diverse content
        const [luxuryData, weddingData, corporateData, homeData, festivalData, personalData] = await Promise.all([
          fetchGalleryByCategory('LUXURY', 50),
          fetchGalleryByCategory('WEDDING', 50),
          fetchGalleryByCategory('CORPORATE', 50),
          fetchGalleryByCategory('HOME', 50),
          fetchGalleryByCategory('FESTIVAL', 50),
          fetchGalleryByCategory('PERSONAL', 50)
        ]);

        // Combine all data
        const allData = [...luxuryData, ...weddingData, ...corporateData, ...homeData, ...festivalData, ...personalData];

        // Group by subcategory and count images
        const subcategoryMap = new Map<string, SubcategoryData>();

        console.log('🔍 Processing gallery data:', allData.length, 'total images');

        allData.forEach(image => {
          if (image.subcategory && image.isActive) {
            const key = `${image.category}_${image.subcategory}`;
            console.log(`📸 Found image: ${image.title} (${image.category} > ${image.subcategory})`);

            if (!subcategoryMap.has(key)) {
              subcategoryMap.set(key, {
                subcategory: image.subcategory,
                displayName: getSubcategoryDisplayName(image.subcategory),
                category: image.category,
                gradient: GRADIENT_STYLES[subcategoryMap.size % GRADIENT_STYLES.length],
                imageCount: 0
              });
            }

            const existing = subcategoryMap.get(key)!;
            existing.imageCount++;
          }
        });

        console.log('📊 Subcategory map:', Array.from(subcategoryMap.values()));

        // Convert to array and sort by image count (descending)
        const subcategoriesArray = Array.from(subcategoryMap.values())
          .filter(sub => sub.imageCount > 0) // Only include subcategories with images
          .sort((a, b) => b.imageCount - a.imageCount);

        setSubcategories(subcategoriesArray);
      } catch (err) {
        console.error('Error fetching subcategories:', err);
        setError('Failed to load gallery subcategories');
      } finally {
        setLoading(false);
      }
    };

    fetchSubcategories();
  }, []);

  if (loading) {
    return (
      <section className="py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-2">Our Gallery Showcase</h3>
            <p className="text-gray-600">Loading our diverse collection...</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 justify-items-center">
            {[1, 2, 3].map((i) => (
              <div key={i} className="w-full aspect-[4/3] max-w-sm rounded-xl shadow-lg overflow-hidden bg-gray-200 animate-pulse">
                <div className="p-3 bg-gray-300">
                  <div className="h-4 bg-gray-400 rounded"></div>
                </div>
                <div className="h-full bg-gray-200"></div>
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className="py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-2">Our Gallery Showcase</h3>
            <p className="text-red-600">{error}</p>
          </div>
        </div>
      </section>
    );
  }

  // Fallback to main categories if no subcategories found
  if (subcategories.length === 0) {
    const fallbackCategories = [
      { category: 'LUXURY' as const, displayName: 'Luxury Collection', gradient: 'from-purple-600 to-pink-600' },
      { category: 'WEDDING' as const, displayName: 'Wedding Collection', gradient: 'from-rose-600 to-red-600' },
      { category: 'CORPORATE' as const, displayName: 'Corporate Collection', gradient: 'from-blue-600 to-indigo-600' }
    ];

    return (
      <section className="py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-2">Our Gallery Showcase</h3>
            <p className="text-gray-600">Explore our diverse collection across different categories</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 justify-items-center">
            {fallbackCategories.map((categoryData) => (
              <div key={categoryData.category} className="w-full aspect-[4/3] max-w-sm rounded-xl shadow-lg overflow-hidden bg-white">
                <div className={`p-3 bg-gradient-to-r ${categoryData.gradient} text-white text-center`}>
                  <h4 className="font-semibold text-sm">{categoryData.displayName}</h4>
                  <p className="text-xs opacity-90">Category Collection</p>
                </div>
                <div className="h-full">
                  <GallerySlider
                    useGalleryAPI={true}
                    galleryCategory={categoryData.category}
                    height="3xl"
                    rounded={false}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  // Determine grid layout based on number of subcategories
  const getGridCols = (count: number) => {
    if (count === 1) return 'grid-cols-1';
    if (count === 2) return 'grid-cols-1 md:grid-cols-2';
    if (count === 3) return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3';
    if (count === 4) return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4';
    return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4';
  };

  return (
    <section className="py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8">
          <h3 className="text-2xl font-bold text-gray-900 mb-2">Our Gallery Showcase</h3>
          <p className="text-gray-600">
            Explore our diverse collection across {subcategories.length} different {subcategories.length === 1 ? 'category' : 'categories'}
          </p>
        </div>
        <div className={`grid ${getGridCols(subcategories.length)} gap-8 justify-items-center`}>
          {subcategories.map((subcategoryData) => (
            <div key={`${subcategoryData.category}_${subcategoryData.subcategory}`} className="w-full aspect-[4/3] max-w-sm rounded-xl shadow-lg overflow-hidden bg-white">
              <div className={`p-3 bg-gradient-to-r ${subcategoryData.gradient} text-white text-center`}>
                <h4 className="font-semibold text-sm">{subcategoryData.displayName}</h4>
              </div>
              <div className="h-full">
                <GallerySlider
                  useGalleryAPI={true}
                  galleryCategory={subcategoryData.category}
                  gallerySubcategory={subcategoryData.subcategory}
                  height="3xl"
                  rounded={false}
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}

export default function LuxuryCards() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-amber-50 to-rose-100 py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="flex items-center justify-center mb-6">
            <Crown className="w-12 h-12 text-amber-600 mr-4" />
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900">
              Luxury & Boutique Style Cards
            </h1>
          </div>
          <h2 className="text-2xl md:text-3xl font-semibold text-amber-700 mb-6">
            Where Words Meet Elegance
          </h2>
          <p className="text-xl text-gray-700 leading-relaxed max-w-4xl mx-auto mb-6">
            At Customine, we believe that the right words, beautifully expressed, can turn a simple gesture into a timeless memory. Our boutique-style cards are more than just stationery — they&apos;re keepsakes crafted with purpose, poise, and a touch of luxury.
          </p>
        </div>
      </section>
      {/* Dynamic Gallery Section */}
      <DynamicGallerySliders />

      {/* What Sets Our Cards Apart Section */}
      <section className="py-16 bg-white">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">What Sets Our Cards Apart</h3>
            <p className="text-lg text-gray-600">Crafted with purpose, poise, and luxury in every detail</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            
            {/* Premium Textures & Finishes */}
            <div className="bg-gradient-to-br from-amber-50 to-yellow-50 rounded-2xl p-8 hover:shadow-lg transition-shadow">
              <div className="flex items-center mb-6">
                <div className="w-16 h-16 bg-amber-100 rounded-full flex items-center justify-center mr-4">
                  <Diamond className="w-8 h-8 text-amber-600" />
                </div>
                <h4 className="text-2xl font-bold text-gray-900">Premium Textures & Finishes</h4>
              </div>
              <p className="text-gray-700 leading-relaxed">
                From soft-touch matte to metallic foiling, each card is designed to feel as special as the message inside.
              </p>
            </div>

            {/* Timeless Designs */}
            <div className="bg-gradient-to-br from-rose-50 to-pink-50 rounded-2xl p-8 hover:shadow-lg transition-shadow">
              <div className="flex items-center mb-6">
                <div className="w-16 h-16 bg-rose-100 rounded-full flex items-center justify-center mr-4">
                  <Feather className="w-8 h-8 text-rose-600" />
                </div>
                <h4 className="text-2xl font-bold text-gray-900">Timeless Designs</h4>
              </div>
              <p className="text-gray-700 leading-relaxed">
                Understated elegance meets modern minimalism — designed to complement any celebration, not overwhelm it.
              </p>
            </div>

            {/* Personalized Touches */}
            <div className="bg-gradient-to-br from-purple-50 to-indigo-50 rounded-2xl p-8 hover:shadow-lg transition-shadow">
              <div className="flex items-center mb-6">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mr-4">
                  <Heart className="w-8 h-8 text-purple-600" />
                </div>
                <h4 className="text-2xl font-bold text-gray-900">Personalized Touches</h4>
              </div>
              <p className="text-gray-700 leading-relaxed">
                Include your name, initials, or custom message — all styled to match your aesthetic and event branding.
              </p>
            </div>

            {/* Limited Editions */}
            <div className="bg-gradient-to-br from-emerald-50 to-teal-50 rounded-2xl p-8 hover:shadow-lg transition-shadow">
              <div className="flex items-center mb-6">
                <div className="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mr-4">
                  <Star className="w-8 h-8 text-emerald-600" />
                </div>
                <h4 className="text-2xl font-bold text-gray-900">Limited Editions</h4>
              </div>
              <p className="text-gray-700 leading-relaxed">
                Many of our card designs are seasonal or one-time runs, making each collection truly exclusive.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Perfect For Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">Perfect For</h3>
            <p className="text-lg text-gray-600">Elegant cards for life&apos;s most meaningful moments</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
              <div className="w-12 h-12 bg-pink-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Heart className="w-6 h-6 text-pink-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">Wedding Thank-You Notes</h4>
              <p className="text-gray-600 text-sm">Express gratitude with elegance and style</p>
            </div>

            <div className="bg-white rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Sparkles className="w-6 h-6 text-purple-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">Proposal or Bridesmaid Invites</h4>
              <p className="text-gray-600 text-sm">Make your special requests unforgettable</p>
            </div>

            <div className="bg-white rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
              <div className="w-12 h-12 bg-amber-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Star className="w-6 h-6 text-amber-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">Milestone Birthdays</h4>
              <p className="text-gray-600 text-sm">Celebrate significant years with luxury</p>
            </div>

            <div className="bg-white rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Gift className="w-6 h-6 text-blue-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">Baby Announcements</h4>
              <p className="text-gray-600 text-sm">Share your joy with beautiful announcements</p>
            </div>

            <div className="bg-white rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
              <div className="w-12 h-12 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Crown className="w-6 h-6 text-emerald-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">Luxury Corporate Gifting</h4>
              <p className="text-gray-600 text-sm">Professional elegance for business relationships</p>
            </div>

            <div className="bg-white rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
              <div className="w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Feather className="w-6 h-6 text-indigo-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">Personalized Event Message Cards</h4>
              <p className="text-gray-600 text-sm">Custom messages for any special occasion</p>
            </div>
          </div>
        </div>
      </section>

      {/* Add-On Option Section */}
      <section className="py-16 bg-gradient-to-r from-amber-600 to-rose-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="flex items-center justify-center mb-6">
            <Gift className="w-12 h-12 text-yellow-200 mr-4" />
            <h3 className="text-3xl font-bold text-white">Add-On Option for Customine Gift Boxes</h3>
          </div>
          <p className="text-xl text-amber-100 mb-8">
            Elevate your gifting by including one of our Signature Cards handwritten or printed with your personalized message and tucked inside your curated gift box.
          </p>
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 max-w-2xl mx-auto">
            <p className="text-lg text-white font-medium italic">
              &quot;Because luxury isn&apos;t just about what&apos;s inside the box — it&apos;s also in how you say it.&quot;
            </p>
          </div>
        </div>
      </section>

      {/* Features Showcase */}
      <section className="py-16 bg-white">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">Luxury in Every Detail</h3>
            <p className="text-lg text-gray-600">Experience the difference quality makes</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-20 h-20 bg-gradient-to-br from-amber-100 to-rose-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Diamond className="w-10 h-10 text-amber-600" />
              </div>
              <h4 className="text-xl font-semibold text-gray-900 mb-3">Premium Materials</h4>
              <p className="text-gray-600">Soft-touch matte, metallic foiling, and luxury paper stocks</p>
            </div>

            <div className="text-center">
              <div className="w-20 h-20 bg-gradient-to-br from-purple-100 to-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Feather className="w-10 h-10 text-purple-600" />
              </div>
              <h4 className="text-xl font-semibold text-gray-900 mb-3">Handwritten Options</h4>
              <p className="text-gray-600">Personal touch with beautiful calligraphy and handwriting</p>
            </div>

            <div className="text-center">
              <div className="w-20 h-20 bg-gradient-to-br from-emerald-100 to-teal-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Star className="w-10 h-10 text-emerald-600" />
              </div>
              <h4 className="text-xl font-semibold text-gray-900 mb-3">Exclusive Designs</h4>
              <p className="text-gray-600">Limited edition collections you won&apos;t find anywhere else</p>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h3 className="text-3xl font-bold text-gray-900 mb-4">Ready to Add Elegance to Your Message?</h3>
          <p className="text-lg text-gray-600 mb-8">Let&apos;s create the perfect luxury card for your special moment</p>
          
          <div className="bg-gradient-to-r from-amber-500 to-rose-500 rounded-2xl p-8 text-white">
            <h4 className="text-2xl font-bold mb-4">Discover Our Luxury Card Collection</h4>
            <p className="text-lg mb-6 opacity-90">
              Contact us to explore our boutique-style cards and find the perfect design for your occasion.
            </p>
            <a
              href="/pages/special-occasion-inquiry"
              className="inline-flex items-center bg-white text-amber-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-200"
            >
              Inquire About Luxury Cards
              <ArrowRight className="w-5 h-5 ml-2" />
            </a>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 bg-gray-900 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h3 className="text-2xl font-bold mb-6">Questions About Our Luxury Cards?</h3>
          <p className="text-lg text-gray-300 mb-8">
            We&apos;d love to help you find the perfect card for your special message
          </p>
          <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-8">
            <a
              href="mailto:<EMAIL>"
              className="flex items-center text-amber-400 hover:text-amber-300 transition-colors"
            >
              <Mail className="w-5 h-5 mr-2" />
              <EMAIL>
            </a>
            <a
              href="tel:+919876543210"
              className="flex items-center text-amber-400 hover:text-amber-300 transition-colors"
            >
              <Phone className="w-5 h-5 mr-2" />
              +91 98765 43210
            </a>
          </div>
        </div>
      </section>
    </div>
  );
}
