import { Handshake, Star, Gift, Crown, <PERSON>R<PERSON>, Phone, Mail } from 'lucide-react';

export default function ClientThankYou() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-emerald-50 to-teal-100 py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="flex items-center justify-center mb-6">
            <Handshake className="w-12 h-12 text-emerald-600 mr-4" />
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900">
              Premium Client Thank You Gifts
            </h1>
          </div>
          <h2 className="text-2xl md:text-3xl font-semibold text-emerald-700 mb-6">
            Turn Gratitude Into Loyalty
          </h2>
          <p className="text-xl text-gray-700 leading-relaxed max-w-3xl mx-auto">
            Show appreciation with beautifully designed boxes that reflect your professionalism and reinforce relationships with high-value clients.
          </p>
        </div>
      </section>

      {/* Box Ideas Section */}
      <section className="py-16 bg-white">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">Premium Thank You Gift Ideas</h3>
            <p className="text-lg text-gray-600">Luxury gifts that express genuine appreciation</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="bg-gray-50 rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-amber-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Crown className="w-8 h-8 text-amber-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">Gourmet Luxuries</h4>
              <p className="text-gray-600 text-sm">Premium chocolates, fine wines, and artisanal delicacies</p>
            </div>

            <div className="bg-gray-50 rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Star className="w-8 h-8 text-purple-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">Luxury Accessories</h4>
              <p className="text-gray-600 text-sm">Designer candles, premium desk accessories, and elegant décor</p>
            </div>

            <div className="bg-gray-50 rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Gift className="w-8 h-8 text-emerald-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">Personalized Items</h4>
              <p className="text-gray-600 text-sm">Custom gifts with client names, logos, or personal touches</p>
            </div>

            <div className="bg-gray-50 rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-rose-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Handshake className="w-8 h-8 text-rose-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">Wellness Kits</h4>
              <p className="text-gray-600 text-sm">Self-care focused thank-you gifts for work-life balance</p>
            </div>
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">Our Client Appreciation Process</h3>
            <p className="text-lg text-gray-600">Building stronger relationships through thoughtful gifting</p>
          </div>

          <div className="space-y-12">
            {[
              {
                step: "01",
                title: "Client Relationship Analysis",
                description: "Understand your client relationships, preferences, and the message you want to convey."
              },
              {
                step: "02", 
                title: "Luxury Gift Curation",
                description: "Select premium items that reflect your brand values and show genuine appreciation."
              },
              {
                step: "03",
                title: "Personalization & Branding",
                description: "Add custom touches, personal messages, and subtle branding elements."
              },
              {
                step: "04",
                title: "White-Glove Delivery",
                description: "Ensure perfect presentation and timing for maximum impact and appreciation."
              }
            ].map((item, index) => (
              <div key={index} className="flex items-start space-x-6">
                <div className="flex-shrink-0">
                  <div className="w-16 h-16 bg-emerald-600 text-white rounded-full flex items-center justify-center font-bold text-xl">
                    {item.step}
                  </div>
                </div>
                <div className="flex-1">
                  <h4 className="text-xl font-semibold text-gray-900 mb-2">{item.title}</h4>
                  <p className="text-gray-600 leading-relaxed">{item.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h3 className="text-3xl font-bold text-gray-900 mb-4">Plan Your Client Thank You Gifts</h3>
          <p className="text-lg text-gray-600 mb-8">Let&apos;s create meaningful gifts that strengthen your client relationships</p>

          <div className="bg-gradient-to-r from-emerald-500 to-teal-500 rounded-2xl p-8 text-white">
            <h4 className="text-2xl font-bold mb-4">Ready to Show Your Appreciation?</h4>
            <p className="text-lg mb-6 opacity-90">
              Share your client appreciation goals and we&apos;ll design premium gifts that turn gratitude into lasting loyalty.
            </p>
            <a
              href="/pages/special-occasion-inquiry"
              className="inline-flex items-center bg-white text-emerald-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-200"
            >
              Submit Your Client Gift Inquiry
              <ArrowRight className="w-5 h-5 ml-2" />
            </a>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 bg-gray-900 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h3 className="text-2xl font-bold mb-6">Ready to Show Your Appreciation?</h3>
          <p className="text-lg text-gray-300 mb-8">
            Let&apos;s create premium gifts that strengthen your client relationships
          </p>
          <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-8">
            <a
              href="mailto:<EMAIL>"
              className="flex items-center text-emerald-400 hover:text-emerald-300 transition-colors"
            >
              <Mail className="w-5 h-5 mr-2" />
              <EMAIL>
            </a>
            <a
              href="tel:+919876543210"
              className="flex items-center text-emerald-400 hover:text-emerald-300 transition-colors"
            >
              <Phone className="w-5 h-5 mr-2" />
              +91 98765 43210
            </a>
          </div>
        </div>
      </section>
    </div>
  );
}
