import Link from 'next/link';
import {
  ArrowLeft,
  ArrowRight,
  Mail,
  Phone,
  MapPin,
  Instagram,
  Facebook,
  Star,
  ShoppingCart,
  MessageSquare
} from 'lucide-react';

export default function ArtisanSeriesPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Back Button */}
      <div className="bg-gray-50 py-4">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <Link
            href="/pages/in-house-products"
            className="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            Back to In-House Products
          </Link>
        </div>
      </div>

      {/* Hero Section with Logo */}
      <section className="bg-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Logo and Branding */}
          <div className="text-center mb-12">
            <div className="flex items-center justify-center mb-8">
              {/* Customine Logo Placeholder */}
              <div className="flex items-center space-x-4">
                <div className="relative">
                  {/* Geometric Logo Design */}
                  <div className="flex items-center space-x-1">
                    <div className="w-8 h-8 bg-cyan-400 transform rotate-45 border-2 border-cyan-500"></div>
                    <div className="w-8 h-8 bg-pink-500 transform rotate-45 border-2 border-pink-600 -ml-4"></div>
                    <div className="w-8 h-8 bg-yellow-400 transform rotate-45 border-2 border-yellow-500 -ml-4"></div>
                    <div className="w-8 h-8 bg-gray-800 transform rotate-45 border-2 border-gray-900 -ml-4"></div>
                  </div>
                </div>
                <div className="text-left">
                  <div className="text-2xl font-bold text-gray-900 tracking-wide">CUSTOMINE</div>
                  <div className="text-sm text-gray-600 tracking-widest">ARTISAN SERIES</div>
                </div>
              </div>
              <div className="ml-8">
                <h1 className="text-4xl md:text-5xl font-bold text-gray-900">
                  Where Craft Meets Class
                </h1>
              </div>
            </div>
          </div>

          {/* Description */}
          <div className="max-w-5xl mx-auto mb-12">
            <p className="text-lg text-gray-700 leading-relaxed">
              Welcome to the <span className="font-semibold text-gray-900">Customine</span> Artisan Series, a curated collection of exquisite handmade creations that bring together the warmth of wood and the strength of metal. Each piece is crafted by master artisans with precision, passion, and a deep respect for traditional craftsmanship, reimagined for modern living.
            </p>
          </div>

          {/* Divider */}
          <div className="border-t border-gray-300 mb-12"></div>
        </div>
      </section>

      {/* Why Choose Section */}
      <section className="py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-8">Why Choose the Artisan Series?</h2>

          <div className="space-y-6">
            {/* Authentic Craftsmanship */}
            <div className="flex items-start space-x-4">
              <div className="font-bold text-gray-900 text-lg">Authentic Craftsmanship:</div>
              <p className="text-gray-700">
                Every product is individually handcrafted, making it truly one-of-a-kind.
              </p>
            </div>

            {/* Premium Materials */}
            <div className="flex items-start space-x-4">
              <div className="font-bold text-gray-900 text-lg">Premium Materials:</div>
              <p className="text-gray-700">
                Sustainably sourced wood and high-grade metal ensure durability and elegance.
              </p>
            </div>

            {/* Timeless Design */}
            <div className="flex items-start space-x-4">
              <div className="font-bold text-gray-900 text-lg">Timeless Design:</div>
              <p className="text-gray-700">
                Perfect balance of rustic charm and contemporary sophistication.
              </p>
            </div>
          </div>

          {/* Yellow Underline */}
          <div className="w-24 h-1 bg-yellow-400 mt-8"></div>
        </div>
      </section>

      {/* Product Showcase - Amazon Style */}
      <section className="py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">

            {/* Product 1 - Wall Shelf */}
            <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-lg transition-shadow">
              <div className="aspect-square bg-gray-100 rounded-lg mb-3 relative overflow-hidden">
                <img
                  src="/api/placeholder/200/200"
                  alt="2-Tier Utility Shelves"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="space-y-2">
                <p className="text-sm text-gray-600">Sponsored</p>
                <h3 className="text-sm font-medium text-gray-900 line-clamp-2">
                  2-Tier Utility Shelves | Wall Mount Float Shelf | Wall Mounted Shelf...
                </h3>
                <div className="flex items-center space-x-1">
                  <div className="flex text-yellow-400">
                    {[...Array(4)].map((_, i) => (
                      <Star key={i} className="w-3 h-3 fill-current" />
                    ))}
                    <Star className="w-3 h-3" />
                  </div>
                  <span className="text-xs text-gray-600">139</span>
                </div>
                <div className="space-y-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg font-bold text-gray-900">₹999</span>
                    <span className="text-xs text-gray-500 line-through">₹2,999</span>
                    <span className="text-xs text-green-600">(75% off)</span>
                  </div>
                  <p className="text-xs text-gray-600">Up to 5% back with Amazon Pay ICICI card</p>
                  <p className="text-xs text-blue-600">FREE delivery Wed, 23 Jul</p>
                  <p className="text-xs text-gray-600">Service: Assembly</p>
                </div>
                <button className="w-full bg-yellow-400 hover:bg-yellow-500 text-black text-sm font-medium py-2 px-4 rounded-lg transition-colors">
                  Add to cart
                </button>
              </div>
            </div>

            {/* Product 2 - Metal Shelf */}
            <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-lg transition-shadow">
              <div className="aspect-square bg-gray-100 rounded-lg mb-3 relative overflow-hidden">
                <img
                  src="/api/placeholder/200/200"
                  alt="Organizer Metal Large Wall Mounted Shelves"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="space-y-2">
                <p className="text-sm text-gray-600">Sponsored</p>
                <h3 className="text-sm font-medium text-gray-900 line-clamp-2">
                  Organizer Metal Large wall mounted shelves Bathroom...
                </h3>
                <div className="flex items-center space-x-1">
                  <div className="flex text-yellow-400">
                    {[...Array(4)].map((_, i) => (
                      <Star key={i} className="w-3 h-3 fill-current" />
                    ))}
                    <Star className="w-3 h-3" />
                  </div>
                  <span className="text-xs text-gray-600">577</span>
                </div>
                <div className="space-y-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg font-bold text-gray-900">₹1,199</span>
                    <span className="text-xs text-gray-500 line-through">₹2,999</span>
                    <span className="text-xs text-green-600">(60% off)</span>
                  </div>
                  <p className="text-xs text-gray-600">Up to 5% back with Amazon Pay ICICI card</p>
                  <p className="text-xs text-blue-600">FREE delivery Tue, 22 Jul</p>
                  <p className="text-xs text-gray-600">Service: Assembly</p>
                </div>
                <button className="w-full bg-yellow-400 hover:bg-yellow-500 text-black text-sm font-medium py-2 px-4 rounded-lg transition-colors">
                  See options
                </button>
              </div>
            </div>

            {/* Product 3 - Wooden Rack */}
            <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-lg transition-shadow">
              <div className="aspect-square bg-gray-100 rounded-lg mb-3 relative overflow-hidden">
                <img
                  src="/api/placeholder/200/200"
                  alt="3-Layer Wooden Book Rack"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="space-y-2">
                <p className="text-sm text-gray-600">Sponsored</p>
                <h3 className="text-sm font-medium text-gray-900 line-clamp-2">
                  3-Layer Wooden Book Rack/Shelf/Stand | Multipurpose...
                </h3>
                <div className="flex items-center space-x-1">
                  <div className="flex text-yellow-400">
                    {[...Array(4)].map((_, i) => (
                      <Star key={i} className="w-3 h-3 fill-current" />
                    ))}
                    <Star className="w-3 h-3" />
                  </div>
                  <span className="text-xs text-gray-600">1,234</span>
                </div>
                <div className="space-y-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg font-bold text-gray-900">₹2,889</span>
                    <span className="text-xs text-gray-500 line-through">₹4,999</span>
                    <span className="text-xs text-green-600">(42% off)</span>
                  </div>
                  <p className="text-xs text-gray-600">Save extra with Amazon Pay ICICI card</p>
                  <p className="text-xs text-blue-600">FREE delivery Tue, 22 Jul</p>
                </div>
                <button className="w-full bg-yellow-400 hover:bg-yellow-500 text-black text-sm font-medium py-2 px-4 rounded-lg transition-colors">
                  See options
                </button>
              </div>
            </div>

            {/* Product 4 - Floating Shelves */}
            <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-lg transition-shadow">
              <div className="aspect-square bg-gray-100 rounded-lg mb-3 relative overflow-hidden">
                <img
                  src="/api/placeholder/200/200"
                  alt="Craftware Walnut Floating Shelves"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="space-y-2">
                <p className="text-sm text-gray-600">Sponsored</p>
                <h3 className="text-sm font-medium text-gray-900 line-clamp-2">
                  Craftware Walnut Floating Shelves 12 inch Deep Rustic Wall Mounted...
                </h3>
                <div className="flex items-center space-x-1">
                  <div className="flex text-yellow-400">
                    {[...Array(4)].map((_, i) => (
                      <Star key={i} className="w-3 h-3 fill-current" />
                    ))}
                    <Star className="w-3 h-3" />
                  </div>
                  <span className="text-xs text-gray-600">19</span>
                </div>
                <div className="space-y-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg font-bold text-gray-900">₹1,499</span>
                    <span className="text-xs text-gray-500 line-through">₹2,999</span>
                    <span className="text-xs text-green-600">(50% off)</span>
                  </div>
                  <p className="text-xs text-gray-600">Up to 5% back with Amazon Pay ICICI card</p>
                  <p className="text-xs text-blue-600">FREE delivery Sun, 27 Jul</p>
                </div>
                <button className="w-full bg-yellow-400 hover:bg-yellow-500 text-black text-sm font-medium py-2 px-4 rounded-lg transition-colors">
                  Add to cart
                </button>
              </div>
            </div>

            {/* Product 5 - Plant Stand */}
            <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-lg transition-shadow">
              <div className="aspect-square bg-gray-100 rounded-lg mb-3 relative overflow-hidden">
                <img
                  src="/api/placeholder/200/200"
                  alt="Dime Store Metal & Wood Corner Plant Stand"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="space-y-2">
                <h3 className="text-sm font-medium text-gray-900 line-clamp-2">
                  Dime Store Metal & Wood Corner Modern 2-Tier Tall Plant Flower Po...
                </h3>
                <div className="flex items-center space-x-1">
                  <div className="flex text-yellow-400">
                    {[...Array(4)].map((_, i) => (
                      <Star key={i} className="w-3 h-3 fill-current" />
                    ))}
                    <Star className="w-3 h-3" />
                  </div>
                  <span className="text-xs text-gray-600">1</span>
                </div>
                <div className="space-y-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg font-bold text-gray-900">₹1,170</span>
                    <span className="text-xs text-gray-500 line-through">₹1,499</span>
                    <span className="text-xs text-green-600">(21% off)</span>
                  </div>
                  <p className="text-xs text-gray-600">Up to 5% back with Amazon Pay ICICI card</p>
                  <p className="text-xs text-blue-600">FREE delivery Wed, 30 Jul</p>
                </div>
                <button className="w-full bg-yellow-400 hover:bg-yellow-500 text-black text-sm font-medium py-2 px-4 rounded-lg transition-colors">
                  Add to cart
                </button>
              </div>
            </div>

          </div>
        </div>
      </section>

      {/* Shop Now Section */}
      <section className="py-16 bg-gradient-to-r from-slate-800 to-gray-900">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="flex items-center justify-center mb-6">
            <ShoppingCart className="w-12 h-12 text-white mr-4" />
            <h3 className="text-3xl font-bold text-white">Shop Now</h3>
          </div>
          <p className="text-xl text-gray-200 mb-8">
            Browse our collections. Pick your vibe.
          </p>
          <p className="text-lg text-gray-300 mb-8">
            Custom-pack options available for gifting & bulk orders.
          </p>
          
          <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6">
            <Link
              href="/collections/all"
              className="bg-white text-slate-800 px-8 py-4 rounded-xl font-semibold hover:bg-gray-100 transition-all duration-300 flex items-center space-x-2 shadow-lg hover:shadow-xl"
            >
              <ShoppingCart className="w-5 h-5" />
              <span>Browse Collections</span>
            </Link>
            <Link
              href="/pages/special-occasion-inquiry"
              className="bg-amber-600 text-white px-8 py-4 rounded-xl font-semibold hover:bg-amber-700 transition-all duration-300 flex items-center space-x-2 shadow-lg hover:shadow-xl"
            >
              <MessageSquare className="w-5 h-5" />
              <span>Custom Orders</span>
            </Link>
          </div>
        </div>
      </section>


    </div>
  );
}
