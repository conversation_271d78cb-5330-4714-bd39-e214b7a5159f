import Link from 'next/link';
import {
  Car,
  Home,
  Sparkles,
  ShoppingCart,
  ArrowRight,
  CheckCircle,
  ArrowLeft,
  Star,
  MessageSquare
} from 'lucide-react';

export default function RoomCarFresheners() {
  return (
    <div className="min-h-screen bg-white">
      {/* Back Button */}
      <div className="bg-gray-50 py-4">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <Link
            href="/pages/in-house-products"
            className="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            Back to In-House Products
          </Link>
        </div>
      </div>

      {/* Hero Section with Logo */}
      <section className="bg-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Logo and Content Layout */}
          <div className="flex flex-col lg:flex-row items-start lg:items-center space-y-8 lg:space-y-0 lg:space-x-12">
            {/* Logo Section - Custom HTML/CSS Design */}
            <div className="flex-shrink-0">
              <div className="flex items-center space-x-6">
                {/* Geometric Logo Design */}
                <div className="relative w-32 h-32">
                  {/* Top Row Triangles */}
                  <div className="absolute top-0 left-0 w-8 h-8 bg-green-500 transform rotate-45 origin-center"></div>
                  <div className="absolute top-0 left-6 w-6 h-6 bg-yellow-400 transform rotate-45 origin-center"></div>
                  <div className="absolute top-0 right-0 w-8 h-8 bg-yellow-500 transform rotate-45 origin-center"></div>

                  {/* Middle Row */}
                  <div className="absolute top-6 left-0 w-10 h-10 bg-teal-500 transform rotate-45 origin-center"></div>
                  <div className="absolute top-6 left-8 w-8 h-8 bg-green-400 transform rotate-45 origin-center"></div>
                  <div className="absolute top-6 right-2 w-6 h-6 bg-orange-400 transform rotate-45 origin-center"></div>

                  {/* Bottom Row */}
                  <div className="absolute bottom-8 left-0 w-8 h-8 bg-blue-600 transform rotate-45 origin-center"></div>
                  <div className="absolute bottom-8 left-6 w-10 h-10 bg-purple-600 transform rotate-45 origin-center"></div>
                  <div className="absolute bottom-0 right-0 w-12 h-12 bg-red-500 transform rotate-45 origin-center"></div>

                  {/* Additional accent shapes */}
                  <div className="absolute bottom-4 left-4 w-6 h-6 bg-pink-500 transform rotate-45 origin-center"></div>
                  <div className="absolute top-8 right-6 w-4 h-4 bg-cyan-400 transform rotate-45 origin-center"></div>
                </div>

                {/* Text Logo */}
                <div className="text-left">
                  <div className="flex items-baseline space-x-2">
                    <span className="text-3xl font-bold text-gray-900 tracking-wide">CUSTOMINE</span>
                    <span className="text-2xl font-script text-teal-500 italic">Fresh</span>
                  </div>
                  <div className="text-sm text-gray-400 tracking-widest mt-1">A Breath of Better Living</div>
                </div>
              </div>
            </div>

            {/* Content Section */}
            <div className="flex-1">
              <p className="text-xl md:text-2xl text-gray-900 leading-relaxed font-medium">
                Bring home freshness that lingers. Whether it's your car, living space, or bathroom, we craft air fresheners that don't just mask <span className="underline decoration-2 decoration-gray-400">odors</span> – they uplift moods.
              </p>
            </div>
          </div>

          {/* Divider */}
          <div className="border-t border-gray-300 mt-12 mb-12"></div>
        </div>
      </section>

      {/* Product Categories */}
      <section className="py-16 bg-white">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">

            {/* Car Fresheners */}
            <div className="bg-gradient-to-br from-red-50 to-orange-50 rounded-2xl p-8 hover:shadow-lg transition-shadow">
              <div className="flex items-center mb-6">
                <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mr-4">
                  <Car className="w-8 h-8 text-red-600" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900">🚗 Car Fresheners</h3>
              </div>
              <h4 className="text-lg font-semibold text-gray-800 mb-3">Drive into freshness.</h4>
              <p className="text-gray-700 leading-relaxed mb-6">
                Experience every ride with confidence. Our car fresheners are crafted with essential oil blends that keep your vehicle smelling fresh, vibrant, and soothing for weeks.
              </p>

              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0" />
                  <span className="text-gray-700">Long-lasting aroma</span>
                </div>
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0" />
                  <span className="text-gray-700">No harmful chemicals</span>
                </div>
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0" />
                  <span className="text-gray-700">Sleek, compact designs</span>
                </div>
              </div>
            </div>

            {/* Room Fresheners */}
            <div className="bg-gradient-to-br from-green-50 to-teal-50 rounded-2xl p-8 hover:shadow-lg transition-shadow">
              <div className="flex items-center mb-6">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mr-4">
                  <Home className="w-8 h-8 text-green-600" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900">🏠 Room Fresheners</h3>
              </div>
              <h4 className="text-lg font-semibold text-gray-800 mb-3">Home is where freshness lives.</h4>
              <p className="text-gray-700 leading-relaxed mb-6">
                Make every corner of your room feel alive. Our room fresheners are thoughtfully designed to create a refreshing ambiance and elevate your space.
              </p>

              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0" />
                  <span className="text-gray-700">Natural fragrance oils</span>
                </div>
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0" />
                  <span className="text-gray-700">Stylish designs</span>
                </div>
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0" />
                  <span className="text-gray-700">Perfect for living rooms, bedrooms & workspaces</span>
                </div>
              </div>
            </div>

            {/* Toilet Fresheners */}
            <div className="bg-gradient-to-br from-blue-50 to-cyan-50 rounded-2xl p-8 hover:shadow-lg transition-shadow">
              <div className="flex items-center mb-6">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                  <Sparkles className="w-8 h-8 text-blue-600" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900">🚽 Toilet Fresheners</h3>
              </div>
              <h4 className="text-lg font-semibold text-gray-800 mb-3">Say goodbye to awkward odors.</h4>
              <p className="text-gray-700 leading-relaxed mb-6">
                Our toilet fresheners work round the clock to ensure your bathroom always smells crisp and clean.
              </p>

              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0" />
                  <span className="text-gray-700">Discreet & effective</span>
                </div>
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0" />
                  <span className="text-gray-700">Easy to use</span>
                </div>
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0" />
                  <span className="text-gray-700">Available in citrus, floral & aqua variants</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Product Showcase - Amazon Style */}
      <section className="py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Product Grid Container with Border */}
          <div className="border-2 border-blue-500 rounded-lg p-6 bg-white">
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">

            {/* Product 1 - Aromatherapy Gel Wick Hanging Car Perfume */}
            <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-lg transition-shadow">
              <div className="aspect-square bg-gray-100 rounded-lg mb-3 relative overflow-hidden">
                <img
                  src="/placeholder-product.png"
                  alt="Aromatherapy Gel Wick Hanging Car Perfume - Berry Lavender"
                  className="w-full h-full object-cover"
                />
                <div className="absolute top-2 left-2 bg-red-600 text-white text-xs px-2 py-1 rounded">
                  Amazon's Choice
                </div>
              </div>
              <div className="space-y-2">
                <p className="text-sm text-gray-600">Customine Fresh</p>
                <h3 className="text-sm font-medium text-gray-900 line-clamp-2">
                  Aromatherapy Gel Wick Hanging Car Perfume - Berry Lavender, Lasting Gel Based Car Air...
                </h3>
                <div className="flex items-center space-x-1">
                  <div className="flex text-yellow-400">
                    {[...Array(4)].map((_, i) => (
                      <Star key={i} className="w-3 h-3 fill-current" />
                    ))}
                    <Star className="w-3 h-3" />
                  </div>
                  <span className="text-xs text-gray-600">752</span>
                </div>
                <div className="space-y-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg font-bold text-gray-900">₹224</span>
                    <span className="text-xs text-gray-500 line-through">₹299</span>
                    <span className="text-xs text-green-600">(25% off)</span>
                  </div>
                  <p className="text-xs text-gray-600">Save extra with Customine offers</p>
                  <p className="text-xs text-blue-600">FREE delivery Mon, 23 Jul</p>
                  <p className="text-xs text-gray-600">Or fastest delivery Mon, 23 Jul</p>
                </div>
                <button className="w-full bg-yellow-400 hover:bg-yellow-500 text-black text-sm font-medium py-2 px-4 rounded-lg transition-colors">
                  Add to cart
                </button>
              </div>
            </div>

            {/* Product 2 - Godrej aer Twist, Car Air Freshener */}
            <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-lg transition-shadow">
              <div className="aspect-square bg-gray-100 rounded-lg mb-3 relative overflow-hidden">
                <img
                  src="/placeholder-product.png"
                  alt="Godrej aer Twist, Car Air Freshener"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="space-y-2">
                <p className="text-sm text-gray-600">Customine Fresh</p>
                <h3 className="text-sm font-medium text-gray-900 line-clamp-2">
                  Godrej aer Twist, Car Air Freshener - Cool Surf Blue (45ml)
                </h3>
                <div className="flex items-center space-x-1">
                  <div className="flex text-yellow-400">
                    {[...Array(4)].map((_, i) => (
                      <Star key={i} className="w-3 h-3 fill-current" />
                    ))}
                    <Star className="w-3 h-3" />
                  </div>
                  <span className="text-xs text-gray-600">1,876</span>
                </div>
                <div className="space-y-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg font-bold text-gray-900">₹368</span>
                    <span className="text-xs text-gray-500 line-through">₹450</span>
                    <span className="text-xs text-green-600">(18% off)</span>
                  </div>
                  <p className="text-xs text-gray-600">Save extra with Customine offers</p>
                  <p className="text-xs text-blue-600">FREE delivery Mon, 23 Jul</p>
                  <p className="text-xs text-gray-600">Or fastest delivery Mon, 23 Jul</p>
                </div>
                <button className="w-full bg-yellow-400 hover:bg-yellow-500 text-black text-sm font-medium py-2 px-4 rounded-lg transition-colors">
                  Add to cart
                </button>
              </div>
            </div>

            {/* Product 3 - Involve Your Senses One Musk */}
            <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-lg transition-shadow">
              <div className="aspect-square bg-gray-100 rounded-lg mb-3 relative overflow-hidden">
                <img
                  src="/placeholder-product.png"
                  alt="Involve Your Senses One Musk Potpourri Car Air Freshener"
                  className="w-full h-full object-cover"
                />
                <div className="absolute top-2 left-2 bg-orange-600 text-white text-xs px-2 py-1 rounded">
                  Best Seller
                </div>
              </div>
              <div className="space-y-2">
                <p className="text-sm text-gray-600">Customine Fresh</p>
                <h3 className="text-sm font-medium text-gray-900 line-clamp-2">
                  Involve Your Senses One Musk Potpourri Car Air Freshener for Freshening Your...
                </h3>
                <div className="flex items-center space-x-1">
                  <div className="flex text-yellow-400">
                    {[...Array(4)].map((_, i) => (
                      <Star key={i} className="w-3 h-3 fill-current" />
                    ))}
                    <Star className="w-3 h-3" />
                  </div>
                  <span className="text-xs text-gray-600">156</span>
                </div>
                <div className="space-y-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg font-bold text-gray-900">₹369</span>
                    <span className="text-xs text-gray-500 line-through">₹499</span>
                    <span className="text-xs text-green-600">(26% off)</span>
                  </div>
                  <p className="text-xs text-gray-600">Save extra with Customine offers</p>
                  <p className="text-xs text-blue-600">FREE delivery Mon, 23 Jul</p>
                  <p className="text-xs text-gray-600">Or fastest delivery Mon, 23 Jul</p>
                </div>
                <button className="w-full bg-yellow-400 hover:bg-yellow-500 text-black text-sm font-medium py-2 px-4 rounded-lg transition-colors">
                  Add to cart
                </button>
              </div>
            </div>

            {/* Product 4 - ARGANICE Aroma Swing */}
            <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-lg transition-shadow">
              <div className="aspect-square bg-gray-100 rounded-lg mb-3 relative overflow-hidden">
                <img
                  src="/placeholder-product.png"
                  alt="ARGANICE Aroma Swing - Pack of 3 Pocket Car Air Freshener"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="space-y-2">
                <p className="text-sm text-gray-600">Customine Fresh</p>
                <h3 className="text-sm font-medium text-gray-900 line-clamp-2">
                  ARGANICE Aroma Swing - Pack of 3 Pocket Car Air Freshener, Aromatic Essential Oils in...
                </h3>
                <div className="flex items-center space-x-1">
                  <div className="flex text-yellow-400">
                    {[...Array(4)].map((_, i) => (
                      <Star key={i} className="w-3 h-3 fill-current" />
                    ))}
                    <Star className="w-3 h-3" />
                  </div>
                  <span className="text-xs text-gray-600">203</span>
                </div>
                <div className="space-y-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg font-bold text-gray-900">₹239</span>
                    <span className="text-xs text-gray-500 line-through">₹299</span>
                    <span className="text-xs text-green-600">(20% off)</span>
                  </div>
                  <p className="text-xs text-gray-600">Save extra with Customine offers</p>
                  <p className="text-xs text-blue-600">FREE delivery Mon, 23 Jul</p>
                  <p className="text-xs text-gray-600">Or fastest delivery Mon, 23 Jul</p>
                </div>
                <button className="w-full bg-yellow-400 hover:bg-yellow-500 text-black text-sm font-medium py-2 px-4 rounded-lg transition-colors">
                  Add to cart
                </button>
              </div>
            </div>

            {/* Product 5 - Aromatherapy Gel Wick Hanging Car Perfume (Duplicate) */}
            <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-lg transition-shadow">
              <div className="aspect-square bg-gray-100 rounded-lg mb-3 relative overflow-hidden">
                <img
                  src="/placeholder-product.png"
                  alt="Aromatherapy Gel Wick Hanging Car Perfume - Berry Lavender"
                  className="w-full h-full object-cover"
                />
                <div className="absolute top-2 left-2 bg-red-600 text-white text-xs px-2 py-1 rounded">
                  Limited Deal
                </div>
              </div>
              <div className="space-y-2">
                <p className="text-sm text-gray-600">Customine Fresh</p>
                <h3 className="text-sm font-medium text-gray-900 line-clamp-2">
                  Aromatherapy Gel Wick Hanging Car Perfume - Berry Lavender, Lasting Gel Based Car Air...
                </h3>
                <div className="flex items-center space-x-1">
                  <div className="flex text-yellow-400">
                    {[...Array(4)].map((_, i) => (
                      <Star key={i} className="w-3 h-3 fill-current" />
                    ))}
                    <Star className="w-3 h-3" />
                  </div>
                  <span className="text-xs text-gray-600">752</span>
                </div>
                <div className="space-y-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg font-bold text-gray-900">₹224</span>
                    <span className="text-xs text-gray-500 line-through">₹299</span>
                    <span className="text-xs text-green-600">(25% off)</span>
                  </div>
                  <p className="text-xs text-gray-600">Save extra with Customine offers</p>
                  <p className="text-xs text-blue-600">FREE delivery Mon, 23 Jul</p>
                  <p className="text-xs text-gray-600">Or fastest delivery Mon, 23 Jul</p>
                </div>
                <button className="w-full bg-yellow-400 hover:bg-yellow-500 text-black text-sm font-medium py-2 px-4 rounded-lg transition-colors">
                  Add to cart
                </button>
              </div>
            </div>

            {/* Product 6 - Godrej aer Twist (Row 2) */}
            <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-lg transition-shadow">
              <div className="aspect-square bg-gray-100 rounded-lg mb-3 relative overflow-hidden">
                <img
                  src="/placeholder-product.png"
                  alt="Godrej aer Twist, Car Air Freshener"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="space-y-2">
                <p className="text-sm text-gray-600">Customine Fresh</p>
                <h3 className="text-sm font-medium text-gray-900 line-clamp-2">
                  Godrej aer Twist, Car Air Freshener - Cool Surf Blue (45ml)
                </h3>
                <div className="flex items-center space-x-1">
                  <div className="flex text-yellow-400">
                    {[...Array(4)].map((_, i) => (
                      <Star key={i} className="w-3 h-3 fill-current" />
                    ))}
                    <Star className="w-3 h-3" />
                  </div>
                  <span className="text-xs text-gray-600">1,876</span>
                </div>
                <div className="space-y-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg font-bold text-gray-900">₹368</span>
                    <span className="text-xs text-gray-500 line-through">₹450</span>
                    <span className="text-xs text-green-600">(18% off)</span>
                  </div>
                  <p className="text-xs text-gray-600">Save extra with Customine offers</p>
                  <p className="text-xs text-blue-600">FREE delivery Mon, 23 Jul</p>
                  <p className="text-xs text-gray-600">Or fastest delivery Mon, 23 Jul</p>
                </div>
                <button className="w-full bg-yellow-400 hover:bg-yellow-500 text-black text-sm font-medium py-2 px-4 rounded-lg transition-colors">
                  Add to cart
                </button>
              </div>
            </div>

            {/* Product 7 - Involve Your Senses One Musk (Row 2) */}
            <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-lg transition-shadow">
              <div className="aspect-square bg-gray-100 rounded-lg mb-3 relative overflow-hidden">
                <img
                  src="/placeholder-product.png"
                  alt="Involve Your Senses One Musk Potpourri Car Air Freshener"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="space-y-2">
                <p className="text-sm text-gray-600">Customine Fresh</p>
                <h3 className="text-sm font-medium text-gray-900 line-clamp-2">
                  Involve Your Senses One Musk Potpourri Car Air Freshener for Freshening Your...
                </h3>
                <div className="flex items-center space-x-1">
                  <div className="flex text-yellow-400">
                    {[...Array(4)].map((_, i) => (
                      <Star key={i} className="w-3 h-3 fill-current" />
                    ))}
                    <Star className="w-3 h-3" />
                  </div>
                  <span className="text-xs text-gray-600">156</span>
                </div>
                <div className="space-y-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg font-bold text-gray-900">₹369</span>
                    <span className="text-xs text-gray-500 line-through">₹499</span>
                    <span className="text-xs text-green-600">(26% off)</span>
                  </div>
                  <p className="text-xs text-gray-600">Save extra with Customine offers</p>
                  <p className="text-xs text-blue-600">FREE delivery Mon, 23 Jul</p>
                  <p className="text-xs text-gray-600">Or fastest delivery Mon, 23 Jul</p>
                </div>
                <button className="w-full bg-yellow-400 hover:bg-yellow-500 text-black text-sm font-medium py-2 px-4 rounded-lg transition-colors">
                  Add to cart
                </button>
              </div>
            </div>

            {/* Product 8 - ARGANICE Aroma Swing (Row 2) */}
            <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-lg transition-shadow">
              <div className="aspect-square bg-gray-100 rounded-lg mb-3 relative overflow-hidden">
                <img
                  src="/placeholder-product.png"
                  alt="ARGANICE Aroma Swing - Pack of 3 Pocket Car Air Freshener"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="space-y-2">
                <p className="text-sm text-gray-600">Customine Fresh</p>
                <h3 className="text-sm font-medium text-gray-900 line-clamp-2">
                  ARGANICE Aroma Swing - Pack of 3 Pocket Car Air Freshener, Aromatic Essential Oils in...
                </h3>
                <div className="flex items-center space-x-1">
                  <div className="flex text-yellow-400">
                    {[...Array(4)].map((_, i) => (
                      <Star key={i} className="w-3 h-3 fill-current" />
                    ))}
                    <Star className="w-3 h-3" />
                  </div>
                  <span className="text-xs text-gray-600">203</span>
                </div>
                <div className="space-y-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg font-bold text-gray-900">₹239</span>
                    <span className="text-xs text-gray-500 line-through">₹299</span>
                    <span className="text-xs text-green-600">(20% off)</span>
                  </div>
                  <p className="text-xs text-gray-600">Save extra with Customine offers</p>
                  <p className="text-xs text-blue-600">FREE delivery Mon, 23 Jul</p>
                  <p className="text-xs text-gray-600">Or fastest delivery Mon, 23 Jul</p>
                </div>
                <button className="w-full bg-yellow-400 hover:bg-yellow-500 text-black text-sm font-medium py-2 px-4 rounded-lg transition-colors">
                  Add to cart
                </button>
              </div>
            </div>

            {/* Product 9 - Airpaper Aroma Swing */}
            <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-lg transition-shadow">
              <div className="aspect-square bg-gray-100 rounded-lg mb-3 relative overflow-hidden">
                <img
                  src="/placeholder-product.png"
                  alt="Airpaper Aroma Swing - Pack of 3 Pocket Car Air Freshener"
                  className="w-full h-full object-cover"
                />
                <div className="absolute top-2 left-2 bg-green-600 text-white text-xs px-2 py-1 rounded">
                  New Launch
                </div>
              </div>
              <div className="space-y-2">
                <p className="text-sm text-gray-600">Customine Fresh</p>
                <h3 className="text-sm font-medium text-gray-900 line-clamp-2">
                  Airpaper Aroma Swing - Pack of 3 Pocket Car Air Freshener, Lavender & Jasmine Quilt Car...
                </h3>
                <div className="flex items-center space-x-1">
                  <div className="flex text-yellow-400">
                    {[...Array(4)].map((_, i) => (
                      <Star key={i} className="w-3 h-3 fill-current" />
                    ))}
                    <Star className="w-3 h-3" />
                  </div>
                  <span className="text-xs text-gray-600">89</span>
                </div>
                <div className="space-y-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg font-bold text-gray-900">₹259</span>
                    <span className="text-xs text-gray-500 line-through">₹329</span>
                    <span className="text-xs text-green-600">(21% off)</span>
                  </div>
                  <p className="text-xs text-gray-600">Save extra with Customine offers</p>
                  <p className="text-xs text-blue-600">FREE delivery Mon, 23 Jul</p>
                  <p className="text-xs text-gray-600">Or fastest delivery Mon, 23 Jul</p>
                </div>
                <button className="w-full bg-yellow-400 hover:bg-yellow-500 text-black text-sm font-medium py-2 px-4 rounded-lg transition-colors">
                  Add to cart
                </button>
              </div>
            </div>

            {/* Product 10 - Premium Car Air Freshener Collection */}
            <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-lg transition-shadow">
              <div className="aspect-square bg-gray-100 rounded-lg mb-3 relative overflow-hidden">
                <img
                  src="/placeholder-product.png"
                  alt="Premium Car Air Freshener Collection"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="space-y-2">
                <p className="text-sm text-gray-600">Customine Fresh</p>
                <h3 className="text-sm font-medium text-gray-900 line-clamp-2">
                  Premium Car Air Freshener Collection - Multi-Pack Variety Set with Essential Oils...
                </h3>
                <div className="flex items-center space-x-1">
                  <div className="flex text-yellow-400">
                    {[...Array(4)].map((_, i) => (
                      <Star key={i} className="w-3 h-3 fill-current" />
                    ))}
                    <Star className="w-3 h-3" />
                  </div>
                  <span className="text-xs text-gray-600">324</span>
                </div>
                <div className="space-y-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg font-bold text-gray-900">₹399</span>
                    <span className="text-xs text-gray-500 line-through">₹499</span>
                    <span className="text-xs text-green-600">(20% off)</span>
                  </div>
                  <p className="text-xs text-gray-600">Save extra with Customine offers</p>
                  <p className="text-xs text-blue-600">FREE delivery Mon, 23 Jul</p>
                  <p className="text-xs text-gray-600">Or fastest delivery Mon, 23 Jul</p>
                </div>
                <button className="w-full bg-yellow-400 hover:bg-yellow-500 text-black text-sm font-medium py-2 px-4 rounded-lg transition-colors">
                  Add to cart
                </button>
              </div>
            </div>

          </div>

          {/* Shop Now Button */}
          <div className="mt-8 text-center">
            <button className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-8 rounded-lg transition-colors">
              Shop Now
            </button>
          </div>
        </div>
        </div>
      </section>

      {/* Shop Now Section */}
      <section className="py-16 bg-gradient-to-r from-slate-800 to-gray-900">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="flex items-center justify-center mb-6">
            <ShoppingCart className="w-12 h-12 text-white mr-4" />
            <h3 className="text-3xl font-bold text-white">🛒 Shop Now</h3>
          </div>
          <p className="text-xl text-gray-200 mb-8">
            Browse our collections. Pick your vibe.
          </p>
          <p className="text-lg text-gray-300 mb-8">
            Custom-pack options available for gifting & bulk orders.
          </p>

          <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6">
            <Link
              href="/collections/all"
              className="bg-white text-slate-800 px-8 py-4 rounded-xl font-semibold hover:bg-gray-100 transition-all duration-300 flex items-center space-x-2 shadow-lg hover:shadow-xl"
            >
              <ShoppingCart className="w-5 h-5" />
              <span>Browse Collections</span>
            </Link>
            <Link
              href="/pages/special-occasion-inquiry"
              className="bg-amber-600 text-white px-8 py-4 rounded-xl font-semibold hover:bg-amber-700 transition-all duration-300 flex items-center space-x-2 shadow-lg hover:shadow-xl"
            >
              <MessageSquare className="w-5 h-5" />
              <span>Custom Orders</span>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
