import { Briefcase, Users, Gift, Star, ArrowRight, Phone, Mail } from 'lucide-react';

export default function CorporateGifting() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-amber-50 to-orange-100 py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="flex items-center justify-center mb-6">
            <Briefcase className="w-12 h-12 text-amber-600 mr-4" />
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900">
              Corporate Gifting Solutions
            </h1>
          </div>
          <h2 className="text-2xl md:text-3xl font-semibold text-amber-700 mb-6">
            Build Lasting Impressions with Every Gift
          </h2>
          <p className="text-xl text-gray-700 leading-relaxed max-w-3xl mx-auto">
            Elevate your brand with curated corporate gift boxes that reflect professionalism, gratitude, and thoughtful branding. Ideal for clients, employees, and partners.
          </p>
        </div>
      </section>

      {/* Box Ideas Section */}
      <section className="py-16 bg-white">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">Corporate Gift Box Ideas</h3>
            <p className="text-lg text-gray-600">Professional, thoughtful, and perfectly branded for your business needs</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="bg-gray-50 rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Briefcase className="w-8 h-8 text-blue-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">Custom-Branded Stationery</h4>
              <p className="text-gray-600 text-sm">Tech accessories, notebooks, and professional essentials with your company branding</p>
            </div>

            <div className="bg-gray-50 rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Gift className="w-8 h-8 text-green-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">Gourmet Treats</h4>
              <p className="text-gray-600 text-sm">Premium snacks, artisanal teas, specialty coffees, and gourmet delicacies</p>
            </div>

            <div className="bg-gray-50 rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="w-8 h-8 text-purple-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">Personalized Planners</h4>
              <p className="text-gray-600 text-sm">Custom notebooks, planners, and organizational tools for productivity</p>
            </div>

            <div className="bg-gray-50 rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Star className="w-8 h-8 text-emerald-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">Eco-Friendly Essentials</h4>
              <p className="text-gray-600 text-sm">Sustainable lifestyle products that reflect your company&apos;s values</p>
            </div>
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">Our Gifting Process</h3>
            <p className="text-lg text-gray-600">From concept to delivery, we handle everything</p>
          </div>

          <div className="space-y-12">
            {[
              {
                step: "01",
                title: "Initial Consultation",
                description: "Share your vision, budget, and timeline. We&apos;ll discuss your brand values and recipient preferences."
              },
              {
                step: "02", 
                title: "Custom Design",
                description: "Our team creates a personalized gift concept that reflects your brand and delights recipients."
              },
              {
                step: "03",
                title: "Approval & Production",
                description: "Review and approve your design. We handle sourcing, assembly, and quality control."
              },
              {
                step: "04",
                title: "Delivery & Coordination",
                description: "We manage all logistics, shipping, and delivery coordination to ensure perfect timing."
              }
            ].map((item, index) => (
              <div key={index} className="flex items-start space-x-6">
                <div className="flex-shrink-0">
                  <div className="w-16 h-16 bg-amber-600 text-white rounded-full flex items-center justify-center font-bold text-xl">
                    {item.step}
                  </div>
                </div>
                <div className="flex-1">
                  <h4 className="text-xl font-semibold text-gray-900 mb-2">{item.title}</h4>
                  <p className="text-gray-600 leading-relaxed">{item.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h3 className="text-3xl font-bold text-gray-900 mb-4">Start Your Corporate Gifting Journey</h3>
          <p className="text-lg text-gray-600 mb-8">Tell us about your project and we&apos;ll create something amazing together</p>

          <div className="bg-gradient-to-r from-amber-500 to-orange-500 rounded-2xl p-8 text-white">
            <h4 className="text-2xl font-bold mb-4">Ready to Get Started?</h4>
            <p className="text-lg mb-6 opacity-90">
              Let&apos;s discuss your corporate gifting needs and create a customized solution that reflects your brand values.
            </p>
            <a
              href="/pages/special-occasion-inquiry"
              className="inline-flex items-center bg-white text-amber-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-200"
            >
              Submit Your Inquiry
              <ArrowRight className="w-5 h-5 ml-2" />
            </a>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 bg-gray-900 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h3 className="text-2xl font-bold mb-6">Ready to Get Started?</h3>
          <p className="text-lg text-gray-300 mb-8">
            Let&apos;s create corporate gifts that make a lasting impression
          </p>
          <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-8">
            <a
              href="mailto:<EMAIL>"
              className="flex items-center text-amber-400 hover:text-amber-300 transition-colors"
            >
              <Mail className="w-5 h-5 mr-2" />
              <EMAIL>
            </a>
            <a
              href="tel:+919876543210"
              className="flex items-center text-amber-400 hover:text-amber-300 transition-colors"
            >
              <Phone className="w-5 h-5 mr-2" />
              +91 98765 43210
            </a>
          </div>
        </div>
      </section>
    </div>
  );
}
