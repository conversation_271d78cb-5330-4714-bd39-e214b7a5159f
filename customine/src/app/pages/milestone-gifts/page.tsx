import { Trophy, Star, Gift, Calendar, ArrowRight, Phone, Mail } from 'lucide-react';

export default function MilestoneGifts() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-yellow-50 to-orange-100 py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="flex items-center justify-center mb-6">
            <Trophy className="w-12 h-12 text-yellow-600 mr-4" />
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900">
              Milestone Celebration Gifts
            </h1>
          </div>
          <h2 className="text-2xl md:text-3xl font-semibold text-yellow-700 mb-6">
            Every Milestone Deserves a Moment
          </h2>
          <p className="text-xl text-gray-700 leading-relaxed max-w-3xl mx-auto">
            Honor life&apos;s most special moments — from retirements to birthdays to mitzvahs — with curated boxes that make lasting memories.
          </p>
        </div>
      </section>

      {/* Box Ideas Section */}
      <section className="py-16 bg-white">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">Milestone Gift Ideas</h3>
            <p className="text-lg text-gray-600">Meaningful gifts for life&apos;s special achievements</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="bg-gray-50 rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Calendar className="w-8 h-8 text-yellow-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">Memory Keepsakes</h4>
              <p className="text-gray-600 text-sm">Custom photo frames and celebratory treats that capture the moment</p>
            </div>

            <div className="bg-gray-50 rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Star className="w-8 h-8 text-orange-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">Personalized Mementos</h4>
              <p className="text-gray-600 text-sm">Custom keepsakes and décor pieces that commemorate achievements</p>
            </div>

            <div className="bg-gray-50 rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-amber-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Gift className="w-8 h-8 text-amber-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">Luxury Essentials</h4>
              <p className="text-gray-600 text-sm">Scented candles, journals, drinkware, and premium lifestyle items</p>
            </div>

            <div className="bg-gray-50 rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Trophy className="w-8 h-8 text-red-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">Themed Collections</h4>
              <p className="text-gray-600 text-sm">Age or occasion-themed gift sets tailored to the celebration</p>
            </div>
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">Our Milestone Gifting Process</h3>
            <p className="text-lg text-gray-600">Creating memorable celebrations for life&apos;s special moments</p>
          </div>

          <div className="space-y-12">
            {[
              {
                step: "01",
                title: "Milestone Understanding",
                description: "Learn about the achievement, the person being honored, and the significance of this milestone."
              },
              {
                step: "02", 
                title: "Celebration Design",
                description: "Create gifts that reflect the magnitude of the achievement and the personality of the honoree."
              },
              {
                step: "03",
                title: "Personal Touch Addition",
                description: "Add custom elements, meaningful messages, and personal touches that make the gift truly special."
              },
              {
                step: "04",
                title: "Memorable Delivery",
                description: "Coordinate perfect timing and presentation to enhance the celebration moment."
              }
            ].map((item, index) => (
              <div key={index} className="flex items-start space-x-6">
                <div className="flex-shrink-0">
                  <div className="w-16 h-16 bg-yellow-600 text-white rounded-full flex items-center justify-center font-bold text-xl">
                    {item.step}
                  </div>
                </div>
                <div className="flex-1">
                  <h4 className="text-xl font-semibold text-gray-900 mb-2">{item.title}</h4>
                  <p className="text-gray-600 leading-relaxed">{item.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h3 className="text-3xl font-bold text-gray-900 mb-4">Plan Your Milestone Celebration</h3>
          <p className="text-lg text-gray-600 mb-8">Let&apos;s create meaningful gifts that honor this special achievement</p>

          <div className="bg-gradient-to-r from-yellow-500 to-orange-500 rounded-2xl p-8 text-white">
            <h4 className="text-2xl font-bold mb-4">Ready to Celebrate This Milestone?</h4>
            <p className="text-lg mb-6 opacity-90">
              Tell us about the achievement and we&apos;ll create gifts that make this milestone truly memorable.
            </p>
            <a
              href="/pages/special-occasion-inquiry"
              className="inline-flex items-center bg-white text-yellow-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-200"
            >
              Submit Your Milestone Inquiry
              <ArrowRight className="w-5 h-5 ml-2" />
            </a>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 bg-gray-900 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h3 className="text-2xl font-bold mb-6">Ready to Celebrate This Milestone?</h3>
          <p className="text-lg text-gray-300 mb-8">
            Let&apos;s create meaningful gifts that honor this special achievement
          </p>
          <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-8">
            <a
              href="mailto:<EMAIL>"
              className="flex items-center text-yellow-400 hover:text-yellow-300 transition-colors"
            >
              <Mail className="w-5 h-5 mr-2" />
              <EMAIL>
            </a>
            <a
              href="tel:+919876543210"
              className="flex items-center text-yellow-400 hover:text-yellow-300 transition-colors"
            >
              <Phone className="w-5 h-5 mr-2" />
              +91 98765 43210
            </a>
          </div>
        </div>
      </section>
    </div>
  );
}
