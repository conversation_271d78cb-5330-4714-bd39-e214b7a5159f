import { Users, Laptop, Gift, Zap, ArrowRight, Phone, Mail } from 'lucide-react';

export default function EventConferenceGifting() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 to-indigo-100 py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="flex items-center justify-center mb-6">
            <Users className="w-12 h-12 text-blue-600 mr-4" />
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900">
              Event & Conference Gifting
            </h1>
          </div>
          <h2 className="text-2xl md:text-3xl font-semibold text-blue-700 mb-6">
            Memorable Gifting for Meaningful Events
          </h2>
          <p className="text-xl text-gray-700 leading-relaxed max-w-3xl mx-auto">
            From industry conferences to intimate seminars, our curated gifts keep your brand top-of-mind long after the event ends.
          </p>
        </div>
      </section>

      {/* Box Ideas Section */}
      <section className="py-16 bg-white">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">Event Gift Box Ideas</h3>
            <p className="text-lg text-gray-600">Professional gifts that enhance the event experience</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="bg-gray-50 rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Laptop className="w-8 h-8 text-blue-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">Branded Tech Essentials</h4>
              <p className="text-gray-600 text-sm">Notebooks, pens, tech gadgets, and accessories with your event branding</p>
            </div>

            <div className="bg-gray-50 rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Zap className="w-8 h-8 text-green-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">Refresh & Energy Kits</h4>
              <p className="text-gray-600 text-sm">Snacks, hydration essentials, and eye masks for long conference days</p>
            </div>

            <div className="bg-gray-50 rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Gift className="w-8 h-8 text-purple-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">Welcome & Farewell Boxes</h4>
              <p className="text-gray-600 text-sm">Thoughtful gifts for event arrival and memorable takeaways</p>
            </div>

            <div className="bg-gray-50 rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="w-8 h-8 text-indigo-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">Digital Detox Items</h4>
              <p className="text-gray-600 text-sm">Mindfulness items, stress relief tools, and wellness essentials</p>
            </div>
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">Our Event Gifting Process</h3>
            <p className="text-lg text-gray-600">Seamless coordination for successful events</p>
          </div>

          <div className="space-y-12">
            {[
              {
                step: "01",
                title: "Event Planning Session",
                description: "Discuss your event goals, attendee profile, timeline, and branding requirements."
              },
              {
                step: "02", 
                title: "Strategic Gift Design",
                description: "Create gifts that align with your event theme and enhance the attendee experience."
              },
              {
                step: "03",
                title: "Production & Quality Control",
                description: "Manage all sourcing, customization, and quality assurance for your event gifts."
              },
              {
                step: "04",
                title: "Event Coordination",
                description: "Handle venue delivery, setup coordination, and distribution logistics."
              }
            ].map((item, index) => (
              <div key={index} className="flex items-start space-x-6">
                <div className="flex-shrink-0">
                  <div className="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold text-xl">
                    {item.step}
                  </div>
                </div>
                <div className="flex-1">
                  <h4 className="text-xl font-semibold text-gray-900 mb-2">{item.title}</h4>
                  <p className="text-gray-600 leading-relaxed">{item.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h3 className="text-3xl font-bold text-gray-900 mb-4">Plan Your Event Gifting</h3>
          <p className="text-lg text-gray-600 mb-8">Let&apos;s create memorable gifts that enhance your event experience</p>

          <div className="bg-gradient-to-r from-blue-500 to-indigo-500 rounded-2xl p-8 text-white">
            <h4 className="text-2xl font-bold mb-4">Ready to Elevate Your Event?</h4>
            <p className="text-lg mb-6 opacity-90">
              Tell us about your event goals and we&apos;ll design gifts that keep your brand top-of-mind long after the event ends.
            </p>
            <a
              href="/pages/special-occasion-inquiry"
              className="inline-flex items-center bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-200"
            >
              Submit Your Event Inquiry
              <ArrowRight className="w-5 h-5 ml-2" />
            </a>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 bg-gray-900 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h3 className="text-2xl font-bold mb-6">Ready to Elevate Your Event?</h3>
          <p className="text-lg text-gray-300 mb-8">
            Let&apos;s create gifts that make your event unforgettable
          </p>
          <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-8">
            <a
              href="mailto:<EMAIL>"
              className="flex items-center text-blue-400 hover:text-blue-300 transition-colors"
            >
              <Mail className="w-5 h-5 mr-2" />
              <EMAIL>
            </a>
            <a
              href="tel:+919876543210"
              className="flex items-center text-blue-400 hover:text-blue-300 transition-colors"
            >
              <Phone className="w-5 h-5 mr-2" />
              +91 98765 43210
            </a>
          </div>
        </div>
      </section>
    </div>
  );
}
