'use client';

import React from 'react';
import Link from 'next/link';
import {
  ArrowR<PERSON>,
  CheckCircle,
  Phone
} from 'lucide-react';

export default function AddYourOwnLogoPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-amber-50 via-white to-orange-50">
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <div className="text-center">
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-amber-100 text-amber-800 text-sm font-medium mb-8">
              Our Ready-to-Ship Gifts + Your Branding
            </div>

            <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
              Make It Yours with Our{' '}
              <span className="bg-gradient-to-r from-amber-600 to-orange-600 bg-clip-text text-transparent">
                Ready-to-Ship Gift Boxes
              </span>
            </h1>

            <p className="text-xl text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed">
              Choose from our beautifully curated Ready-to-Ship Collection, and we&apos;ll replace our Customine branding with yours—seamless and stunning!
            </p>
          </div>
        </div>
      </section>

      {/* Key Benefits */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 gap-16 items-center">
            <div className="space-y-12">
              <div className="flex items-start space-x-6">
                <div className="flex-shrink-0">
                  <div className="w-16 h-16 bg-emerald-100 rounded-xl flex items-center justify-center">
                    <CheckCircle className="w-8 h-8 text-emerald-600" />
                  </div>
                </div>
                <div>
                  <h3 className="text-3xl font-bold text-gray-900 mb-4">
                    Low Minimums, Maximum Impact
                  </h3>
                  <p className="text-lg text-gray-600 leading-relaxed">
                    Start with just 5 gift boxes—one of the lowest branded gift minimums in the industry!
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-6">
                <div className="flex-shrink-0">
                  <div className="w-16 h-16 bg-amber-100 rounded-xl flex items-center justify-center">
                    <CheckCircle className="w-8 h-8 text-amber-600" />
                  </div>
                </div>
                <div>
                  <h3 className="text-3xl font-bold text-gray-900 mb-4">
                    Customize the Details
                  </h3>
                  <p className="text-lg text-gray-600 leading-relaxed">
                    Add your branding to key elements like gift tags, bellybands, and notecards for a fully personalized unboxing experience.
                  </p>
                </div>
              </div>
            </div>


            {/* Visual Showcase */}
            <div className="relative">
              <div className="bg-gradient-to-br from-amber-50 to-orange-50 rounded-3xl p-8">
                {/* eslint-disable-next-line @next/next/no-img-element */}
                 <img
                  src="/logo_notecard.png"
                  alt="Gift box branding showcase with logo notecard"
                  width={600}
                  height={400}
                  className="w-full h-auto rounded-2xl shadow-lg"
                />

                {/* Branding callouts */}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-amber-600 to-orange-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold text-white mb-6">
            Ready to Start Your Branded Gift Project?
          </h2>
          <p className="text-xl text-amber-100 mb-10 max-w-2xl mx-auto">
            Let&apos;s create something beautiful together. Our team is ready to help you bring your vision to life.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/contact"
              className="inline-flex items-center px-8 py-4 bg-white text-amber-600 font-medium rounded-lg hover:bg-amber-50 transition-colors duration-200"
            >
              Start Your Project
              <ArrowRight className="ml-2 w-5 h-5" />
            </Link>
            <Link
              href="/contact"
              className="inline-flex items-center px-8 py-4 bg-transparent border-2 border-white text-white font-medium rounded-lg hover:bg-white hover:text-amber-600 transition-colors duration-200"
            >
              <Phone className="mr-2 w-5 h-5" />
              Schedule a Call
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
