'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { ArrowLeft, Phone, Mail, MapPin, Clock, Send } from 'lucide-react';
import { inquiryAPI } from '../../../utils/inquiryAPI';
import toast from 'react-hot-toast';
import { contactFormSubmit } from '../../../lib/gtag';

export default function ContactUs() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: '',
    inquiryType: 'general'
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitStatus('idle');

    try {
      // Map form inquiry type to API inquiry type
      const inquiryTypeMap: Record<string, string> = {
        'general': 'GENERAL_INQUIRY',
        'corporate': 'CORPORATE_INQUIRY',
        'wedding': 'SPECIAL_OCCASION',
        'custom': 'CUSTOM_GIFT_BOX',
        'bulk': 'BULK_ORDER',
        'support': 'GENERAL_INQUIRY'
      };

      // Prepare inquiry data
      const inquiryData = {
        type: inquiryTypeMap[formData.inquiryType] || 'GENERAL_INQUIRY',
        name: formData.name,
        email: formData.email,
        phone: formData.phone || undefined,
        subject: formData.subject,
        message: formData.message,
        category: formData.inquiryType,
        priority: 'medium' as const,
        source: 'website',
        eventType: formData.inquiryType === 'wedding' ? 'wedding' as const : undefined
      };

      console.log('Submitting contact form:', inquiryData);

      // Submit inquiry using the API
      const result = await inquiryAPI.create(inquiryData);

      if (result.success) {
        setSubmitStatus('success');

        // Track contact form submission
        contactFormSubmit(formData.inquiryType);

        // Show success toast
        toast.success('Thank you! Your message has been sent successfully. We\'ll get back to you soon.', {
          duration: 5000,
          icon: '✅',
        });

        // Reset form
        setFormData({
          name: '',
          email: '',
          phone: '',
          subject: '',
          message: '',
          inquiryType: 'general'
        });
      } else {
        throw new Error(result.error || 'Failed to submit inquiry');
      }
    } catch (error) {
      console.error('Error submitting contact form:', error);
      setSubmitStatus('error');

      // Show error toast
      toast.error('Sorry, there was an error sending your message. Please try again or contact us directly.', {
        duration: 5000,
        icon: '❌',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-white">
      <div className="container mx-auto px-4 py-16">
        {/* Back to Home Link */}
        <div className="mb-8">
          <Link
            href="/"
            className="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors duration-200 group"
          >
            <ArrowLeft className="w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform duration-200" />
            Back to Home
          </Link>
        </div>

        {/* Page Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-8">
            CONTACT US
          </h1>
          <div className="max-w-4xl mx-auto space-y-4">
            <h2 className="text-2xl md:text-3xl font-semibold text-gray-800">
              Let&apos;s Create Something Thoughtful Together
            </h2>
            <p className="text-xl text-gray-600">
              Have an idea, occasion, or question? We&apos;d love to hear from you.
            </p>
          </div>
        </div>

        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
            {/* Contact Information */}
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-8">Get in Touch</h2>
              
              <div className="space-y-6">
                {/* Phone */}
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <Phone className="w-6 h-6 text-[var(--color-teal)] mt-1" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-1">Phone/WhatsApp</h3>
                    <p className="text-gray-600">+91- 90426 71801</p>
                    <p className="text-sm text-gray-500">Available for calls and WhatsApp</p>
                  </div>
                </div>

                {/* Email */}
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <Mail className="w-6 h-6 text-[var(--color-teal)] mt-1" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-1">Email</h3>
                    <p className="text-gray-600"><EMAIL></p>
                    <p className="text-sm text-gray-500">We&apos;ll respond within 24 hours</p>
                  </div>
                </div>

                {/* Location */}
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <MapPin className="w-6 h-6 text-[var(--color-teal)] mt-1" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-1">Location</h3>
                    <p className="text-gray-600">
                      Based in Madambakkam, Chennai -600126<br />
                      <span className="text-sm">Serving All of India</span>
                    </p>
                  </div>
                </div>

                {/* Business Hours */}
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <Clock className="w-6 h-6 text-[var(--color-teal)] mt-1" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-1">Business Hours</h3>
                    <div className="text-gray-600 space-y-1">
                      <p>Monday - Friday: 9:00 AM - 6:00 PM</p>
                      <p>Saturday: 10:00 AM - 4:00 PM</p>
                      <p>Sunday: Closed</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Quick Links */}
              <div className="mt-12">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Quick Links</h3>
                <div className="space-y-2">
                  <Link href="/pages/corporate-gifting" className="block text-[var(--color-teal)] hover:text-[var(--color-navy)] transition-colors">
                    Corporate Gifting Solutions
                  </Link>
                  <Link href="/pages/wedding-welcome-gifts" className="block text-[var(--color-teal)] hover:text-[var(--color-navy)] transition-colors">
                    Wedding Welcome Gifts
                  </Link>
                  <Link href="/pages/full-custom" className="block text-[var(--color-teal)] hover:text-[var(--color-navy)] transition-colors">
                    Full Custom Gifts
                  </Link>
                  <Link href="/collections/all" className="block text-[var(--color-teal)] hover:text-[var(--color-navy)] transition-colors">
                    Browse All Gifts
                  </Link>
                </div>
              </div>
            </div>

            {/* Contact Form */}
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-8">Send us a Message</h2>
              
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Inquiry Type */}
                <div>
                  <label htmlFor="inquiryType" className="block text-sm font-medium text-gray-700 mb-2">
                    Inquiry Type
                  </label>
                  <select
                    id="inquiryType"
                    name="inquiryType"
                    value={formData.inquiryType}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-[var(--color-teal)] focus:border-transparent text-gray-900"
                    required
                  >
                    <option value="general">General Inquiry</option>
                    <option value="corporate">Corporate Gifting</option>
                    <option value="wedding">Wedding Gifts</option>
                    <option value="custom">Custom Orders</option>
                    <option value="bulk">Bulk Orders</option>
                    <option value="support">Customer Support</option>
                  </select>
                </div>

                {/* Name */}
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                    Full Name *
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-[var(--color-teal)] focus:border-transparent text-gray-900"
                    required
                  />
                </div>

                {/* Email */}
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                    Email Address *
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-[var(--color-teal)] focus:border-transparent text-gray-900"
                    required
                  />
                </div>

                {/* Phone */}
                <div>
                  <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                    Phone Number
                  </label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-[var(--color-teal)] focus:border-transparent text-gray-900"
                  />
                </div>

                {/* Subject */}
                <div>
                  <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-2">
                    Subject *
                  </label>
                  <input
                    type="text"
                    id="subject"
                    name="subject"
                    value={formData.subject}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-[var(--color-teal)] focus:border-transparent text-gray-900"
                    required
                  />
                </div>

                {/* Message */}
                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                    Message *
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleInputChange}
                    rows={6}
                    className="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-[var(--color-teal)] focus:border-transparent resize-vertical text-gray-900"
                    required
                  />
                </div>

                {/* Submit Button */}
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full bg-[var(--color-navy)] text-white px-8 py-3 rounded-md font-semibold hover:bg-[var(--color-teal)] transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2 shadow-lg hover:shadow-xl"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      <span>Sending...</span>
                    </>
                  ) : (
                    <>
                      <Send className="w-4 h-4" />
                      <span>Send Message</span>
                    </>
                  )}
                </button>

                {/* Status Messages */}
                {submitStatus === 'success' && (
                  <div className="p-4 bg-green-50 border border-green-200 rounded-md">
                    <p className="text-green-800">Thank you! Your message has been sent successfully. We&apos;ll get back to you soon.</p>
                  </div>
                )}

                {submitStatus === 'error' && (
                  <div className="p-4 bg-red-50 border border-red-200 rounded-md">
                    <p className="text-red-800">Sorry, there was an error sending your message. Please try again or contact us directly.</p>
                  </div>
                )}
              </form>
            </div>
          </div>
        </div>

        {/* FAQ Section */}
        <section className="mt-20 bg-gray-50 py-16 -mx-4 px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">Frequently Asked Questions</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">What is your minimum order quantity?</h3>
                <p className="text-gray-600">We don&apos;t have a minimum order quantity. Whether you need 1 gift or 1000, we&apos;re here to help.</p>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">How long does delivery take?</h3>
                <p className="text-gray-600">Ready-to-ship gifts typically take 3-5 business days. Custom orders may take 7-14 days depending on complexity.</p>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Do you ship pan-India?</h3>
                <p className="text-gray-600">Yes! We offer pan-India shipping for all our products with reliable courier partners.</p>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Can I customize existing products?</h3>
                <p className="text-gray-600">Absolutely! Most of our products can be customized with your branding, messaging, or specific requirements.</p>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
}
