import React from 'react';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';

export default function AboutUs() {
  return (
    <div className="min-h-screen bg-white">
      <div className="container mx-auto px-4 py-16">
        {/* Back to Home Link */}
        <div className="mb-8">
          <Link
            href="/"
            className="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors duration-200 group"
          >
            <ArrowLeft className="w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform duration-200" />
            Back to Home
          </Link>
        </div>

        {/* Page Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            About Customine
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Gifting is an art. Let us take care of yours.
          </p>
        </div>

        {/* Mission Section */}
        <section className="mb-16">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-gray-900 mb-6">OUR MISSION</h2>
            <div className="prose prose-lg text-gray-700 leading-relaxed">
              <p className="mb-4">
                At Customine, we believe gifting is more than a gesture—it&apos;s a meaningful expression of care, gratitude, and celebration. But we don&apos;t stop at aesthetics. While beautiful design matters deeply to us, so do precision, reliability, and seamless execution.
              </p>
              <p className="mb-4">
                Our mission is simple: to bring joy to the recipient and peace of mind to the sender. From idea to delivery, we take gifting completely off your shoulders so you can focus on the moment, not the logistics.
              </p>
              <p>
                We aim to create thoughtful, personalized gift experiences that make both giving and receiving unforgettable.
              </p>
            </div>
          </div>
        </section>

        {/* Story Section */}
        <section className="mb-16 bg-gray-50 py-16 -mx-4 px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-gray-900 mb-6">OUR STORY</h2>
            <div className="prose prose-lg text-gray-700 leading-relaxed">
              <p className="mb-4">
                Welcome to Customine—a modern gifting studio built on the belief that every occasion deserves a curated, heartwarming, and hassle-free gift.
              </p>
              <p className="mb-4">
                Our journey began with a simple frustration: beautiful gifts were hard to find, and even harder to pull off seamlessly. So, we set out to change that.
              </p>
              <p className="mb-4">
                While we may be new, our passion runs deep. What started as a dream is now a growing team, fueled by creativity and backed by solid systems. We&apos;re here to serve individuals, event planners, and businesses who want gifting done right—with thoughtfulness, elegance, and ease.
              </p>
              <p>
                Whether it&apos;s a wedding welcome box, a corporate thank-you, or a personal milestone, we&apos;re committed to making each gift uniquely yours.
              </p>
            </div>
          </div>
        </section>

        {/* Clients Section */}
        <section className="mb-16">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-gray-900 mb-6">OUR CLIENTS</h2>
            <div className="prose prose-lg text-gray-700 leading-relaxed">
              <p className="mb-4">
                You&apos;re someone who values both form and function. Maybe you&apos;re:
              </p>
              <ul className="list-disc pl-6 mb-4 space-y-2">
                <li>An event planner who wants memorable welcome gifts for clients or guests</li>
                <li>A business owner aiming to elevate your client experience</li>
                <li>A soon-to-be-wed couple looking to wow your guests</li>
                <li>Or just someone short on time but big on thoughtfulness</li>
              </ul>
              <p className="mb-4">
                Whatever the case, you&apos;re done with last-minute stress and tired of generic options. You&apos;re ready to hand it over to someone who gets it.
              </p>
              <p>
                Our clients trust us not just for good taste, but for flawless follow-through. With Customine, you&apos;ll never have to assemble another gift in a living room or scramble the day before a big event. That&apos;s our job now.
              </p>
            </div>
          </div>
        </section>

        {/* Studio Section */}
        <section className="mb-16 bg-gray-50 py-16 -mx-4 px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-gray-900 mb-6">OUR STUDIO</h2>
            <div className="prose prose-lg text-gray-700 leading-relaxed">
              <p className="mb-4">
                Customine is proudly taking its first steps, and though we may not have a 14,000 sq. ft. warehouse just yet, we operate with the same big-picture mindset.
              </p>
              <p className="mb-4">
                We&apos;ve set up a cozy but capable workspace where creativity meets logistics. Every box packed, every ribbon tied, and every note handwritten is done with genuine care and pride. As we grow, so will our space—but our values will remain the same.
              </p>
              <p>
                Our goal? To become a trusted name in gifting, known for warmth, style, and dependable service.
              </p>
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <section className="text-center">
          <div className="max-w-2xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4" style={{ fontFamily: 'Playfair Display, serif' }}>
              Best Selling Ready-to-Ship
            </h2>
            <p className="text-lg text-gray-600 mb-8">
              Let us handle your next gifting occasion with care, creativity, and precision.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="/collections/all"
                className="bg-gray-900 text-white px-8 py-3 rounded-md font-semibold hover:bg-gray-800 transition-colors"
              >
                Browse Ready-to-Ship Gifts
              </a>
              <a
                href="/pages/contact-us"
                className="border border-gray-900 text-gray-900 px-8 py-3 rounded-md font-semibold hover:bg-gray-900 hover:text-white transition-colors"
              >
                Contact Us
              </a>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
}
