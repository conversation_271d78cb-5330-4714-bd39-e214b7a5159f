'use client';

import React from 'react';
import Link from 'next/link';
import {
  ArrowRight,
  Phone,
} from 'lucide-react';

export default function FullCustomPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-amber-50 via-white to-orange-50">
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <div className="text-center">
            <h1 className="text-6xl md:text-7xl font-bold text-gray-900 mb-6">
              Our Gifting Process
            </h1>
            
            <p className="text-2xl text-gray-600 mb-12 font-light italic">
              Beautiful gifts. Effortless experience.
            </p>
          </div>
        </div>
      </section>

      {/* Process Steps - Vertical Timeline */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="relative">
            {/* Vertical Timeline Line */}
            <div className="absolute left-1/2 transform -translate-x-1/2 w-0.5 bg-gray-300 h-full"></div>

            <div className="space-y-16">
              {/* Step 1 */}
              <div className="relative flex items-center">
                <div className="flex-1 pr-8 text-right">
                  <h3 className="text-xl font-bold text-gray-900 mb-3">SUBMIT YOUR INQUIRY</h3>
                  <p className="text-gray-600 leading-relaxed">
                    Start by filling out our quick inquiry form. Share the essentials—date, location, guest count—and your vision for the event&apos;s style and branding.
                  </p>
                </div>
                <div className="absolute left-1/2 transform -translate-x-1/2 w-16 h-16 bg-[#f5d6c0] rounded-full flex items-center justify-center z-10">
                  <span className="text-2xl font-bold text-gray-800">1</span>
                </div>
                <div className="flex-1 pl-8"></div>
              </div>

              {/* Step 2 */}
              <div className="relative flex items-center">
                <div className="flex-1 pr-8"></div>
                <div className="absolute left-1/2 transform -translate-x-1/2 w-16 h-16 bg-[#f5d6c0] rounded-full flex items-center justify-center z-10">
                  <span className="text-2xl font-bold text-gray-800">2</span>
                </div>
                <div className="flex-1 pl-8">
                  <h3 className="text-xl font-bold text-gray-900 mb-3">LET&apos;S CHAT</h3>
                  <p className="text-gray-600 leading-relaxed">
                    We&apos;ll send over all the details you need to know. Want to talk it out? We&apos;re happy to hop on a free consultation call.
                  </p>
                </div>
              </div>

              {/* Step 3 */}
              <div className="relative flex items-center">
                <div className="flex-1 pr-8 text-right">
                  <h3 className="text-xl font-bold text-gray-900 mb-3">LOCK IT IN</h3>
                  <p className="text-gray-600 leading-relaxed">
                    Once you&apos;re ready, secure your spot by signing the agreement and paying the retainer. Then complete our quick Gift Design Questionnaire to help us personalize every detail.
                  </p>
                </div>
                <div className="absolute left-1/2 transform -translate-x-1/2 w-16 h-16 bg-[#f5d6c0] rounded-full flex items-center justify-center z-10">
                  <span className="text-2xl font-bold text-gray-800">3</span>
                </div>
                <div className="flex-1 pl-8"></div>
              </div>

              {/* Step 4 */}
              <div className="relative flex items-center">
                <div className="flex-1 pr-8"></div>
                <div className="absolute left-1/2 transform -translate-x-1/2 w-16 h-16 bg-[#f5d6c0] rounded-full flex items-center justify-center z-10">
                  <span className="text-2xl font-bold text-gray-800">4</span>
                </div>
                <div className="flex-1 pl-8">
                  <h3 className="text-xl font-bold text-gray-900 mb-3">DESIGN MAGIC BEGINS</h3>
                  <p className="text-gray-600 leading-relaxed">
                    Using your input, we&apos;ll craft an initial custom gift design tailored to your aesthetic and needs.
                  </p>
                </div>
              </div>

              {/* Step 5 */}
              <div className="relative flex items-center">
                <div className="flex-1 pr-8 text-right">
                  <h3 className="text-xl font-bold text-gray-900 mb-3">REFINE & APPROVE</h3>
                  <p className="text-gray-600 leading-relaxed">
                    We&apos;ll collaborate with you on edits until the design is just right. Once approved and paid in full—cheers! Your gifting journey is officially underway.
                  </p>
                </div>
                <div className="absolute left-1/2 transform -translate-x-1/2 w-16 h-16 bg-[#f5d6c0] rounded-full flex items-center justify-center z-10">
                  <span className="text-2xl font-bold text-gray-800">5</span>
                </div>
                <div className="flex-1 pl-8"></div>
              </div>

              {/* Step 6 */}
              <div className="relative flex items-center">
                <div className="flex-1 pr-8"></div>
                <div className="absolute left-1/2 transform -translate-x-1/2 w-16 h-16 bg-[#f5d6c0] rounded-full flex items-center justify-center z-10">
                  <span className="text-2xl font-bold text-gray-800">6</span>
                </div>
                <div className="flex-1 pl-8">
                  <h3 className="text-xl font-bold text-gray-900 mb-3">SIT BACK, WE&apos;VE GOT THIS</h3>
                  <p className="text-gray-600 leading-relaxed">
                    From assembly to delivery and even hotel or venue coordination—we handle it all while you stay stress-free.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Customine Logo Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="w-32 h-32 mx-auto mb-8 bg-white rounded-full flex items-center justify-center shadow-lg">
            <div className="text-3xl text-gray-800" style={{ fontFamily: 'cursive', fontStyle: 'italic' }}>
              Customine
            </div>
          </div>
          <p className="text-gray-600 text-lg font-light">
            MADE JUST FOR YOU
          </p>
        </div>
      </section>



      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-amber-600 to-orange-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold text-white mb-6">
            Ready to Start Your Custom Gift Journey?
          </h2>
          <p className="text-xl text-amber-100 mb-10 max-w-2xl mx-auto">
            Let&apos;s create something extraordinary together. Submit your inquiry today and experience our seamless gifting process.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link 
              href="/contact"
              className="inline-flex items-center px-8 py-4 bg-white text-amber-600 font-medium rounded-lg hover:bg-amber-50 transition-colors duration-200"
            >
              Submit Your Inquiry
              <ArrowRight className="ml-2 w-5 h-5" />
            </Link>
            <Link 
              href="/contact"
              className="inline-flex items-center px-8 py-4 bg-transparent border-2 border-white text-white font-medium rounded-lg hover:bg-white hover:text-amber-600 transition-colors duration-200"
            >
              <Phone className="mr-2 w-5 h-5" />
              Schedule a Call
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
