// AWS Configuration for Customine
// This configuration supports both development and production deployments

export interface AWSConfig {
  s3: {
    bucketName: string;
    region: string;
    publicUrl: string;
  };
  graphql: {
    endpoint: string;
    region: string;
  };
}

// Get environment-specific configuration
const getAWSConfig = (): AWSConfig => {
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  if (isDevelopment) {
    // Development configuration (local development)
    return {
      s3: {
        bucketName: 'customine4ae3bb77bc244f68885ca662b9bd1b4e1a04b-dev',
        region: 'ap-south-1',
        publicUrl: 'https://customine4ae3bb77bc244f68885ca662b9bd1b4e1a04b-dev.s3.ap-south-1.amazonaws.com'
      },
      graphql: {
        endpoint: 'https://your-dev-graphql-endpoint.amazonaws.com/graphql',
        region: 'ap-south-1'
      }
    };
  } else {
    // Production configuration (HostGator deployment)
    return {
      s3: {
        bucketName: process.env.NEXT_PUBLIC_S3_BUCKET || 'customine4ae3bb77bc244f68885ca662b9bd1b4e1a04b-prod',
        region: process.env.NEXT_PUBLIC_AWS_REGION || 'ap-south-1',
        publicUrl: process.env.NEXT_PUBLIC_S3_URL || 'https://customine4ae3bb77bc244f68885ca662b9bd1b4e1a04b-prod.s3.ap-south-1.amazonaws.com'
      },
      graphql: {
        endpoint: process.env.NEXT_PUBLIC_GRAPHQL_ENDPOINT || 'https://your-prod-graphql-endpoint.amazonaws.com/graphql',
        region: process.env.NEXT_PUBLIC_AWS_REGION || 'ap-south-1'
      }
    };
  }
};

export const awsConfig = getAWSConfig();

// Helper function to generate S3 URLs
export const generateS3Url = (key: string): string => {
  return `${awsConfig.s3.publicUrl}/public/${key}`;
};

// Helper function to generate optimized image URLs
export const generateOptimizedImageUrl = (key: string, options?: {
  width?: number;
  height?: number;
  quality?: number;
}): string => {
  const baseUrl = generateS3Url(key);
  
  // For future: Add CloudFront or image optimization service
  // if (options) {
  //   return `${baseUrl}?w=${options.width}&h=${options.height}&q=${options.quality}`;
  // }
  
  return baseUrl;
};

// Environment variables for HostGator deployment
export const getEnvironmentVariables = () => ({
  // Add these to your HostGator environment or .env.production file
  NEXT_PUBLIC_S3_BUCKET: 'customine4ae3bb77bc244f68885ca662b9bd1b4e1a04b-prod',
  NEXT_PUBLIC_AWS_REGION: 'ap-south-1',
  NEXT_PUBLIC_S3_URL: 'https://customine4ae3bb77bc244f68885ca662b9bd1b4e1a04b-prod.s3.ap-south-1.amazonaws.com',
  NEXT_PUBLIC_GRAPHQL_ENDPOINT: 'https://your-prod-graphql-endpoint.amazonaws.com/graphql'
});
