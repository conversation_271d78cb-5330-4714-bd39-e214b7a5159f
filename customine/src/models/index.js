// @ts-check
import { initSchema } from '@aws-amplify/datastore';
import { schema } from './schema';

const OrderStatus = {
  "PENDING": "PENDING",
  "CONFIRMED": "CONFIRMED",
  "PROCESSING": "PROCESSING",
  "PACKED": "PACKED",
  "SHIPPED": "SHIPPED",
  "OUT_FOR_DELIVERY": "OUT_FOR_DELIVERY",
  "DELIVERED": "DELIVERED",
  "CANCELLED": "CANCELLED",
  "REFUNDED": "REFUNDED",
  "RETURNED": "RETURNED"
};

const PaymentStatus = {
  "PENDING": "PENDING",
  "PROCESSING": "PROCESSING",
  "PAID": "PAID",
  "FAILED": "FAILED",
  "CANCELLED": "CANCELLED",
  "REFUNDED": "REFUNDED",
  "PARTIALLY_REFUNDED": "PARTIALLY_REFUNDED"
};

const InquiryType = {
  "GENERAL_INQUIRY": "GENERAL_INQUIRY",
  "PRODUCT_INQUIRY": "PRODUCT_INQUIRY",
  "ORDER_INQUIRY": "ORDER_INQUIRY",
  "SPECIAL_OCCASION": "SPECIAL_OCCASION",
  "CORPORATE_INQUIRY": "CORPORATE_INQUIRY",
  "BULK_ORDER": "BULK_ORDER",
  "CUSTOM_GIFT_BOX": "CUSTOM_GIFT_BOX",
  "COMPLAINT": "COMPLAINT",
  "SUGGESTION": "SUGGESTION",
  "PARTNERSHIP": "PARTNERSHIP",
  "MEDIA_INQUIRY": "MEDIA_INQUIRY",
  "NEWSLETTER_SIGNUP": "NEWSLETTER_SIGNUP"
};

const InquiryStatus = {
  "NEW": "NEW",
  "OPEN": "OPEN",
  "IN_PROGRESS": "IN_PROGRESS",
  "WAITING_FOR_CUSTOMER": "WAITING_FOR_CUSTOMER",
  "RESOLVED": "RESOLVED",
  "CLOSED": "CLOSED",
  "ESCALATED": "ESCALATED"
};

const { UserProfile, Product, CartItem, Order, OrderItem, Inquiry, InquiryResponse, Newsletter, ShippingAddress } = initSchema(schema);

export {
  UserProfile,
  Product,
  CartItem,
  Order,
  OrderItem,
  Inquiry,
  InquiryResponse,
  Newsletter,
  OrderStatus,
  PaymentStatus,
  InquiryType,
  InquiryStatus,
  ShippingAddress
};