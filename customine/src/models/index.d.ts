import { ModelInit, MutableModel, __modelMeta__, ManagedIdentifier } from "@aws-amplify/datastore";
// @ts-ignore
import { LazyLoading, LazyLoadingDisabled, AsyncCollection, AsyncItem } from "@aws-amplify/datastore";

export enum OrderStatus {
  PENDING = "PENDING",
  CONFIRMED = "CONFIRMED",
  PROCESSING = "PROCESSING",
  PACKED = "PACKED",
  SHIPPED = "SHIPPED",
  OUT_FOR_DELIVERY = "OUT_FOR_DELIVERY",
  DELIVERED = "DELIVERED",
  CANCELLED = "CANCELLED",
  REFUNDED = "REFUNDED",
  RETURNED = "RETURNED"
}

export enum PaymentStatus {
  PENDING = "PENDING",
  PROCESSING = "PROCESSING",
  PAID = "PAID",
  FAILED = "FAILED",
  CANCELLED = "CANCELLED",
  REFUNDED = "REFUNDED",
  PARTIALLY_REFUNDED = "PARTIALLY_REFUNDED"
}

export enum InquiryType {
  GENERAL_INQUIRY = "GENERAL_INQUIRY",
  PRODUCT_INQUIRY = "PRODUCT_INQUIRY",
  ORDER_INQUIRY = "ORDER_INQUIRY",
  SPECIAL_OCCASION = "SPECIAL_OCCASION",
  CORPORATE_INQUIRY = "CORPORATE_INQUIRY",
  BULK_ORDER = "BULK_ORDER",
  CUSTOM_GIFT_BOX = "CUSTOM_GIFT_BOX",
  COMPLAINT = "COMPLAINT",
  SUGGESTION = "SUGGESTION",
  PARTNERSHIP = "PARTNERSHIP",
  MEDIA_INQUIRY = "MEDIA_INQUIRY",
  NEWSLETTER_SIGNUP = "NEWSLETTER_SIGNUP"
}

export enum InquiryStatus {
  NEW = "NEW",
  OPEN = "OPEN",
  IN_PROGRESS = "IN_PROGRESS",
  WAITING_FOR_CUSTOMER = "WAITING_FOR_CUSTOMER",
  RESOLVED = "RESOLVED",
  CLOSED = "CLOSED",
  ESCALATED = "ESCALATED"
}

type EagerShippingAddress = {
  readonly name: string;
  readonly phone: string;
  readonly email?: string | null;
  readonly addressLine1: string;
  readonly addressLine2?: string | null;
  readonly landmark?: string | null;
  readonly city: string;
  readonly state: string;
  readonly pincode: string;
  readonly country: string;
  readonly addressType?: string | null;
}

type LazyShippingAddress = {
  readonly name: string;
  readonly phone: string;
  readonly email?: string | null;
  readonly addressLine1: string;
  readonly addressLine2?: string | null;
  readonly landmark?: string | null;
  readonly city: string;
  readonly state: string;
  readonly pincode: string;
  readonly country: string;
  readonly addressType?: string | null;
}

export declare type ShippingAddress = LazyLoading extends LazyLoadingDisabled ? EagerShippingAddress : LazyShippingAddress

export declare const ShippingAddress: (new (init: ModelInit<ShippingAddress>) => ShippingAddress)

type EagerUserProfile = {
  readonly [__modelMeta__]: {
    identifier: ManagedIdentifier<UserProfile, 'id'>;
  };
  readonly id: string;
  readonly email: string;
  readonly name: string;
  readonly phone?: string | null;
  readonly address?: string | null;
  readonly city?: string | null;
  readonly state?: string | null;
  readonly pincode?: string | null;
  readonly dateOfBirth?: string | null;
  readonly gender?: string | null;
  readonly preferences?: string | null;
  readonly isActive: boolean;
  readonly createdAt: string;
  readonly updatedAt: string;
  readonly cartItems?: (CartItem | null)[] | null;
  readonly orders?: (Order | null)[] | null;
  readonly inquiries?: (Inquiry | null)[] | null;
}

type LazyUserProfile = {
  readonly [__modelMeta__]: {
    identifier: ManagedIdentifier<UserProfile, 'id'>;
  };
  readonly id: string;
  readonly email: string;
  readonly name: string;
  readonly phone?: string | null;
  readonly address?: string | null;
  readonly city?: string | null;
  readonly state?: string | null;
  readonly pincode?: string | null;
  readonly dateOfBirth?: string | null;
  readonly gender?: string | null;
  readonly preferences?: string | null;
  readonly isActive: boolean;
  readonly createdAt: string;
  readonly updatedAt: string;
  readonly cartItems: AsyncCollection<CartItem>;
  readonly orders: AsyncCollection<Order>;
  readonly inquiries: AsyncCollection<Inquiry>;
}

export declare type UserProfile = LazyLoading extends LazyLoadingDisabled ? EagerUserProfile : LazyUserProfile

export declare const UserProfile: (new (init: ModelInit<UserProfile>) => UserProfile) & {
  copyOf(source: UserProfile, mutator: (draft: MutableModel<UserProfile>) => MutableModel<UserProfile> | void): UserProfile;
}

type EagerProduct = {
  readonly [__modelMeta__]: {
    identifier: ManagedIdentifier<Product, 'id'>;
  };
  readonly id: string;
  readonly name: string;
  readonly description: string;
  readonly shortDescription?: string | null;
  readonly price: number;
  readonly originalPrice?: number | null;
  readonly images: string[];
  readonly category: string;
  readonly subcategory?: string | null;
  readonly tags?: string[] | null;
  readonly inStock: boolean;
  readonly stockQuantity?: number | null;
  readonly slug: string;
  readonly sku?: string | null;
  readonly weight?: number | null;
  readonly dimensions?: string | null;
  readonly luxuryDescription?: string | null;
  readonly budgetDescription?: string | null;
  readonly narration?: string | null;
  readonly features?: string[] | null;
  readonly luxuryFeatures?: string[] | null;
  readonly budgetFeatures?: string[] | null;
  readonly specifications?: string | null;
  readonly materials?: string[] | null;
  readonly careInstructions?: string | null;
  readonly warranty?: string | null;
  readonly shippingInfo?: string | null;
  readonly returnPolicy?: string | null;
  readonly rating?: number | null;
  readonly reviewCount?: number | null;
  readonly badge?: string | null;
  readonly relatedProductIds?: string[] | null;
  readonly metaTitle?: string | null;
  readonly metaDescription?: string | null;
  readonly isActive: boolean;
  readonly isFeatured: boolean;
  readonly sortOrder?: number | null;
  readonly createdBy?: string | null;
  readonly createdAt: string;
  readonly updatedAt: string;
  readonly cartItems?: (CartItem | null)[] | null;
  readonly orderItems?: (OrderItem | null)[] | null;
}

type LazyProduct = {
  readonly [__modelMeta__]: {
    identifier: ManagedIdentifier<Product, 'id'>;
  };
  readonly id: string;
  readonly name: string;
  readonly description: string;
  readonly shortDescription?: string | null;
  readonly price: number;
  readonly originalPrice?: number | null;
  readonly images: string[];
  readonly category: string;
  readonly subcategory?: string | null;
  readonly tags?: string[] | null;
  readonly inStock: boolean;
  readonly stockQuantity?: number | null;
  readonly slug: string;
  readonly sku?: string | null;
  readonly weight?: number | null;
  readonly dimensions?: string | null;
  readonly luxuryDescription?: string | null;
  readonly budgetDescription?: string | null;
  readonly narration?: string | null;
  readonly features?: string[] | null;
  readonly luxuryFeatures?: string[] | null;
  readonly budgetFeatures?: string[] | null;
  readonly specifications?: string | null;
  readonly materials?: string[] | null;
  readonly careInstructions?: string | null;
  readonly warranty?: string | null;
  readonly shippingInfo?: string | null;
  readonly returnPolicy?: string | null;
  readonly rating?: number | null;
  readonly reviewCount?: number | null;
  readonly badge?: string | null;
  readonly relatedProductIds?: string[] | null;
  readonly metaTitle?: string | null;
  readonly metaDescription?: string | null;
  readonly isActive: boolean;
  readonly isFeatured: boolean;
  readonly sortOrder?: number | null;
  readonly createdBy?: string | null;
  readonly createdAt: string;
  readonly updatedAt: string;
  readonly cartItems: AsyncCollection<CartItem>;
  readonly orderItems: AsyncCollection<OrderItem>;
}

export declare type Product = LazyLoading extends LazyLoadingDisabled ? EagerProduct : LazyProduct

export declare const Product: (new (init: ModelInit<Product>) => Product) & {
  copyOf(source: Product, mutator: (draft: MutableModel<Product>) => MutableModel<Product> | void): Product;
}

type EagerCartItem = {
  readonly [__modelMeta__]: {
    identifier: ManagedIdentifier<CartItem, 'id'>;
  };
  readonly id: string;
  readonly userId: string;
  readonly productId: string;
  readonly quantity: number;
  readonly price: number;
  readonly giftType?: string | null;
  readonly customization?: string | null;
  readonly notes?: string | null;
  readonly createdAt: string;
  readonly updatedAt: string;
  readonly user?: UserProfile | null;
  readonly product?: Product | null;
}

type LazyCartItem = {
  readonly [__modelMeta__]: {
    identifier: ManagedIdentifier<CartItem, 'id'>;
  };
  readonly id: string;
  readonly userId: string;
  readonly productId: string;
  readonly quantity: number;
  readonly price: number;
  readonly giftType?: string | null;
  readonly customization?: string | null;
  readonly notes?: string | null;
  readonly createdAt: string;
  readonly updatedAt: string;
  readonly user: AsyncItem<UserProfile | undefined>;
  readonly product: AsyncItem<Product | undefined>;
}

export declare type CartItem = LazyLoading extends LazyLoadingDisabled ? EagerCartItem : LazyCartItem

export declare const CartItem: (new (init: ModelInit<CartItem>) => CartItem) & {
  copyOf(source: CartItem, mutator: (draft: MutableModel<CartItem>) => MutableModel<CartItem> | void): CartItem;
}

type EagerOrder = {
  readonly [__modelMeta__]: {
    identifier: ManagedIdentifier<Order, 'id'>;
  };
  readonly id: string;
  readonly userId: string;
  readonly orderNumber: string;
  readonly status: OrderStatus | keyof typeof OrderStatus;
  readonly subtotal: number;
  readonly tax: number;
  readonly shipping: number;
  readonly discount: number;
  readonly total: number;
  readonly currency: string;
  readonly shippingAddress: ShippingAddress;
  readonly billingAddress?: ShippingAddress | null;
  readonly paymentMethod: string;
  readonly paymentStatus: PaymentStatus | keyof typeof PaymentStatus;
  readonly paymentId?: string | null;
  readonly razorpayOrderId?: string | null;
  readonly razorpayPaymentId?: string | null;
  readonly trackingNumber?: string | null;
  readonly estimatedDelivery?: string | null;
  readonly deliveredAt?: string | null;
  readonly customerNotes?: string | null;
  readonly adminNotes?: string | null;
  readonly giftMessage?: string | null;
  readonly specialInstructions?: string | null;
  readonly createdAt: string;
  readonly updatedAt: string;
  readonly user?: UserProfile | null;
  readonly items?: (OrderItem | null)[] | null;
}

type LazyOrder = {
  readonly [__modelMeta__]: {
    identifier: ManagedIdentifier<Order, 'id'>;
  };
  readonly id: string;
  readonly userId: string;
  readonly orderNumber: string;
  readonly status: OrderStatus | keyof typeof OrderStatus;
  readonly subtotal: number;
  readonly tax: number;
  readonly shipping: number;
  readonly discount: number;
  readonly total: number;
  readonly currency: string;
  readonly shippingAddress: ShippingAddress;
  readonly billingAddress?: ShippingAddress | null;
  readonly paymentMethod: string;
  readonly paymentStatus: PaymentStatus | keyof typeof PaymentStatus;
  readonly paymentId?: string | null;
  readonly razorpayOrderId?: string | null;
  readonly razorpayPaymentId?: string | null;
  readonly trackingNumber?: string | null;
  readonly estimatedDelivery?: string | null;
  readonly deliveredAt?: string | null;
  readonly customerNotes?: string | null;
  readonly adminNotes?: string | null;
  readonly giftMessage?: string | null;
  readonly specialInstructions?: string | null;
  readonly createdAt: string;
  readonly updatedAt: string;
  readonly user: AsyncItem<UserProfile | undefined>;
  readonly items: AsyncCollection<OrderItem>;
}

export declare type Order = LazyLoading extends LazyLoadingDisabled ? EagerOrder : LazyOrder

export declare const Order: (new (init: ModelInit<Order>) => Order) & {
  copyOf(source: Order, mutator: (draft: MutableModel<Order>) => MutableModel<Order> | void): Order;
}

type EagerOrderItem = {
  readonly [__modelMeta__]: {
    identifier: ManagedIdentifier<OrderItem, 'id'>;
  };
  readonly id: string;
  readonly orderId: string;
  readonly productId: string;
  readonly userId: string;
  readonly quantity: number;
  readonly price: number;
  readonly giftType?: string | null;
  readonly customization?: string | null;
  readonly notes?: string | null;
  readonly createdAt: string;
  readonly updatedAt: string;
  readonly order?: Order | null;
  readonly product?: Product | null;
}

type LazyOrderItem = {
  readonly [__modelMeta__]: {
    identifier: ManagedIdentifier<OrderItem, 'id'>;
  };
  readonly id: string;
  readonly orderId: string;
  readonly productId: string;
  readonly userId: string;
  readonly quantity: number;
  readonly price: number;
  readonly giftType?: string | null;
  readonly customization?: string | null;
  readonly notes?: string | null;
  readonly createdAt: string;
  readonly updatedAt: string;
  readonly order: AsyncItem<Order | undefined>;
  readonly product: AsyncItem<Product | undefined>;
}

export declare type OrderItem = LazyLoading extends LazyLoadingDisabled ? EagerOrderItem : LazyOrderItem

export declare const OrderItem: (new (init: ModelInit<OrderItem>) => OrderItem) & {
  copyOf(source: OrderItem, mutator: (draft: MutableModel<OrderItem>) => MutableModel<OrderItem> | void): OrderItem;
}

type EagerInquiry = {
  readonly [__modelMeta__]: {
    identifier: ManagedIdentifier<Inquiry, 'id'>;
  };
  readonly id: string;
  readonly userId?: string | null;
  readonly type: InquiryType | keyof typeof InquiryType;
  readonly name: string;
  readonly email: string;
  readonly phone?: string | null;
  readonly company?: string | null;
  readonly subject: string;
  readonly message: string;
  readonly category?: string | null;
  readonly priority?: string | null;
  readonly eventType?: string | null;
  readonly eventDate?: string | null;
  readonly guestCount?: number | null;
  readonly budget?: string | null;
  readonly location?: string | null;
  readonly status: InquiryStatus | keyof typeof InquiryStatus;
  readonly assignedTo?: string | null;
  readonly isRead: boolean;
  readonly isReplied: boolean;
  readonly adminNotes?: string | null;
  readonly internalNotes?: string | null;
  readonly attachments?: string[] | null;
  readonly relatedOrderId?: string | null;
  readonly relatedProductId?: string | null;
  readonly createdAt: string;
  readonly updatedAt: string;
  readonly user?: UserProfile | null;
  readonly responses?: (InquiryResponse | null)[] | null;
}

type LazyInquiry = {
  readonly [__modelMeta__]: {
    identifier: ManagedIdentifier<Inquiry, 'id'>;
  };
  readonly id: string;
  readonly userId?: string | null;
  readonly type: InquiryType | keyof typeof InquiryType;
  readonly name: string;
  readonly email: string;
  readonly phone?: string | null;
  readonly company?: string | null;
  readonly subject: string;
  readonly message: string;
  readonly category?: string | null;
  readonly priority?: string | null;
  readonly eventType?: string | null;
  readonly eventDate?: string | null;
  readonly guestCount?: number | null;
  readonly budget?: string | null;
  readonly location?: string | null;
  readonly status: InquiryStatus | keyof typeof InquiryStatus;
  readonly assignedTo?: string | null;
  readonly isRead: boolean;
  readonly isReplied: boolean;
  readonly adminNotes?: string | null;
  readonly internalNotes?: string | null;
  readonly attachments?: string[] | null;
  readonly relatedOrderId?: string | null;
  readonly relatedProductId?: string | null;
  readonly createdAt: string;
  readonly updatedAt: string;
  readonly user: AsyncItem<UserProfile | undefined>;
  readonly responses: AsyncCollection<InquiryResponse>;
}

export declare type Inquiry = LazyLoading extends LazyLoadingDisabled ? EagerInquiry : LazyInquiry

export declare const Inquiry: (new (init: ModelInit<Inquiry>) => Inquiry) & {
  copyOf(source: Inquiry, mutator: (draft: MutableModel<Inquiry>) => MutableModel<Inquiry> | void): Inquiry;
}

type EagerInquiryResponse = {
  readonly [__modelMeta__]: {
    identifier: ManagedIdentifier<InquiryResponse, 'id'>;
  };
  readonly id: string;
  readonly inquiryId: string;
  readonly responderId: string;
  readonly responderName: string;
  readonly responderEmail: string;
  readonly message: string;
  readonly isInternal: boolean;
  readonly attachments?: string[] | null;
  readonly createdAt: string;
  readonly updatedAt: string;
  readonly inquiry?: Inquiry | null;
}

type LazyInquiryResponse = {
  readonly [__modelMeta__]: {
    identifier: ManagedIdentifier<InquiryResponse, 'id'>;
  };
  readonly id: string;
  readonly inquiryId: string;
  readonly responderId: string;
  readonly responderName: string;
  readonly responderEmail: string;
  readonly message: string;
  readonly isInternal: boolean;
  readonly attachments?: string[] | null;
  readonly createdAt: string;
  readonly updatedAt: string;
  readonly inquiry: AsyncItem<Inquiry | undefined>;
}

export declare type InquiryResponse = LazyLoading extends LazyLoadingDisabled ? EagerInquiryResponse : LazyInquiryResponse

export declare const InquiryResponse: (new (init: ModelInit<InquiryResponse>) => InquiryResponse) & {
  copyOf(source: InquiryResponse, mutator: (draft: MutableModel<InquiryResponse>) => MutableModel<InquiryResponse> | void): InquiryResponse;
}

type EagerNewsletter = {
  readonly [__modelMeta__]: {
    identifier: ManagedIdentifier<Newsletter, 'id'>;
  };
  readonly id: string;
  readonly email: string;
  readonly name?: string | null;
  readonly phone?: string | null;
  readonly preferences?: string | null;
  readonly isActive: boolean;
  readonly source?: string | null;
  readonly tags?: string[] | null;
  readonly createdAt: string;
  readonly updatedAt: string;
}

type LazyNewsletter = {
  readonly [__modelMeta__]: {
    identifier: ManagedIdentifier<Newsletter, 'id'>;
  };
  readonly id: string;
  readonly email: string;
  readonly name?: string | null;
  readonly phone?: string | null;
  readonly preferences?: string | null;
  readonly isActive: boolean;
  readonly source?: string | null;
  readonly tags?: string[] | null;
  readonly createdAt: string;
  readonly updatedAt: string;
}

export declare type Newsletter = LazyLoading extends LazyLoadingDisabled ? EagerNewsletter : LazyNewsletter

export declare const Newsletter: (new (init: ModelInit<Newsletter>) => Newsletter) & {
  copyOf(source: Newsletter, mutator: (draft: MutableModel<Newsletter>) => MutableModel<Newsletter> | void): Newsletter;
}