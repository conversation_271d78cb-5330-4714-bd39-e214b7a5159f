/**
 * Product API utilities for Customine
 * Handles product data fetching with AWS Amplify GraphQL
 */

import { generateClient } from 'aws-amplify/api';
import type { GraphQLResult } from '@aws-amplify/api';
import { getProduct, listProducts } from '../graphql/queries';
// Import API configuration to ensure Amplify is configured
import '../utils/api';

// Product interface matching GraphQL schema
export interface Product {
  id: string;
  name: string;
  slug: string;
  price: number;
  originalPrice?: number;
  images: string[];
  category: string;
  subcategory?: string;
  tags?: string[];
  rating?: number;
  reviewCount?: number;
  badge?: string;
  description: string;
  shortDescription?: string;
  narration?: string;
  luxuryDescription?: string;
  budgetDescription?: string;
  features?: string[];
  luxuryFeatures?: string[];
  budgetFeatures?: string[];
  specifications?: any;
  inStock: boolean;
  stockQuantity?: number;
  shippingInfo?: string;
  returnPolicy?: string;
  isFeatured?: boolean;
  isActive?: boolean;
  createdAt?: string;
  updatedAt?: string;
  sku?: string;
  weight?: number;
  dimensions?: string;
  materials?: string[];
  careInstructions?: string;
  warranty?: string;
  relatedProductIds?: string[];
  metaTitle?: string;
  metaDescription?: string;
  sortOrder?: number;
  createdBy?: string;
}

// API Response types
export interface ListProductsResponse {
  items: Product[];
  nextToken?: string;
}

export interface ProductAPIError {
  message: string;
  code?: string;
  details?: any;
}

// Initialize GraphQL client
const client = generateClient();

/**
 * Test API connectivity and check if products exist
 */
export async function testAPIConnection(): Promise<{ connected: boolean; hasProducts: boolean; error?: string }> {
  try {
    console.log('Testing API connection...');

    const result = await client.graphql({
      query: listProducts,
      variables: {
        limit: 1
      }
    }) as GraphQLResult<{ listProducts: ListProductsResponse }>;

    if (result.errors) {
      return {
        connected: false,
        hasProducts: false,
        error: `GraphQL errors: ${result.errors.map(e => e.message).join(', ')}`
      };
    }

    const products = result.data?.listProducts?.items || [];

    return {
      connected: true,
      hasProducts: products.length > 0,
      error: products.length === 0 ? 'Database is empty - no products found' : undefined
    };
  } catch (error) {
    console.error('API connection test failed:', error);
    return {
      connected: false,
      hasProducts: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Fetch all products with optional filtering
 */
export async function fetchProducts(
  filter?: any,
  limit: number = 50,
  nextToken?: string
): Promise<ListProductsResponse> {
  try {
    console.log('Fetching products with filter:', filter);

    const result = await client.graphql({
      query: listProducts,
      variables: {
        filter: {
          // Temporarily removed isActive filter to show all products
          // isActive: { eq: true },
          ...filter
        },
        limit,
        nextToken
      }
    }) as GraphQLResult<{ listProducts: ListProductsResponse }>;

    console.log('Products GraphQL result:', result);

    if (result.errors) {
      console.error('GraphQL errors:', result.errors);
      throw new Error(`GraphQL errors: ${result.errors.map(e => e.message).join(', ')}`);
    }

    const products = result.data?.listProducts || { items: [] };
    console.log('Fetched products count:', products.items.length);

    return products;
  } catch (error) {
    console.error('Error fetching products:', error);

    // Return empty result instead of throwing to allow fallback
    console.warn('API fetch failed, returning empty result to allow fallback');
    return { items: [] };
  }
}

// Removed duplicate fetchProductById function - using the more detailed one below

/**
 * Fetch a single product by ID using getProduct query (more efficient)
 */
export async function fetchProductById(id: string): Promise<Product | null> {
  try {
    console.log('🔍 fetchProductById: Searching for ID:', id);

    const result = await client.graphql({
      query: getProduct,
      variables: {
        id: id
      }
    }) as GraphQLResult<{ getProduct: Product }>;

    console.log('📡 getProduct query result:', {
      hasData: !!result.data,
      hasErrors: !!result.errors,
      productFound: !!result.data?.getProduct,
      searchedId: id
    });

    if (result.errors) {
      console.error('GraphQL errors in fetchProductById:', result.errors);
      return null;
    }

    const product = result.data?.getProduct;
    if (product) {
      console.log('✅ Product found by ID:', {
        id: product.id,
        name: product.name,
        slug: product.slug
      });
      return product;
    } else {
      console.log('❌ No product found with ID:', id);
      return null;
    }
  } catch (error) {
    console.error('💥 Error in fetchProductById:', error);
    return null;
  }
}

/**
 * Fetch a single product by slug using listProducts with filter (legacy method)
 */
export async function fetchProductBySlug(slug: string): Promise<Product | null> {
  try {
    console.log('🔍 fetchProductBySlug: Searching for slug:', slug);

    const result = await client.graphql({
      query: listProducts,
      variables: {
        filter: {
          slug: { eq: slug }
          // Removed isActive filter to show all products
        },
        limit: 1
      }
    }) as GraphQLResult<{ listProducts: ListProductsResponse }>;

    console.log('📡 listProducts query result (with slug filter):', {
      hasData: !!result.data,
      hasErrors: !!result.errors,
      itemCount: result.data?.listProducts?.items?.length || 0,
      searchedSlug: slug
    });

    if (result.errors) {
      console.error('❌ GraphQL errors:', result.errors);
      throw new Error(`GraphQL errors: ${result.errors.map(e => e.message).join(', ')}`);
    }

    const products = result.data?.listProducts?.items || [];

    if (products.length > 0) {
      const product = products[0];
      console.log('✅ Product found:', {
        id: product.id,
        name: product.name,
        slug: product.slug,
        category: product.category,
        isActive: product.isActive,
        hasImages: product.images?.length > 0,
        imageCount: product.images?.length || 0
      });
      return product;
    } else {
      console.log('❌ No product found with isActive filter. Trying without isActive filter...');

      // Try again without isActive filter
      const fallbackResult = await client.graphql({
        query: listProducts,
        variables: {
          filter: {
            slug: { eq: slug }
          },
          limit: 1
        }
      }) as GraphQLResult<{ listProducts: ListProductsResponse }>;

      console.log('📡 Fallback query result (no isActive filter):', {
        hasData: !!fallbackResult.data,
        hasErrors: !!fallbackResult.errors,
        itemCount: fallbackResult.data?.listProducts?.items?.length || 0
      });

      const fallbackProducts = fallbackResult.data?.listProducts?.items || [];

      if (fallbackProducts.length > 0) {
        const product = fallbackProducts[0];
        console.log('✅ Product found without isActive filter:', {
          id: product.id,
          name: product.name,
          slug: product.slug,
          isActive: product.isActive,
          category: product.category
        });
        return product;
      } else {
        console.log('❌ No product found even without isActive filter for slug:', slug);
        // Show debug info
        await debugListAllProductSlugs();
        return null;
      }
    }
  } catch (error) {
    console.error('💥 Error fetching product by slug:', error);
    console.warn(`⚠️ API fetch failed for slug: ${slug}`);
    return null;
  }
}

/**
 * Debug function to list all available products and their slugs
 */
export async function debugListAllProductSlugs(): Promise<void> {
  try {
    console.log('🔍 Debug: Fetching all products to check available slugs...');
    const result = await client.graphql({
      query: listProducts,
      variables: {
        limit: 100
      }
    }) as GraphQLResult<{ listProducts: ListProductsResponse }>;

    const allProducts = result.data?.listProducts?.items || [];
    console.log('📊 Available products in database:', allProducts.map(p => ({
      id: p.id,
      name: p.name,
      slug: p.slug,
      isActive: p.isActive
    })));
  } catch (error) {
    console.error('❌ Error fetching all products for debug:', error);
  }
}

/**
 * Fetch products by category
 */
export async function fetchProductsByCategory(
  category: string,
  limit: number = 20,
  nextToken?: string
): Promise<ListProductsResponse> {
  return fetchProducts(
    { category: { eq: category } },
    limit,
    nextToken
  );
}

/**
 * Fetch featured products with smart sorting
 */
export async function fetchFeaturedProducts(limit: number = 10): Promise<Product[]> {
  try {
    console.log(`🌟 Fetching ${limit} featured products`);

    // Fetch more products than needed to allow for better sorting
    const result = await fetchProducts(
      {
        isFeatured: { eq: true },
        isActive: { eq: true } // Only show active products
      },
      Math.max(limit * 2, 8) // Fetch 2x the limit or at least 8 to have sorting options
    );

    console.log(`📦 Found ${result.items.length} featured products in database`);

    if (result.items.length === 0) {
      console.log('⚠️ No featured products found');
      return [];
    }

    // Sort featured products by priority:
    // 1. sortOrder (ascending - lower numbers first)
    // 2. createdAt (descending - newer first)
    const sortedProducts = result.items.sort((a, b) => {
      // First priority: sortOrder (if available)
      if (a.sortOrder !== undefined && b.sortOrder !== undefined) {
        return a.sortOrder - b.sortOrder;
      }
      if (a.sortOrder !== undefined) return -1;
      if (b.sortOrder !== undefined) return 1;

      // Second priority: newer products first
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });

    // Take only the requested number of products
    const selectedProducts = sortedProducts.slice(0, limit);

    console.log(`✅ Returning top ${selectedProducts.length} featured products:`,
      selectedProducts.map(p => ({ name: p.name, sortOrder: p.sortOrder, isFeatured: p.isFeatured }))
    );

    return selectedProducts;
  } catch (error) {
    console.error('❌ Error fetching featured products:', error);
    return [];
  }
}

/**
 * Gallery Image interface for homepage hero
 */
export interface GalleryImage {
  id: string;
  title: string;
  description?: string;
  imageKey: string;
  imageUrl?: string;
  category: 'HOME' | 'LUXURY' | 'CORPORATE' | 'WEDDING' | 'FESTIVAL' | 'PERSONAL';
  tags?: string[];
  isActive: boolean;
  sortOrder?: number;
  altText?: string;
  metaTitle?: string;
  metaDescription?: string;
  createdBy?: string;
  updatedBy?: string;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * Fetch homepage hero gallery images
 */
export async function fetchHomepageHeroGallery(): Promise<GalleryImage[]> {
  try {
    console.log('Fetching homepage hero gallery images...');

    const { generateClient } = await import('aws-amplify/api');
    const { listGalleries } = await import('../graphql/queries');

    const client = generateClient();

    const result = await client.graphql({
      query: listGalleries,
      variables: {
        filter: {
          category: { eq: 'HOME' },
          isActive: { eq: true }
        },
        limit: 10
      }
    });

    if (result.data?.listGalleries?.items) {
      const galleryItems = result.data.listGalleries.items;

      // Sort by sortOrder if available, otherwise by createdAt
      const sortedItems = galleryItems.sort((a: any, b: any) => {
        if (a.sortOrder !== undefined && b.sortOrder !== undefined) {
          return a.sortOrder - b.sortOrder;
        }
        return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
      });

      // Get image URLs from S3 for each item
      const imagesWithUrls = await Promise.all(
        sortedItems.map(async (item: any) => {
          let imageUrl = '';
          if (item.imageKey) {
            try {
              // For home gallery, use direct public S3 URL to avoid authentication issues with correct bucket name
              imageUrl = `https://customine4ae3bb77bc244f68885ca662b9bd1b4e1a04b-dev.s3.ap-south-1.amazonaws.com/public/${item.imageKey}`;
              console.log('Using public S3 URL for', item.imageKey);
            } catch (urlError) {
              console.error('Error getting image URL:', urlError);
              // Final fallback to direct S3 URL with correct bucket name
              imageUrl = `https://customine4ae3bb77bc244f68885ca662b9bd1b4e1a04b-dev.s3.ap-south-1.amazonaws.com/public/${item.imageKey}`;
            }
          }

          return {
            ...item,
            imageUrl
          } as GalleryImage;
        })
      );

      console.log(`Successfully fetched ${imagesWithUrls.length} homepage hero gallery images`);
      return imagesWithUrls;
    }

    console.log('No homepage hero gallery images found');
    return [];
  } catch (error) {
    console.error('Error fetching homepage hero gallery:', error);
    return [];
  }
}

/**
 * Search products by name or description
 */
export async function searchProducts(
  searchTerm: string,
  limit: number = 20,
  nextToken?: string
): Promise<ListProductsResponse> {
  return fetchProducts(
    {
      or: [
        { name: { contains: searchTerm } },
        { description: { contains: searchTerm } },
        { shortDescription: { contains: searchTerm } }
      ]
    },
    limit,
    nextToken
  );
}

/**
 * Fetch related products by IDs
 */
export async function fetchRelatedProducts(productIds: string[]): Promise<Product[]> {
  try {
    const promises = productIds.map(id => fetchProductById(id));
    const results = await Promise.allSettled(promises);
    
    return results
      .filter((result): result is PromiseFulfilledResult<Product | null> => 
        result.status === 'fulfilled' && result.value !== null
      )
      .map(result => result.value as Product);
  } catch (error) {
    console.error('Error fetching related products:', error);
    return [];
  }
}

/**
 * Transform static product data to API format (for migration purposes only)
 * This function is used by the bulk import script to convert static data to API format
 */
export function transformStaticToAPIProduct(staticProduct: any): Partial<Product> {
  return {
    name: staticProduct.name,
    slug: staticProduct.slug,
    price: staticProduct.price,
    originalPrice: staticProduct.originalPrice,
    images: staticProduct.images || [],
    category: staticProduct.category,
    rating: staticProduct.rating,
    reviewCount: staticProduct.reviews,
    badge: staticProduct.badge,
    description: staticProduct.description,
    shortDescription: staticProduct.longDescription,
    narration: staticProduct.narration,
    luxuryDescription: staticProduct.luxuryDescription,
    budgetDescription: staticProduct.budgetDescription,
    features: staticProduct.features || [],
    luxuryFeatures: staticProduct.luxuryFeatures || [],
    budgetFeatures: staticProduct.budgetFeatures || [],
    specifications: staticProduct.specifications || {},
    inStock: staticProduct.inStock,
    stockQuantity: staticProduct.stockCount,
    shippingInfo: staticProduct.shippingInfo,
    returnPolicy: staticProduct.returnPolicy,
    isFeatured: staticProduct.isFeatured || false,
    isActive: staticProduct.isActive !== false,
    sku: staticProduct.sku,
    weight: typeof staticProduct.weight === 'string' ? 
      parseFloat(staticProduct.weight.replace(/[^\d.]/g, '')) : staticProduct.weight,
    dimensions: staticProduct.dimensions,
    materials: staticProduct.materials || [],
    careInstructions: staticProduct.careInstructions,
    warranty: staticProduct.warranty,
    relatedProductIds: staticProduct.relatedProducts?.map(String) || [],
    tags: staticProduct.tags || []
  };
}

/**
 * Get database status and product count
 */
export async function getDatabaseStatus(): Promise<{
  hasProducts: boolean;
  productCount: number;
  isConnected: boolean;
  error?: string;
}> {
  try {
    const testResult = await testAPIConnection();
    if (!testResult.connected) {
      return {
        hasProducts: false,
        productCount: 0,
        isConnected: false,
        error: testResult.error
      };
    }

    const result = await fetchProducts({}, 1);
    return {
      hasProducts: result.items.length > 0,
      productCount: result.items.length,
      isConnected: true
    };
  } catch (error) {
    return {
      hasProducts: false,
      productCount: 0,
      isConnected: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Custom error class for Product API errors
 */
class ProductAPIError extends Error {
  code?: string;
  details?: any;

  constructor({ message, code, details }: { message: string; code?: string; details?: any }) {
    super(message);
    this.name = 'ProductAPIError';
    this.code = code;
    this.details = details;
  }
}
