/**
 * Image utility functions for handling S3 keys and URLs
 */

/**
 * Determines if a string is an S3 key that needs URL conversion
 */
export const isS3Key = (src: string): boolean => {
  return src.startsWith('products/') || src.includes('products/');
};

/**
 * Determines if a string is a relative path
 */
export const isRelativePath = (src: string): boolean => {
  return src.startsWith('/');
};

/**
 * Determines if a string is a full URL
 */
export const isFullUrl = (src: string): boolean => {
  return src.startsWith('http');
};

/**
 * Extracts S3 key from an S3 URL
 */
export const extractS3Key = (url: string): string | null => {
  try {
    const urlParts = url.split('/');
    const keyIndex = urlParts.findIndex(part => part === 'products');
    if (keyIndex !== -1 && keyIndex < urlParts.length - 1) {
      return urlParts.slice(keyIndex).join('/');
    }
    return null;
  } catch (error) {
    console.error('Error extracting S3 key:', error);
    return null;
  }
};

/**
 * Gets the best fallback image URL
 */
export const getFallbackImage = (): string => {
  return '/placeholder-product.png';
};

/**
 * Processes image source for display - returns the best available option
 * This is a synchronous function for server-side components
 */
export const processImageSrc = (src: string): string => {
  // If it's a relative path, use it as is
  if (isRelativePath(src)) {
    return src;
  }

  // If it's already a full URL, use it (even if it might be expired)
  // The client-side component will handle refreshing if needed
  if (isFullUrl(src)) {
    return src;
  }

  // If it's an S3 key, we can't convert it server-side
  // Return fallback and let client-side component handle it
  if (isS3Key(src)) {
    return getFallbackImage();
  }

  // For any other format, use fallback
  return getFallbackImage();
};

/**
 * Image configuration for different use cases
 */
export const IMAGE_CONFIG = {
  // S3 URL expiry time (24 hours)
  URL_EXPIRY: 86400,
  
  // Fallback image
  FALLBACK: '/placeholder-product.png',
  
  // Loading placeholder
  LOADING_PLACEHOLDER: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkxvYWRpbmcuLi48L3RleHQ+PC9zdmc+'
};
