/**
 * Admin Role Assignment Utility
 * 
 * This utility helps with role management for testing and initial setup.
 * In production, you would typically manage roles through a secure admin interface.
 */

import { generateClient } from 'aws-amplify/api';
import { updateUserProfile } from '../graphql/mutations';
import { getUserProfile, userProfilesByEmail } from '../graphql/queries';

const client = generateClient();

export interface RoleUpdateResult {
  success: boolean;
  message: string;
  data?: any;
}

/**
 * Update a user's role by their email address
 * This is useful for initial admin setup
 */
export async function updateUserRoleByEmail(email: string, newRole: 'admin' | 'user' | 'manager'): Promise<RoleUpdateResult> {
  try {
    console.log(`Attempting to update role for ${email} to ${newRole}`);

    // First, find the user by email
    const result = await client.graphql({
      query: userProfilesByEmail,
      variables: { email }
    });

    const profiles = (result.data as any)?.userProfilesByEmail?.items || [];
    
    if (profiles.length === 0) {
      return {
        success: false,
        message: `No user found with email: ${email}`
      };
    }

    const userProfile = profiles[0];
    console.log(`Found user: ${userProfile.name} (${userProfile.email})`);

    // Update the user's role
    const updateResult = await client.graphql({
      query: updateUserProfile,
      variables: {
        input: {
          id: userProfile.id,
          role: newRole
        }
      }
    });

    const updatedProfile = (updateResult.data as any)?.updateUserProfile;

    return {
      success: true,
      message: `Successfully updated ${userProfile.name}'s role to ${newRole}`,
      data: updatedProfile
    };

  } catch (error) {
    console.error('Error updating user role:', error);
    return {
      success: false,
      message: `Failed to update role: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

/**
 * Update a user's role by their user ID
 */
export async function updateUserRoleById(userId: string, newRole: 'admin' | 'user' | 'manager'): Promise<RoleUpdateResult> {
  try {
    console.log(`Attempting to update role for user ID ${userId} to ${newRole}`);

    // First, get the current user profile
    const result = await client.graphql({
      query: getUserProfile,
      variables: { id: userId }
    });

    const userProfile = (result.data as any)?.getUserProfile;
    
    if (!userProfile) {
      return {
        success: false,
        message: `No user found with ID: ${userId}`
      };
    }

    console.log(`Found user: ${userProfile.name} (${userProfile.email})`);

    // Update the user's role
    const updateResult = await client.graphql({
      query: updateUserProfile,
      variables: {
        input: {
          id: userId,
          role: newRole
        }
      }
    });

    const updatedProfile = (updateResult.data as any)?.updateUserProfile;

    return {
      success: true,
      message: `Successfully updated ${userProfile.name}'s role to ${newRole}`,
      data: updatedProfile
    };

  } catch (error) {
    console.error('Error updating user role:', error);
    return {
      success: false,
      message: `Failed to update role: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

/**
 * Get a user's current role by email
 */
export async function getUserRoleByEmail(email: string): Promise<{ success: boolean; role?: string; message: string }> {
  try {
    const result = await client.graphql({
      query: userProfilesByEmail,
      variables: { email }
    });

    const profiles = (result.data as any)?.userProfilesByEmail?.items || [];
    
    if (profiles.length === 0) {
      return {
        success: false,
        message: `No user found with email: ${email}`
      };
    }

    const userProfile = profiles[0];
    return {
      success: true,
      role: userProfile.role || 'user',
      message: `User ${userProfile.name} has role: ${userProfile.role || 'user'}`
    };

  } catch (error) {
    console.error('Error getting user role:', error);
    return {
      success: false,
      message: `Failed to get role: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

/**
 * List all users with their roles (for admin use)
 */
export async function listAllUsersWithRoles(): Promise<{ success: boolean; users?: any[]; message: string }> {
  try {
    // Note: This would need a custom query to list all users
    // For now, this is a placeholder that would need to be implemented
    // based on your specific requirements
    
    return {
      success: false,
      message: 'This function needs to be implemented with a custom GraphQL query'
    };

  } catch (error) {
    console.error('Error listing users:', error);
    return {
      success: false,
      message: `Failed to list users: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

// Example usage functions for testing
export const adminRoleExamples = {
  /**
   * Make the first user an admin (useful for initial setup)
   */
  async makeFirstUserAdmin(email: string) {
    console.log('🔧 Making first user admin...');
    const result = await updateUserRoleByEmail(email, 'admin');
    console.log(result.success ? '✅' : '❌', result.message);
    return result;
  },

  /**
   * Check current user role
   */
  async checkUserRole(email: string) {
    console.log('🔍 Checking user role...');
    const result = await getUserRoleByEmail(email);
    console.log(result.success ? '✅' : '❌', result.message);
    return result;
  },

  /**
   * Reset user to regular user role
   */
  async resetToUser(email: string) {
    console.log('🔄 Resetting user to regular user role...');
    const result = await updateUserRoleByEmail(email, 'user');
    console.log(result.success ? '✅' : '❌', result.message);
    return result;
  }
};

// Export for console testing
if (typeof window !== 'undefined') {
  (window as any).adminRoleHelper = {
    updateUserRoleByEmail,
    updateUserRoleById,
    getUserRoleByEmail,
    examples: adminRoleExamples
  };
  
  console.log('🔧 Admin Role Helper loaded! Use window.adminRoleHelper in console for testing.');
  console.log('Examples:');
  console.log('- window.adminRoleHelper.examples.makeFirstUserAdmin("<EMAIL>")');
  console.log('- window.adminRoleHelper.examples.checkUserRole("<EMAIL>")');
  console.log('- window.adminRoleHelper.examples.resetToUser("<EMAIL>")');
}
