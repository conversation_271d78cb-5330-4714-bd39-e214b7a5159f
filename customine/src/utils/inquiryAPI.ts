/**
 * Inquiry and Contact Form API
 * Handles all inquiry types including contact forms, special occasions, corporate inquiries
 */

import {
  inquiriesByUserId,
  inquiriesByType,
  getInquiry,
  listInquiries,
  newslettersByEmail,
  inquiryResponsesByInquiryId
} from '../graphql/queries';
import {
  createInquiry,
  updateInquiry,
  createInquiryResponse,
  createNewsletter,
  updateNewsletter
} from '../graphql/mutations';

export interface InquiryData {
  userId?: string;
  type: 'GENERAL_INQUIRY' | 'PRODUCT_INQUIRY' | 'ORDER_INQUIRY' | 'SPECIAL_OCCASION' | 'CORPORATE_INQUIRY' | 'BULK_ORDER' | 'CUSTOM_GIFT_BOX' | 'COMPLAINT' | 'SUGGESTION' | 'PARTNERSHIP' | 'MEDIA_INQUIRY' | 'NEWSLETTER_SIGNUP';
  name: string;
  email: string;
  phone?: string;
  company?: string;
  subject: string;
  message: string;
  category?: string;
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  
  // Event/Occasion specific fields
  eventType?: 'wedding' | 'corporate' | 'birthday' | 'anniversary' | 'festival' | 'other';
  eventDate?: string;
  guestCount?: number;
  budget?: string;
  location?: string;
  
  // References
  attachments?: string[];
  relatedOrderId?: string;
  relatedProductId?: string;
}

export interface NewsletterData {
  email: string;
  name?: string;
  phone?: string;
  preferences?: Record<string, unknown>;
  source?: string;
  tags?: string[];
}

/**
 * Inquiry API for contact forms and customer inquiries
 */
export const inquiryAPI = {
  /**
   * Create a new inquiry/contact form submission
   */
  create: async (inquiryData: InquiryData) => {
    try {
      const { generateClient } = await import('aws-amplify/api');
      const client = generateClient();

      // Get current user ID if not provided and user is authenticated
      let userId = inquiryData.userId;
      if (!userId) {
        try {
          const { getCurrentUser } = await import('aws-amplify/auth');
          const currentUser = await getCurrentUser();
          userId = currentUser.userId;
        } catch (authError) {
          // User not authenticated - inquiry will be anonymous
          console.log('User not authenticated, creating anonymous inquiry');
        }
      }

      const result = await client.graphql({
        query: createInquiry,
        variables: {
          input: {
            ...inquiryData,
            userId, // Include userId if available
            status: 'NEW',
            isRead: false,
            isReplied: false
          }
        }
      });

      return {
        success: true,
        data: (result.data as any)?.createInquiry,
        message: 'Your inquiry has been submitted successfully. We will get back to you soon.'
      };
    } catch (error: unknown) {
      console.error('Error creating inquiry:', error);
      return {
        success: false,
        error: 'Failed to submit inquiry. Please try again.'
      };
    }
  },

  /**
   * Create inquiry for authenticated user (ensures userId is included)
   */
  createForUser: async (inquiryData: Omit<InquiryData, 'userId'>, userId: string) => {
    return await inquiryAPI.create({
      ...inquiryData,
      userId
    });
  },

  /**
   * Get user's inquiries
   */
  getUserInquiries: async (userId: string) => {
    try {
      const { generateClient } = await import('aws-amplify/api');
      const client = generateClient();
      
      const result = await client.graphql({
        query: inquiriesByUserId,
        variables: { userId }
      });

      return {
        success: true,
        data: (result.data as any)?.inquiriesByUserId?.items || []
      };
    } catch (error: unknown) {
      console.error('Error fetching user inquiries:', error);
      return {
        success: false,
        error: 'Failed to fetch inquiries'
      };
    }
  },

  /**
   * Get inquiries by type (for admin)
   */
  getByType: async (type: InquiryData['type'], limit = 50) => {
    try {
      const { generateClient } = await import('aws-amplify/api');
      const client = generateClient();
      
      const result = await client.graphql({
        query: inquiriesByType,
        variables: { type, limit }
      });

      return {
        success: true,
        data: (result.data as any)?.inquiriesByType?.items || []
      };
    } catch (error: unknown) {
      console.error('Error fetching inquiries by type:', error);
      return {
        success: false,
        error: 'Failed to fetch inquiries'
      };
    }
  },

  /**
   * Get inquiry by ID with responses
   */
  getById: async (id: string) => {
    try {
      const { generateClient } = await import('aws-amplify/api');
      const client = generateClient();
      
      const result = await client.graphql({
        query: getInquiry,
        variables: { id }
      });

      return {
        success: true,
        data: (result.data as any)?.getInquiry
      };
    } catch (error: unknown) {
      console.error('Error fetching inquiry:', error);
      return {
        success: false,
        error: 'Failed to fetch inquiry'
      };
    }
  },

  /**
   * Update inquiry status (admin)
   */
  updateStatus: async (id: string, status: 'NEW' | 'OPEN' | 'IN_PROGRESS' | 'WAITING_FOR_CUSTOMER' | 'RESOLVED' | 'CLOSED' | 'ESCALATED', adminNotes?: string, assignedTo?: string) => {
    try {
      const { generateClient } = await import('aws-amplify/api');
      const client = generateClient();
      
      const result = await client.graphql({
        query: updateInquiry,
        variables: {
          input: {
            id,
            status,
            ...(adminNotes && { adminNotes }),
            ...(assignedTo && { assignedTo })
          }
        }
      });

      return {
        success: true,
        data: (result.data as any)?.updateInquiry
      };
    } catch (error: unknown) {
      console.error('Error updating inquiry status:', error);
      return {
        success: false,
        error: 'Failed to update inquiry status'
      };
    }
  },

  /**
   * Add response to inquiry
   */
  addResponse: async (inquiryId: string, response: {
    responderId: string;
    responderName: string;
    responderEmail: string;
    message: string;
    isInternal?: boolean;
    attachments?: string[];
  }) => {
    try {
      const { generateClient } = await import('aws-amplify/api');
      const client = generateClient();
      
      const result = await client.graphql({
        query: createInquiryResponse,
        variables: {
          input: {
            inquiryId,
            ...response,
            isInternal: response.isInternal || false
          }
        }
      });

      // Mark inquiry as replied
      await client.graphql({
        query: updateInquiry,
        variables: {
          input: {
            id: inquiryId,
            status: 'IN_PROGRESS',
            adminNotes: `Response added by ${response.responderName}`,
            isReplied: true
          }
        }
      });

      return {
        success: true,
        data: (result.data as any)?.createInquiryResponse
      };
    } catch (error: unknown) {
      console.error('Error adding inquiry response:', error);
      return {
        success: false,
        error: 'Failed to add response'
      };
    }
  },

  /**
   * Mark inquiry as read
   */
  markAsRead: async (id: string) => {
    try {
      const { generateClient } = await import('aws-amplify/api');
      const client = generateClient();

      const result = await client.graphql({
        query: updateInquiry,
        variables: {
          input: {
            id,
            isRead: true
          }
        }
      });

      return {
        success: true,
        data: (result.data as any)?.updateInquiry
      };
    } catch (error: unknown) {
      console.error('Error marking inquiry as read:', error);
      return {
        success: false,
        error: 'Failed to mark as read'
      };
    }
  },

  /**
   * Get all inquiries (admin only)
   */
  getAllInquiries: async (limit: number = 100) => {
    try {
      const { generateClient } = await import('aws-amplify/api');
      const client = generateClient();

      const result = await client.graphql({
        query: listInquiries,
        variables: {
          limit,
          // Sort by creation date, newest first
          sortDirection: 'DESC'
        }
      });

      return {
        success: true,
        data: (result.data as any)?.listInquiries?.items || []
      };
    } catch (error: unknown) {
      console.error('Error fetching all inquiries:', error);
      return {
        success: false,
        error: 'Failed to fetch inquiries'
      };
    }
  },

  /**
   * Get inquiry responses
   */
  getInquiryResponses: async (inquiryId: string) => {
    try {
      const { generateClient } = await import('aws-amplify/api');
      const client = generateClient();

      const result = await client.graphql({
        query: inquiryResponsesByInquiryId,
        variables: { inquiryId }
      });

      return {
        success: true,
        data: (result.data as any)?.inquiryResponsesByInquiryId?.items || []
      };
    } catch (error: unknown) {
      console.error('Error fetching inquiry responses:', error);
      return {
        success: false,
        error: 'Failed to fetch responses'
      };
    }
  },

  /**
   * Get inquiries with role-based filtering
   */
  getInquiriesForUser: async (userId: string, isAdmin: boolean = false, limit: number = 100) => {
    try {
      if (isAdmin) {
        // Admin can see all inquiries
        return await inquiryAPI.getAllInquiries(limit);
      } else {
        // Regular users can only see their own inquiries
        return await inquiryAPI.getUserInquiries(userId);
      }
    } catch (error: unknown) {
      console.error('Error fetching inquiries for user:', error);
      return {
        success: false,
        error: 'Failed to fetch inquiries'
      };
    }
  },

  /**
   * Update inquiry status
   */
  updateStatus: async (id: string, status: string) => {
    try {
      const { generateClient } = await import('aws-amplify/api');
      const client = generateClient();

      const result = await client.graphql({
        query: updateInquiry,
        variables: {
          input: {
            id,
            status,
            isReplied: status === 'RESPONDED' || status === 'CLOSED'
          }
        }
      });

      return {
        success: true,
        data: (result.data as any)?.updateInquiry
      };
    } catch (error: unknown) {
      console.error('Error updating inquiry status:', error);
      return {
        success: false,
        error: 'Failed to update status'
      };
    }
  },

  /**
   * Update inquiry (admin function)
   */
  updateInquiry: async (id: string, updates: {
    status?: string;
    priority?: string;
    adminNotes?: string;
    internalNotes?: string;
    assignedTo?: string;
    isRead?: boolean;
    isReplied?: boolean;
  }) => {
    try {
      const { generateClient } = await import('aws-amplify/api');
      const client = generateClient();

      const result = await client.graphql({
        query: updateInquiry,
        variables: {
          input: {
            id,
            ...updates,
            // Auto-set isReplied if status indicates response
            isReplied: updates.status === 'RESPONDED' || updates.status === 'CLOSED' || updates.isReplied
          }
        }
      });

      return {
        success: true,
        data: (result.data as any)?.updateInquiry
      };
    } catch (error: unknown) {
      console.error('Error updating inquiry:', error);
      return {
        success: false,
        error: 'Failed to update inquiry'
      };
    }
  },

  /**
   * Get inquiry details with responses
   */
  getInquiryWithResponses: async (id: string) => {
    try {
      const { generateClient } = await import('aws-amplify/api');
      const client = generateClient();

      // Get inquiry details
      const inquiryResult = await client.graphql({
        query: getInquiry,
        variables: { id }
      });

      const inquiry = (inquiryResult.data as any)?.getInquiry;
      if (!inquiry) {
        return {
          success: false,
          error: 'Inquiry not found'
        };
      }

      // Get responses
      const responsesResult = await inquiryAPI.getInquiryResponses(id);

      return {
        success: true,
        data: {
          ...inquiry,
          responses: responsesResult.success ? responsesResult.data : []
        }
      };
    } catch (error: unknown) {
      console.error('Error fetching inquiry with responses:', error);
      return {
        success: false,
        error: 'Failed to fetch inquiry details'
      };
    }
  }
};

/**
 * Newsletter API for email subscriptions
 */
export const newsletterAPI = {
  /**
   * Subscribe to newsletter
   */
  subscribe: async (data: NewsletterData) => {
    try {
      // Check if email already exists
      const existingResult = await newsletterAPI.checkSubscription(data.email);
      
      if (existingResult.success && existingResult.data?.isSubscribed) {
        return {
          success: true,
          message: 'You are already subscribed to our newsletter.',
          data: existingResult.data
        };
      }

      const { generateClient } = await import('aws-amplify/api');
      const client = generateClient();
      
      const result = await client.graphql({
        query: createNewsletter,
        variables: {
          input: {
            ...data,
            isActive: true,
            source: data.source || 'website'
          }
        }
      });

      return {
        success: true,
        data: (result.data as any)?.createNewsletter,
        message: 'Successfully subscribed to our newsletter!'
      };
    } catch (error: unknown) {
      console.error('Error subscribing to newsletter:', error);
      return {
        success: false,
        error: 'Failed to subscribe. Please try again.'
      };
    }
  },

  /**
   * Update newsletter subscription
   */
  updateSubscription: async (id: string, updates: Partial<NewsletterData>) => {
    try {
      const { generateClient } = await import('aws-amplify/api');
      const client = generateClient();
      
      const result = await client.graphql({
        query: updateNewsletter,
        variables: {
          input: { id, ...updates }
        }
      });

      return {
        success: true,
        data: (result.data as any)?.updateNewsletter
      };
    } catch (error: unknown) {
      console.error('Error updating newsletter subscription:', error);
      return {
        success: false,
        error: 'Failed to update subscription'
      };
    }
  },

  /**
   * Unsubscribe from newsletter
   */
  unsubscribe: async (id: string) => {
    try {
      const { generateClient } = await import('aws-amplify/api');
      const client = generateClient();
      
      const result = await client.graphql({
        query: updateNewsletter,
        variables: {
          input: {
            id,
            isActive: false
          }
        }
      });

      return {
        success: true,
        data: (result.data as any)?.updateNewsletter,
        message: 'Successfully unsubscribed from newsletter.'
      };
    } catch (error: unknown) {
      console.error('Error unsubscribing from newsletter:', error);
      return {
        success: false,
        error: 'Failed to unsubscribe'
      };
    }
  },

  /**
   * Check if email is subscribed
   */
  checkSubscription: async (email: string) => {
    try {
      const { generateClient } = await import('aws-amplify/api');
      const client = generateClient();
      
      const result = await client.graphql({
        query: newslettersByEmail,
        variables: { email }
      });

      const subscriptions = (result.data as any)?.newslettersByEmail?.items || [];
      const activeSubscription = subscriptions.find((sub: any) => sub.isActive);

      return {
        success: true,
        data: {
          isSubscribed: !!activeSubscription,
          subscription: activeSubscription
        }
      };
    } catch (error: unknown) {
      console.error('Error checking subscription:', error);
      return {
        success: false,
        error: 'Failed to check subscription'
      };
    }
  }
};

// Export for console usage
if (typeof window !== 'undefined') {
  (window as any).inquiryAPI = inquiryAPI;

  // Helper function for testing - create a sample inquiry
  (window as any).createTestInquiry = async (userEmail?: string, userName?: string) => {
    // Get current user info if not provided
    let currentUserId: string | undefined;
    let currentUserEmail = userEmail;
    let currentUserName = userName;

    try {
      const { getCurrentUser } = await import('aws-amplify/auth');
      const currentUser = await getCurrentUser();
      currentUserId = currentUser.userId;

      if (!currentUserEmail || !currentUserName) {
        // Try to get user profile info
        try {
          const { fetchUserAttributes } = await import('aws-amplify/auth');
          const attributes = await fetchUserAttributes();
          currentUserEmail = currentUserEmail || attributes.email || '<EMAIL>';
          currentUserName = currentUserName || attributes.name || 'Test User';
        } catch (profileError) {
          currentUserEmail = currentUserEmail || '<EMAIL>';
          currentUserName = currentUserName || 'Test User';
        }
      }
    } catch (authError) {
      console.log('User not authenticated, creating anonymous test inquiry');
      currentUserEmail = currentUserEmail || '<EMAIL>';
      currentUserName = currentUserName || 'Test User';
    }

    const testInquiry = {
      type: 'CONTACT' as const,
      name: currentUserName,
      email: currentUserEmail,
      subject: 'Test Inquiry - Custom Gift Box',
      message: 'I would like to inquire about custom gift boxes for a wedding event. Can you provide more details about pricing and customization options?',
      category: 'product',
      priority: 'medium' as const,
      userId: currentUserId // This will be included if user is authenticated
    };

    try {
      const result = await inquiryAPI.create(testInquiry);
      console.log('✅ Test inquiry created:', result);
      console.log('📧 Email:', currentUserEmail);
      console.log('👤 Name:', currentUserName);
      console.log('🆔 User ID:', currentUserId || 'Anonymous');
      return result;
    } catch (error) {
      console.error('❌ Failed to create test inquiry:', error);
      return { success: false, error };
    }
  };

  // Helper function to fix existing inquiries without userId
  (window as any).fixInquiriesWithoutUserId = async () => {
    try {
      const { getCurrentUser } = await import('aws-amplify/auth');
      const currentUser = await getCurrentUser();
      const userId = currentUser.userId;

      // Get user's email to match inquiries
      const { fetchUserAttributes } = await import('aws-amplify/auth');
      const attributes = await fetchUserAttributes();
      const userEmail = attributes.email;

      if (!userEmail) {
        console.error('❌ Cannot fix inquiries: User email not found');
        return;
      }

      // Get all inquiries for this user's email that don't have userId
      const allInquiries = await inquiryAPI.getAllInquiries(1000);
      if (!allInquiries.success) {
        console.error('❌ Failed to fetch inquiries:', allInquiries.error);
        return;
      }

      const inquiriesToFix = allInquiries.data.filter((inquiry: any) =>
        inquiry.email === userEmail && !inquiry.userId
      );

      console.log(`🔍 Found ${inquiriesToFix.length} inquiries to fix for email: ${userEmail}`);

      let fixedCount = 0;
      for (const inquiry of inquiriesToFix) {
        try {
          const { generateClient } = await import('aws-amplify/api');
          const client = generateClient();

          await client.graphql({
            query: updateInquiry,
            variables: {
              input: {
                id: inquiry.id,
                userId: userId
              }
            }
          });

          fixedCount++;
          console.log(`✅ Fixed inquiry ${inquiry.id} - ${inquiry.subject}`);
        } catch (error) {
          console.error(`❌ Failed to fix inquiry ${inquiry.id}:`, error);
        }
      }

      console.log(`🎉 Successfully fixed ${fixedCount} out of ${inquiriesToFix.length} inquiries`);
      return { fixed: fixedCount, total: inquiriesToFix.length };

    } catch (error) {
      console.error('❌ Error fixing inquiries:', error);
      return { error };
    }
  };

  console.log('🔧 Inquiry API loaded! Available functions:');
  console.log('- window.inquiryAPI - Full API access');
  console.log('- window.createTestInquiry() - Create test inquiry for current user');
  console.log('- window.createTestInquiry("email", "name") - Create test inquiry with custom details');
  console.log('- window.fixInquiriesWithoutUserId() - Fix existing inquiries missing userId');
  console.log('💡 Note: Inquiries now automatically include userId for authenticated users!');
}
