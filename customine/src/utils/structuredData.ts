// Structured Data Generators for SEO

export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  image: string;
  category: string;
  availability: 'InStock' | 'OutOfStock' | 'PreOrder';
  brand?: string;
  sku?: string;
}

export function generateProductStructuredData(product: Product) {
  return {
    "@context": "https://schema.org",
    "@type": "Product",
    "name": product.name,
    "description": product.description,
    "image": product.image,
    "brand": {
      "@type": "Brand",
      "name": product.brand || "Customine"
    },
    "sku": product.sku || product.id,
    "category": product.category,
    "offers": {
      "@type": "Offer",
      "price": product.price,
      "priceCurrency": "INR",
      "availability": `https://schema.org/${product.availability}`,
      "seller": {
        "@type": "Organization",
        "name": "Customine"
      },
      "areaServed": "IN",
      "shippingDetails": {
        "@type": "OfferShippingDetails",
        "shippingRate": {
          "@type": "MonetaryAmount",
          "value": "0",
          "currency": "INR"
        },
        "deliveryTime": {
          "@type": "ShippingDeliveryTime",
          "businessDays": {
            "@type": "OpeningHoursSpecification",
            "dayOfWeek": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"],
            "opens": "09:00",
            "closes": "18:00"
          }
        }
      }
    },
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "reviewCount": "127"
    }
  };
}

export function generateOrganizationStructuredData() {
  return {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Customine",
    "url": "https://customine.in",
    "logo": "https://customine.in/logo.png",
    "description": "Thoughtfully designed gift boxes for weddings, corporate events, client appreciation, festive celebrations, and personal milestones in India.",
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "IN"
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "availableLanguage": ["English", "Hindi"],
      "areaServed": "IN"
    },
    "sameAs": [
      "https://www.facebook.com/customine",
      "https://www.instagram.com/customine",
      "https://www.pinterest.com/customine/"
    ],
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Gift Boxes",
      "itemListElement": [
        {
          "@type": "OfferCatalog",
          "name": "Corporate Gift Boxes",
          "description": "Professional gift boxes for corporate events and client appreciation"
        },
        {
          "@type": "OfferCatalog", 
          "name": "Wedding Gift Boxes",
          "description": "Elegant gift boxes for weddings and special occasions"
        },
        {
          "@type": "OfferCatalog",
          "name": "Festive Gift Boxes", 
          "description": "Curated gift boxes for festivals and celebrations"
        }
      ]
    }
  };
}

export function generateBreadcrumbStructuredData(items: Array<{name: string, url: string}>) {
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": items.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.name,
      "item": item.url
    }))
  };
}

export function generateWebsiteStructuredData() {
  return {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "Customine",
    "url": "https://customine.in",
    "description": "Curated & Custom Gift Boxes in India",
    "potentialAction": {
      "@type": "SearchAction",
      "target": "https://customine.in/search?q={search_term_string}",
      "query-input": "required name=search_term_string"
    },
    "publisher": {
      "@type": "Organization",
      "name": "Customine",
      "logo": {
        "@type": "ImageObject",
        "url": "https://customine.in/logo.png"
      }
    }
  };
}

export function generateLocalBusinessStructuredData() {
  return {
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    "name": "Customine",
    "description": "Curated & Custom Gift Boxes in India",
    "url": "https://customine.in",
    "telephone": "+91-XXXXXXXXXX", // Add actual phone number
    "email": "<EMAIL>", // Add actual email
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "IN"
    },
    "geo": {
      "@type": "GeoCoordinates",
      "latitude": "20.5937",
      "longitude": "78.9629"
    },
    "areaServed": {
      "@type": "Country",
      "name": "India"
    },
    "serviceType": "Gift Box Curation and Delivery",
    "priceRange": "₹₹",
    "paymentAccepted": ["Cash", "Credit Card", "UPI", "Net Banking"],
    "currenciesAccepted": "INR"
  };
}
