/**
 * Authentication and User Profile API
 * Handles Cognito authentication and user profile management
 */

import { formatPhoneForCognito, validatePhoneNumber } from './phoneUtils';
import {
  getUserProfile,
  userProfilesByEmail
} from '../graphql/queries';
import {
  createUserProfile,
  updateUserProfile
} from '../graphql/mutations';

export interface UserProfile {
  id: string;
  email: string;
  name: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  alternatePhone?: string;

  // Address Information
  address?: string;
  addressLine2?: string;
  city?: string;
  state?: string;
  pincode?: string;
  country?: string;
  landmark?: string;

  // Personal Information
  dateOfBirth?: string;
  gender?: string;
  maritalStatus?: string;
  anniversary?: string;
  occupation?: string;
  company?: string;

  // Profile Details
  avatar?: string;
  bio?: string;
  website?: string;
  socialMedia?: Record<string, string>;
  preferences?: Record<string, unknown>;
  communicationPreferences?: Record<string, boolean>;
  language?: string;
  currency?: string;
  timezone?: string;

  // Business Information
  businessName?: string;
  businessType?: string;
  gstNumber?: string;
  panNumber?: string;
  businessAddress?: string;
  businessPhone?: string;
  businessEmail?: string;

  // Admin and System Fields
  tags?: string[];
  notes?: string;
  customerType?: string;
  loyaltyPoints?: number;
  totalSpent?: number;
  lastOrderDate?: string;
  isActive: boolean;
  isVerified?: boolean;
  isVIP?: boolean;
  allowMarketing?: boolean;
  role?: string; // 'admin', 'user', 'manager', etc.
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface SignUpData {
  name: string;
  email: string;
  password: string;
  phone?: string;
}

export interface SignInData {
  email: string;
  password: string;
}

/**
 * Authentication API using AWS Cognito
 */
export const authAPI = {
  /**
   * Sign up a new user with Cognito and create profile
   */
  signUp: async (userData: SignUpData) => {
    try {
      // Validate phone number if provided
      if (userData.phone) {
        const phoneValidation = validatePhoneNumber(userData.phone);
        if (!phoneValidation.isValid) {
          return {
            success: false,
            error: phoneValidation.error || 'Invalid phone number format'
          };
        }
      }

      // Step 1: Create Cognito user
      const { signUp } = await import('aws-amplify/auth');

      // Format phone number for Cognito (E.164 format)
      const formattedPhone = userData.phone ? formatPhoneForCognito(userData.phone) : undefined;

      const { user, nextStep } = await signUp({
        username: userData.email,
        password: userData.password,
        options: {
          userAttributes: {
            email: userData.email,
            name: userData.name,
            ...(formattedPhone && { phone_number: formattedPhone })
          }
        }
      });

      // Note: User profile will be created after email verification

      return {
        success: true,
        data: { user, nextStep },
        message: 'Registration successful. Please check your email for verification.'
      };
    } catch (error: unknown) {
      return {
        success: false,
        error: (error as Error).message || 'Registration failed'
      };
    }
  },

  /**
   * Confirm user registration with verification code and create user profile
   */
  confirmSignUp: async (email: string, code: string) => {
    try {
      const { confirmSignUp, fetchUserAttributes } = await import('aws-amplify/auth');
      const { isSignUpComplete } = await confirmSignUp({
        username: email,
        confirmationCode: code
      });

      if (isSignUpComplete) {
        // Auto-create user profile after successful email verification
        try {
          // Get user attributes from Cognito
          const userAttributes = await fetchUserAttributes();

          // Create user profile with default values
          await userProfileAPI.createUserProfile({
            id: userAttributes.sub || '',
            email: userAttributes.email || email,
            name: userAttributes.name || '',
            phone: userAttributes.phone_number || '',
            isActive: true,
            isVerified: true, // Email is verified
            isVIP: false,     // Default to false as requested
            allowMarketing: true,
            country: 'India',
            currency: 'INR',
            language: 'English',
            timezone: 'Asia/Kolkata',
            customerType: 'Individual',
            role: 'user' // Default role for new users
          });

          console.log('User profile created successfully for:', email);
        } catch (profileError) {
          console.error('Failed to create user profile:', profileError);
          // Don't fail the signup confirmation if profile creation fails
          // The user can still use the system, profile can be created later
        }
      }

      return {
        success: true,
        data: { isSignUpComplete },
        message: 'Email verified successfully'
      };
    } catch (error: unknown) {
      return {
        success: false,
        error: (error as Error).message || 'Verification failed'
      };
    }
  },

  /**
   * Sign in user
   */
  signIn: async (credentials: SignInData) => {
    try {
      const { signIn, fetchUserAttributes } = await import('aws-amplify/auth');
      const { isSignedIn, nextStep } = await signIn({
        username: credentials.email,
        password: credentials.password
      });

      if (isSignedIn) {
        // Get user attributes
        const userAttributes = await fetchUserAttributes();

        // Check if user profile exists, create if not
        try {
          const existingProfile = await userProfileAPI.getByEmail(userAttributes.email || credentials.email);

          if (!existingProfile) {
            // Create profile for existing user who doesn't have one
            await userProfileAPI.createUserProfile({
              id: userAttributes.sub || '',
              email: userAttributes.email || credentials.email,
              name: userAttributes.name || '',
              phone: userAttributes.phone_number || '',
              isActive: true,
              isVerified: true, // Assume verified if they can sign in
              isVIP: false,
              allowMarketing: true,
              country: 'India',
              currency: 'INR',
              language: 'English',
              timezone: 'Asia/Kolkata',
              customerType: 'Individual',
              role: 'user' // Default role for existing users
            });

            console.log('User profile created for existing user:', userAttributes.email);
          }
        } catch (profileError) {
          console.error('Failed to check/create user profile on sign-in:', profileError);
          // Don't fail the sign-in if profile creation fails
        }

        return {
          success: true,
          data: {
            user: {
              id: userAttributes.sub || '',
              email: userAttributes.email || credentials.email,
              name: userAttributes.name || '',
              phone: userAttributes.phone_number || ''
            }
          },
          message: 'Login successful'
        };
      } else {
        return {
          success: false,
          error: 'Additional steps required',
          nextStep
        };
      }
    } catch (error: unknown) {
      return {
        success: false,
        error: (error as Error).message || 'Login failed'
      };
    }
  },

  /**
   * Sign out user
   */
  signOut: async () => {
    try {
      const { signOut } = await import('aws-amplify/auth');
      await signOut();
      return {
        success: true,
        message: 'Logout successful'
      };
    } catch (error: unknown) {
      return {
        success: false,
        error: (error as Error).message || 'Logout failed'
      };
    }
  },

  /**
   * Get current authenticated user
   */
  getCurrentUser: async () => {
    try {
      const { getCurrentUser, fetchUserAttributes } = await import('aws-amplify/auth');
      const user = await getCurrentUser();
      const userAttributes = await fetchUserAttributes();
      
      // Get user profile from database
      let profile = await userProfileAPI.getByEmail(userAttributes.email || '');

      // Create profile if it doesn't exist
      if (!profile) {
        try {
          const createResult = await userProfileAPI.createUserProfile({
            id: user.username,
            email: userAttributes.email || '',
            name: userAttributes.name || '',
            phone: userAttributes.phone_number || '',
            isActive: true,
            isVerified: true,
            isVIP: false,
            allowMarketing: true,
            country: 'India',
            currency: 'INR',
            language: 'English',
            timezone: 'Asia/Kolkata',
            customerType: 'Individual',
            role: 'user' // Default role for current users
          });

          if (createResult.success) {
            profile = createResult.data;
            console.log('User profile created for current user:', userAttributes.email);
          }
        } catch (profileError) {
          console.error('Failed to create user profile for current user:', profileError);
        }
      }

      return {
        success: true,
        data: {
          user: {
            id: user.username,
            email: userAttributes.email || '',
            name: userAttributes.name || profile?.name || '',
            phone: userAttributes.phone_number || profile?.phone,
            profile: profile
          }
        }
      };
    } catch (error: unknown) {
      return {
        success: false,
        error: (error as Error).message || 'Not authenticated'
      };
    }
  },

  /**
   * Check if user is authenticated
   */
  isAuthenticated: async () => {
    try {
      const { getCurrentUser } = await import('aws-amplify/auth');
      await getCurrentUser();
      return {
        success: true,
        data: { isAuthenticated: true }
      };
    } catch (error) {
      return {
        success: true,
        data: { isAuthenticated: false }
      };
    }
  },

  /**
   * Resend confirmation code
   */
  resendConfirmationCode: async (email: string) => {
    try {
      const { resendSignUpCode } = await import('aws-amplify/auth');
      await resendSignUpCode({ username: email });
      return {
        success: true,
        message: 'Confirmation code sent'
      };
    } catch (error: unknown) {
      return {
        success: false,
        error: (error as Error).message || 'Failed to resend code'
      };
    }
  },

  /**
   * Reset password
   */
  resetPassword: async (email: string) => {
    try {
      const { resetPassword } = await import('aws-amplify/auth');
      const { nextStep } = await resetPassword({ username: email });
      return {
        success: true,
        data: { nextStep },
        message: 'Password reset code sent'
      };
    } catch (error: unknown) {
      return {
        success: false,
        error: (error as Error).message || 'Failed to reset password'
      };
    }
  },

  /**
   * Confirm password reset
   */
  confirmResetPassword: async (email: string, code: string, newPassword: string) => {
    try {
      const { confirmResetPassword } = await import('aws-amplify/auth');
      await confirmResetPassword({
        username: email,
        confirmationCode: code,
        newPassword
      });
      return {
        success: true,
        message: 'Password reset successful'
      };
    } catch (error: unknown) {
      return {
        success: false,
        error: (error as Error).message || 'Failed to confirm password reset'
      };
    }
  }
};

/**
 * User Profile API for database operations
 */
export const userProfileAPI = {
  /**
   * Get user profile by ID
   */
  getById: async (id: string): Promise<UserProfile | null> => {
    try {
      const { generateClient } = await import('aws-amplify/api');
      const client = generateClient();
      const result = await client.graphql({
        query: getUserProfile,
        variables: { id }
      });
      
      return (result.data as any)?.getUserProfile || null;
    } catch (error) {
      console.error('Error fetching user profile:', error);
      return null;
    }
  },

  /**
   * Get user profile by email
   */
  getByEmail: async (email: string): Promise<UserProfile | null> => {
    try {
      const { generateClient } = await import('aws-amplify/api');
      const client = generateClient();
      const result = await client.graphql({
        query: userProfilesByEmail,
        variables: { email }
      });
      
      const profiles = (result.data as any)?.userProfilesByEmail?.items || [];
      return profiles.length > 0 ? profiles[0] : null;
    } catch (error) {
      console.error('Error fetching user profile by email:', error);
      return null;
    }
  },

  /**
   * Update user profile
   */
  update: async (id: string, updates: Partial<UserProfile>) => {
    try {
      const { generateClient } = await import('aws-amplify/api');
      const client = generateClient();
      const result = await client.graphql({
        query: updateUserProfile,
        variables: {
          input: { id, ...updates }
        }
      });

      return {
        success: true,
        data: (result.data as any)?.updateUserProfile
      };
    } catch (error) {
      console.error('Error updating user profile:', error);
      return {
        success: false,
        error: 'Failed to update profile'
      };
    }
  },

  /**
   * Create user profile
   */
  createUserProfile: async (profileData: Partial<UserProfile> & { id: string; email: string; name: string }) => {
    try {
      const { generateClient } = await import('aws-amplify/api');
      const client = generateClient();

      // Prepare profile data with defaults
      const profileInput = {
        id: profileData.id,
        email: profileData.email,
        name: profileData.name,
        phone: profileData.phone || '',
        isActive: profileData.isActive !== undefined ? profileData.isActive : true,
        isVerified: profileData.isVerified !== undefined ? profileData.isVerified : false,
        isVIP: profileData.isVIP !== undefined ? profileData.isVIP : false,
        allowMarketing: profileData.allowMarketing !== undefined ? profileData.allowMarketing : true,
        country: profileData.country || 'India',
        currency: profileData.currency || 'INR',
        language: profileData.language || 'English',
        timezone: profileData.timezone || 'Asia/Kolkata',
        customerType: profileData.customerType || 'Individual',
        ...profileData
      };

      const result = await client.graphql({
        query: createUserProfile,
        variables: {
          input: profileInput
        }
      });

      return {
        success: true,
        data: (result.data as any)?.createUserProfile
      };
    } catch (error) {
      console.error('Error creating user profile:', error);
      return {
        success: false,
        error: 'Failed to create profile'
      };
    }
  }
};
