/**
 * Order Management API
 * Handles order creation, tracking, and management
 */

import {
  ordersByUserId,
  ordersByOrderNumber,
  orderItemsByOrderId,
  getProduct,
  listOrders
} from '../graphql/queries';
import {
  createOrder,
  updateOrder,
  createOrderItem
} from '../graphql/mutations';

export interface ShippingAddress {
  name: string;
  phone: string;
  email?: string;
  addressLine1: string;
  addressLine2?: string;
  landmark?: string;
  city: string;
  state: string;
  pincode: string;
  country: string;
  addressType?: 'home' | 'office' | 'other';
}

export interface OrderItem {
  productId: string;
  quantity: number;
  price: number;
  giftType?: string;
  customization?: Record<string, unknown>;
  notes?: string;
}

export interface OrderData {
  userId: string;
  items: OrderItem[];
  shippingAddress: ShippingAddress;
  billingAddress?: ShippingAddress;
  paymentMethod: string;
  customerNotes?: string;
  giftMessage?: string;
  specialInstructions?: string;
  couponCode?: string;
}

export interface OrderCalculation {
  subtotal: number;
  tax: number;
  shipping: number;
  discount: number;
  total: number;
  currency: string;
}

/**
 * Order API for e-commerce functionality
 */
export const orderAPI = {
  /**
   * Calculate order totals
   */
  calculateTotals: (items: OrderItem[], shippingAddress: ShippingAddress, couponCode?: string): OrderCalculation => {
    const subtotal = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    
    // Tax calculation (18% GST for India)
    const tax = subtotal * 0.18;
    
    // Shipping calculation
    let shipping = 0;
    if (subtotal < 1000) {
      shipping = 100; // Free shipping above ₹1000
    }
    
    // Discount calculation (simplified)
    let discount = 0;
    if (couponCode) {
      switch (couponCode.toUpperCase()) {
        case 'WELCOME10':
          discount = subtotal * 0.1; // 10% off
          break;
        case 'FESTIVE15':
          discount = subtotal * 0.15; // 15% off
          break;
        case 'BULK20':
          if (items.reduce((sum, item) => sum + item.quantity, 0) >= 10) {
            discount = subtotal * 0.2; // 20% off for bulk orders
          }
          break;
      }
    }
    
    const total = subtotal + tax + shipping - discount;
    
    return {
      subtotal,
      tax,
      shipping,
      discount,
      total,
      currency: 'INR'
    };
  },

  /**
   * Generate unique order number
   */
  generateOrderNumber: (): string => {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    return `CUS${timestamp.slice(-6)}${random}`;
  },

  /**
   * Create a new order
   */
  create: async (orderData: OrderData) => {
    try {
      const { generateClient } = await import('aws-amplify/api');
      const client = generateClient();
      
      // Calculate totals
      const totals = orderAPI.calculateTotals(
        orderData.items, 
        orderData.shippingAddress, 
        orderData.couponCode
      );
      
      // Generate order number
      const orderNumber = orderAPI.generateOrderNumber();
      
      // Create order
      const orderResult = await client.graphql({
        query: createOrder,
        variables: {
          input: {
            userId: orderData.userId,
            orderNumber,
            status: 'PENDING',
            ...totals,
            shippingAddress: orderData.shippingAddress,
            billingAddress: orderData.billingAddress || orderData.shippingAddress,
            paymentMethod: orderData.paymentMethod,
            paymentStatus: 'PENDING',
            customerNotes: orderData.customerNotes,
            giftMessage: orderData.giftMessage,
            specialInstructions: orderData.specialInstructions
          }
        }
      });

      const order = (orderResult.data as any)?.createOrder;
      
      if (!order) {
        throw new Error('Failed to create order');
      }

      // Create order items
      const orderItemPromises = orderData.items.map(item =>
        client.graphql({
          query: createOrderItem,
          variables: {
            input: {
              orderId: order.id,
              productId: item.productId,
              userId: orderData.userId,
              quantity: item.quantity,
              price: item.price,
              giftType: item.giftType,
              customization: item.customization,
              notes: item.notes
            }
          }
        })
      );

      await Promise.all(orderItemPromises);

      return {
        success: true,
        data: {
          order,
          orderNumber,
          total: totals.total
        },
        message: 'Order created successfully'
      };
    } catch (error: unknown) {
      console.error('Error creating order:', error);
      return {
        success: false,
        error: 'Failed to create order. Please try again.'
      };
    }
  },

  /**
   * Get user's orders
   */
  getUserOrders: async (userId: string) => {
    try {
      const { generateClient } = await import('aws-amplify/api');
      const client = generateClient();
      
      const result = await client.graphql({
        query: ordersByUserId,
        variables: { userId }
      });

      return {
        success: true,
        data: (result.data as any)?.ordersByUserId?.items || []
      };
    } catch (error: unknown) {
      console.error('Error fetching user orders:', error);
      return {
        success: false,
        error: 'Failed to fetch orders'
      };
    }
  },

  /**
   * Get order by order number
   */
  getByOrderNumber: async (orderNumber: string) => {
    try {
      const { generateClient } = await import('aws-amplify/api');
      const client = generateClient();
      
      const result = await client.graphql({
        query: ordersByOrderNumber,
        variables: { orderNumber }
      });

      const orders = (result.data as any)?.ordersByOrderNumber?.items || [];
      return {
        success: true,
        data: orders.length > 0 ? orders[0] : null
      };
    } catch (error: unknown) {
      console.error('Error fetching order by number:', error);
      return {
        success: false,
        error: 'Failed to fetch order'
      };
    }
  },

  /**
   * Update order status (admin)
   */
  updateStatus: async (orderId: string, status: 'PENDING' | 'CONFIRMED' | 'PROCESSING' | 'PACKED' | 'SHIPPED' | 'OUT_FOR_DELIVERY' | 'DELIVERED' | 'CANCELLED' | 'REFUNDED' | 'RETURNED', adminNotes?: string) => {
    try {
      const { generateClient } = await import('aws-amplify/api');
      const client = generateClient();
      
      const result = await client.graphql({
        query: updateOrder,
        variables: {
          input: {
            id: orderId,
            status,
            ...(adminNotes && { adminNotes })
          }
        }
      });

      return {
        success: true,
        data: (result.data as any)?.updateOrder
      };
    } catch (error: unknown) {
      console.error('Error updating order status:', error);
      return {
        success: false,
        error: 'Failed to update order status'
      };
    }
  },

  /**
   * Update order tracking information
   */
  updateTracking: async (orderId: string, trackingNumber: string, estimatedDelivery?: string) => {
    try {
      const { generateClient } = await import('aws-amplify/api');
      const client = generateClient();
      
      const result = await client.graphql({
        query: updateOrder,
        variables: {
          input: {
            id: orderId,
            trackingNumber,
            ...(estimatedDelivery && { estimatedDelivery: new Date(estimatedDelivery).toISOString() })
          }
        }
      });

      return {
        success: true,
        data: (result.data as any)?.updateOrder
      };
    } catch (error: unknown) {
      console.error('Error updating order tracking:', error);
      return {
        success: false,
        error: 'Failed to update tracking information'
      };
    }
  },

  /**
   * Cancel order
   */
  cancel: async (orderId: string, reason?: string) => {
    try {
      const result = await orderAPI.updateStatus(orderId, 'CANCELLED', reason);
      
      if (result.success) {
        return {
          success: true,
          data: result.data,
          message: 'Order cancelled successfully'
        };
      } else {
        return result;
      }
    } catch (error: unknown) {
      console.error('Error cancelling order:', error);
      return {
        success: false,
        error: 'Failed to cancel order'
      };
    }
  },

  /**
   * Validate coupon code
   */
  validateCoupon: async (code: string, subtotal: number, itemCount: number) => {
    // This is a simplified validation - in production, you'd check against a database
    const coupons: Record<string, { discount: number; minAmount?: number; minItems?: number; description: string }> = {
      'WELCOME10': { discount: 0.1, minAmount: 500, description: '10% off on orders above ₹500' },
      'FESTIVE15': { discount: 0.15, minAmount: 1000, description: '15% off on orders above ₹1000' },
      'BULK20': { discount: 0.2, minItems: 10, description: '20% off on bulk orders (10+ items)' },
      'FREESHIP': { discount: 0, description: 'Free shipping on any order' }
    };

    const coupon = coupons[code.toUpperCase()];
    
    if (!coupon) {
      return {
        success: false,
        error: 'Invalid coupon code'
      };
    }

    if (coupon.minAmount && subtotal < coupon.minAmount) {
      return {
        success: false,
        error: `Minimum order amount of ₹${coupon.minAmount} required`
      };
    }

    if (coupon.minItems && itemCount < coupon.minItems) {
      return {
        success: false,
        error: `Minimum ${coupon.minItems} items required`
      };
    }

    const discountAmount = subtotal * coupon.discount;

    return {
      success: true,
      data: {
        code: code.toUpperCase(),
        discount: coupon.discount,
        discountAmount,
        description: coupon.description
      }
    };
  },

  /**
   * Track order by order number
   */
  async trackOrder(orderNumber: string) {
    try {
      const { generateClient } = await import('aws-amplify/api');
      const client = generateClient();

      const result = await client.graphql({
        query: ordersByOrderNumber,
        variables: { orderNumber }
      });

      const orders = result.data?.ordersByOrderNumber?.items || [];
      if (orders.length === 0) {
        return {
          success: false,
          error: 'Order not found'
        };
      }

      const order = orders[0];

      // Fetch order items separately
      const itemsResult = await client.graphql({
        query: orderItemsByOrderId,
        variables: { orderId: order.id }
      });

      const orderItems = itemsResult.data?.orderItemsByOrderId?.items || [];

      // Transform items to include product information
      const transformedItems = await Promise.all(
        orderItems.map(async (item: any) => {
          try {
            // Fetch product details for each item
            const productResult = await client.graphql({
              query: getProduct,
              variables: { id: item.productId }
            });

            const product = productResult.data?.getProduct;

            return {
              id: item.id,
              name: product?.name || 'Unknown Product',
              quantity: item.quantity,
              price: item.price,
              giftType: item.giftType,
              customization: item.customization,
              notes: item.notes
            };
          } catch (error) {
            console.error('Error fetching product for item:', item.productId, error);
            return {
              id: item.id,
              name: 'Unknown Product',
              quantity: item.quantity,
              price: item.price,
              giftType: item.giftType,
              customization: item.customization,
              notes: item.notes
            };
          }
        })
      );

      return {
        success: true,
        data: {
          id: order.id,
          orderNumber: order.orderNumber,
          status: order.status,
          paymentStatus: order.paymentStatus,
          trackingNumber: order.trackingNumber,
          estimatedDelivery: order.estimatedDelivery,
          deliveredAt: order.deliveredAt,
          createdAt: order.createdAt,
          updatedAt: order.updatedAt,
          total: order.total,
          currency: order.currency,
          shippingAddress: order.shippingAddress,
          items: transformedItems
        }
      };
    } catch (error) {
      console.error('Error tracking order:', error);
      return {
        success: false,
        error: 'Failed to track order'
      };
    }
  },

  /**
   * Get user orders for dashboard
   */
  async getUserOrders(userId: string) {
    try {
      const { generateClient } = await import('aws-amplify/api');
      const client = generateClient();

      const result = await client.graphql({
        query: ordersByUserId,
        variables: { userId }
      });

      const orders = result.data?.ordersByUserId?.items || [];

      // Transform orders with items
      const transformedOrders = await Promise.all(
        orders.map(async (order: any) => {
          try {
            // Fetch order items
            const itemsResult = await client.graphql({
              query: orderItemsByOrderId,
              variables: { orderId: order.id }
            });

            const orderItems = itemsResult.data?.orderItemsByOrderId?.items || [];

            // Transform items to include product information
            const transformedItems = await Promise.all(
              orderItems.map(async (item: any) => {
                try {
                  // Fetch product details for each item
                  const productResult = await client.graphql({
                    query: getProduct,
                    variables: { id: item.productId }
                  });

                  const product = productResult.data?.getProduct;

                  return {
                    id: item.id,
                    name: product?.name || 'Unknown Product',
                    quantity: item.quantity,
                    price: item.price,
                    giftType: item.giftType,
                    customization: item.customization,
                    notes: item.notes
                  };
                } catch (error) {
                  console.error('Error fetching product for item:', item.productId, error);
                  return {
                    id: item.id,
                    name: 'Unknown Product',
                    quantity: item.quantity,
                    price: item.price,
                    giftType: item.giftType,
                    customization: item.customization,
                    notes: item.notes
                  };
                }
              })
            );

            return {
              id: order.id,
              orderNumber: order.orderNumber,
              status: order.status,
              paymentStatus: order.paymentStatus,
              trackingNumber: order.trackingNumber,
              estimatedDelivery: order.estimatedDelivery,
              deliveredAt: order.deliveredAt,
              createdAt: order.createdAt,
              updatedAt: order.updatedAt,
              total: order.total,
              currency: order.currency,
              shippingAddress: order.shippingAddress,
              items: transformedItems
            };
          } catch (error) {
            console.error('Error processing order:', order.id, error);
            return {
              id: order.id,
              orderNumber: order.orderNumber,
              status: order.status,
              paymentStatus: order.paymentStatus,
              trackingNumber: order.trackingNumber,
              estimatedDelivery: order.estimatedDelivery,
              deliveredAt: order.deliveredAt,
              createdAt: order.createdAt,
              updatedAt: order.updatedAt,
              total: order.total,
              currency: order.currency,
              shippingAddress: order.shippingAddress,
              items: []
            };
          }
        })
      );

      return {
        success: true,
        data: transformedOrders
      };
    } catch (error) {
      console.error('Error fetching user orders:', error);
      return {
        success: false,
        error: 'Failed to fetch orders'
      };
    }
  },

  /**
   * Update order status (admin function)
   */
  async updateOrderStatus(orderId: string, status: string, trackingNumber?: string, estimatedDelivery?: string) {
    try {
      const { generateClient } = await import('aws-amplify/api');
      const client = generateClient();

      const updateInput: any = {
        id: orderId,
        status,
      };

      if (trackingNumber) {
        updateInput.trackingNumber = trackingNumber;
      }

      if (estimatedDelivery) {
        updateInput.estimatedDelivery = estimatedDelivery;
      }

      if (status === 'DELIVERED') {
        updateInput.deliveredAt = new Date().toISOString();
      }

      const result = await client.graphql({
        query: updateOrder,
        variables: { input: updateInput }
      });

      return {
        success: true,
        data: result.data?.updateOrder
      };
    } catch (error) {
      console.error('Error updating order status:', error);
      return {
        success: false,
        error: 'Failed to update order status'
      };
    }
  },

  /**
   * Get all orders (admin only)
   */
  async getAllOrders(limit = 100): Promise<OrderResponse> {
    try {
      const { generateClient } = await import('aws-amplify/api');
      const client = generateClient();

      const result = await client.graphql({
        query: listOrders,
        variables: { limit }
      });

      const orders = result.data?.listOrders?.items || [];

      // Fetch order items for each order
      const ordersWithItems = await Promise.all(
        orders.map(async (order: any) => {
          try {
            const itemsResult = await client.graphql({
              query: orderItemsByOrderId,
              variables: { orderId: order.id }
            });

            const items = itemsResult.data?.orderItemsByOrderId?.items || [];

            // Fetch product details for each item
            const itemsWithProducts = await Promise.all(
              items.map(async (item: any) => {
                try {
                  const productResult = await client.graphql({
                    query: getProduct,
                    variables: { id: item.productId }
                  });

                  const product = productResult.data?.getProduct;

                  return {
                    id: item.id,
                    name: product?.name || 'Unknown Product',
                    quantity: item.quantity,
                    price: item.price,
                    giftType: item.giftType,
                    customization: item.customization,
                    notes: item.notes
                  };
                } catch (error) {
                  console.error('Error fetching product for item:', error);
                  return {
                    id: item.id,
                    name: 'Unknown Product',
                    quantity: item.quantity,
                    price: item.price,
                    giftType: item.giftType,
                    customization: item.customization,
                    notes: item.notes
                  };
                }
              })
            );

            return {
              ...order,
              items: itemsWithProducts
            };
          } catch (error) {
            console.error('Error fetching items for order:', error);
            return {
              ...order,
              items: []
            };
          }
        })
      );

      return {
        success: true,
        data: ordersWithItems
      };
    } catch (error) {
      console.error('Error fetching all orders:', error);
      return {
        success: false,
        error: 'Failed to fetch orders'
      };
    }
  }
};
