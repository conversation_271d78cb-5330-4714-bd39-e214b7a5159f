/**
 * Utility functions to clean up malformed S3 URLs and extract proper S3 keys
 */

/**
 * Extracts a clean S3 key from a malformed or nested URL
 */
export const extractCleanS3Key = (malformedUrl: string): string | null => {
  try {
    console.log('Attempting to extract S3 key from:', malformedUrl.substring(0, 100) + '...');
    
    // Case 1: Double-encoded URLs (contains https%3A or http%3A)
    if (malformedUrl.includes('https%3A') || malformedUrl.includes('http%3A')) {
      console.log('Detected double-encoded URL');
      
      // Try to find products/ pattern in the URL
      const match = malformedUrl.match(/products\/[^?&%]+/);
      if (match) {
        const key = decodeURIComponent(match[0]);
        console.log('Extracted key from double-encoded URL:', key);
        return key;
      }
      
      // Alternative: try to find products pattern after decoding
      const decoded = decodeURIComponent(malformedUrl);
      const decodedMatch = decoded.match(/products\/[^?&]+/);
      if (decodedMatch) {
        const key = decodedMatch[0];
        console.log('Extracted key after decoding:', key);
        return key;
      }
    }
    
    // Case 2: Normal S3 URL with products/ path
    if (malformedUrl.includes('/products/')) {
      const urlParts = malformedUrl.split('/');
      const keyIndex = urlParts.findIndex(part => part === 'products');
      if (keyIndex !== -1 && keyIndex < urlParts.length - 1) {
        const key = urlParts.slice(keyIndex).join('/');
        // Remove query parameters
        const cleanKey = key.split('?')[0];
        const decodedKey = decodeURIComponent(cleanKey);
        console.log('Extracted key from normal URL:', decodedKey);
        return decodedKey;
      }
    }
    
    // Case 3: Already a clean S3 key
    if (malformedUrl.startsWith('products/')) {
      console.log('Already a clean S3 key:', malformedUrl);
      return malformedUrl;
    }
    
    console.log('Could not extract S3 key from URL');
    return null;
    
  } catch (error) {
    console.error('Error extracting S3 key:', error);
    return null;
  }
};

/**
 * Cleans an array of image URLs/keys, converting malformed URLs to clean S3 keys
 */
export const cleanImageArray = (images: string[]): string[] => {
  return images.map(image => {
    // If it's already a relative path, keep it
    if (image.startsWith('/')) {
      return image;
    }
    
    // If it's already a clean S3 key, keep it
    if (image.startsWith('products/') && !image.includes('http')) {
      return image;
    }
    
    // Try to extract clean S3 key from malformed URL
    const cleanKey = extractCleanS3Key(image);
    if (cleanKey) {
      return cleanKey;
    }
    
    // If we can't clean it, return original (will fallback to placeholder)
    console.warn('Could not clean image URL:', image);
    return image;
  }).filter(Boolean); // Remove any null/undefined values
};

/**
 * Validates if a string is a proper S3 key
 */
export const isValidS3Key = (key: string): boolean => {
  // Should start with products/ and not contain http
  return key.startsWith('products/') && !key.includes('http') && !key.includes('%');
};

/**
 * Batch cleanup function for product data
 */
export const cleanupProductImages = (product: { images?: string[]; [key: string]: unknown }): { images?: string[]; [key: string]: unknown } => {
  if (!product.images || !Array.isArray(product.images)) {
    return product;
  }
  
  const cleanedImages = cleanImageArray(product.images);
  
  console.log('Image cleanup result:', {
    original: product.images.length,
    cleaned: cleanedImages.length,
    before: product.images.map(img => img.substring(0, 50) + '...'),
    after: cleanedImages
  });
  
  return {
    ...product,
    images: cleanedImages
  };
};

/**
 * Debug function to analyze image URL patterns
 */
export const analyzeImageUrl = (url: string) => {
  return {
    original: url.substring(0, 100) + (url.length > 100 ? '...' : ''),
    length: url.length,
    isRelativePath: url.startsWith('/'),
    isS3Key: url.startsWith('products/'),
    isHttpUrl: url.startsWith('http'),
    isDoubleEncoded: url.includes('https%3A') || url.includes('http%3A'),
    hasProducts: url.includes('products/'),
    extractedKey: extractCleanS3Key(url)
  };
};
