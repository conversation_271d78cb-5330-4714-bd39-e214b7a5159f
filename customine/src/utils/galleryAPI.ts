"use client";

import { Amplify } from "aws-amplify";
import { awsConfig } from "../config/aws";

// Configure Amplify
Amplify.configure({
  API: {
    GraphQL: {
      endpoint: awsConfig.graphql.endpoint,
      region: awsConfig.graphql.region,
      defaultAuthMode: 'apiKey', // Change this if your API uses a different auth mode
      apiKey: process.env.NEXT_PUBLIC_GRAPHQL_API_KEY, // Set this in your .env if using API key auth
    },
  },
  Storage: {
    S3: {
      bucket: awsConfig.s3.bucketName,
      region: awsConfig.s3.region,
    },
  },
});

/**
 * Gallery Image interface
 */
export interface GalleryImage {
  id: string;
  title: string;
  description?: string;
  imageKey: string;
  imageUrl?: string;
  category:
    | "HOME"
    | "LUXURY"
    | "CORPORATE"
    | "WEDDING"
    | "FESTIVAL"
    | "PERSONAL";
  subcategory?: string;
  tags?: string[];
  isActive: boolean;
  sortOrder?: number;
  altText?: string;
  metaTitle?: string;
  metaDescription?: string;
  createdBy?: string;
  updatedBy?: string;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * Gallery API response interface
 */
export interface GalleryAPIResponse {
  items: GalleryImage[];
  nextToken?: string;
}

/**
 * Fetch gallery images by category
 */
export async function fetchGalleryByCategory(
  category:
    | "HOME"
    | "LUXURY"
    | "CORPORATE"
    | "WEDDING"
    | "FESTIVAL"
    | "PERSONAL",
  limit: number = 10,
  activeOnly: boolean = true
): Promise<GalleryImage[]> {
  try {
    const { generateClient } = await import("aws-amplify/api");
    const { getUrl } = await import("aws-amplify/storage");
    const { listGalleries } = await import("../graphql/queries");

    const client = generateClient();

    // Build filter
    const filter: any = {
      category: { eq: category },
    };

    if (activeOnly) {
      filter.isActive = { eq: true };
    }

    const result = await client.graphql({
      query: listGalleries,
      variables: {
        filter,
        limit,
      },
    });

    if ("data" in result && result.data?.listGalleries?.items) {
      const galleryItems = (result as any).data.listGalleries.items;

      // Sort by sortOrder if available, otherwise by createdAt
      const sortedItems = galleryItems.sort((a: any, b: any) => {
        if (a.sortOrder !== undefined && b.sortOrder !== undefined) {
          return a.sortOrder - b.sortOrder;
        }
        return (
          new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        );
      });

      // Get image URLs from S3 for each item
      const imagesWithUrls = await Promise.all(
        sortedItems.map(async (item: any) => {
          let imageUrl = "";
          if (item.imageKey) {
            try {
              // Use Amplify getUrl for signed URLs
              const urlResult = await getUrl({
                key: item.imageKey,
                options: { expiresIn: 86400 }, // 24 hours
              });
              imageUrl = urlResult.url.toString();
            } catch (urlError) {
              console.error("Error getting signed URL:", urlError);
              // Fallback to direct S3 URL if needed
              imageUrl = `https://customine4ae3bb77bc244f68885ca662b9bd1b4e1a04b-dev.s3.ap-south-1.amazonaws.com/public/${item.imageKey}`;
            }
          }

          return {
            ...item,
            imageUrl,
          } as GalleryImage;
        })
      );

      return imagesWithUrls;
    }

    return [];
  } catch (error) {
    console.error("Error fetching gallery:", error);
    return [];
  }
}

/**
 * Fetch homepage hero gallery images
 */
export async function fetchHomepageHeroGallery(): Promise<GalleryImage[]> {
  return fetchGalleryByCategory("HOME", 10, true);
}

/**
 * Fetch luxury gallery images
 */
export async function fetchLuxuryGallery(): Promise<GalleryImage[]> {
  return fetchGalleryByCategory("LUXURY", 20, true);
}

/**
 * Fetch gallery images by category and subcategory
 */
export async function fetchGalleryBySubcategory(
  category:
    | "HOME"
    | "LUXURY"
    | "CORPORATE"
    | "WEDDING"
    | "FESTIVAL"
    | "PERSONAL",
  subcategory: string,
  limit: number = 10,
  activeOnly: boolean = true
): Promise<GalleryImage[]> {
  try {
    const { generateClient } = await import("aws-amplify/api");
    const { listGalleries } = await import("../graphql/queries");

    const client = generateClient();

    // Build filter with both category and subcategory
    const filter: any = {
      category: { eq: category },
      subcategory: { eq: subcategory },
    };

    if (activeOnly) {
      filter.isActive = { eq: true };
    }

    const result = await client.graphql({
      query: listGalleries,
      variables: {
        filter,
        limit,
      },
    });

    if ("data" in result && result.data?.listGalleries?.items) {
      const galleryItems = (result as any).data.listGalleries.items;

      // Sort by sortOrder if available, otherwise by createdAt
      const sortedItems = galleryItems.sort((a: any, b: any) => {
        if (a.sortOrder !== undefined && b.sortOrder !== undefined) {
          return a.sortOrder - b.sortOrder;
        }
        return (
          new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        );
      });

      // Generate PUBLIC S3 URLs for each item (no authentication required)
      const imagesWithUrls = sortedItems.map((item: any) => {
        let imageUrl = "";
        if (item.imageKey) {
          // Use public S3 URL instead of signed URL for better performance
          imageUrl = generatePublicS3Url(item.imageKey);
          console.log(
            "Generated public S3 URL for subcategory gallery:",
            item.imageKey,
            "→",
            imageUrl
          );
        }

        return {
          ...item,
          imageUrl,
        } as GalleryImage;
      });

      return imagesWithUrls;
    }

    return [];
  } catch (error) {
    console.error("Error fetching gallery by subcategory:", error);
    return [];
  }
}

/**
 * Fetch all active gallery images
 */
export async function fetchAllActiveGallery(): Promise<GalleryImage[]> {
  try {
    const { generateClient } = await import("aws-amplify/api");
    const { getUrl } = await import("aws-amplify/storage");
    const { listGalleries } = await import("../graphql/queries");

    const client = generateClient();

    const result = await client.graphql({
      query: listGalleries,
      variables: {
        filter: {
          isActive: { eq: true },
        },
        limit: 50,
      },
    });

    if ("data" in result && result.data?.listGalleries?.items) {
      const galleryItems = (result as any).data.listGalleries.items;

      // Sort by category, then by sortOrder, then by createdAt
      const sortedItems = galleryItems.sort((a: any, b: any) => {
        // First sort by category
        if (a.category !== b.category) {
          return a.category.localeCompare(b.category);
        }

        // Then by sortOrder if available
        if (a.sortOrder !== undefined && b.sortOrder !== undefined) {
          return a.sortOrder - b.sortOrder;
        }

        // Finally by createdAt
        return (
          new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        );
      });

      // Get image URLs from S3 for each item
      const imagesWithUrls = await Promise.all(
        sortedItems.map(async (item: any) => {
          let imageUrl = "";
          if (item.imageKey) {
            try {
              const urlResult = await getUrl({
                key: item.imageKey,
                options: { expiresIn: 86400 },
              });
              imageUrl = urlResult.url.toString();
            } catch (urlError) {
              console.error("Error getting signed URL:", urlError);
              imageUrl = `https://customine-storage.s3.amazonaws.com/public/${item.imageKey}`;
            }
          }

          return {
            ...item,
            imageUrl,
          } as GalleryImage;
        })
      );

      return imagesWithUrls;
    }

    return [];
  } catch (error) {
    console.error("Error fetching all active gallery:", error);
    return [];
  }
}

/**
 * Get gallery image URLs only (for simple image arrays)
 */
export async function getGalleryImageUrls(
  category:
    | "HOME"
    | "LUXURY"
    | "CORPORATE"
    | "WEDDING"
    | "FESTIVAL"
    | "PERSONAL",
  limit: number = 10
): Promise<string[]> {
  const images = await fetchGalleryByCategory(category, limit, true);
  return images.map((img) => img.imageUrl || "").filter((url) => url !== "");
}

/**
 * Get homepage hero image URLs
 */
export async function getHomepageHeroImageUrls(): Promise<string[]> {
  return getGalleryImageUrls("HOME", 10);
}

/**
 * Get luxury gallery image URLs
 */
export async function getLuxuryGalleryImageUrls(): Promise<string[]> {
  return getGalleryImageUrls("LUXURY", 20);
}

/**
 * Generate public S3 URL for gallery images
 * This works because our S3 bucket has guest read access enabled
 */
export function generatePublicS3Url(key: string): string {
  // Extract bucket name from amplify config
  const bucketName = "customine4ae3bb77bc244f68885ca662b9bd1b4e1a04b-dev"; // From cli-inputs.json
  const region = "ap-south-1"; // Default Amplify region

  // Generate public URL
  return `https://${bucketName}.s3.${region}.amazonaws.com/public/${key}`;
}

/**
 * Fetch gallery images by category with PUBLIC S3 URLs (no authentication required)
 * Perfect for home page hero slider and public-facing components
 */
export async function fetchPublicGalleryByCategory(
  category:
    | "HOME"
    | "LUXURY"
    | "CORPORATE"
    | "WEDDING"
    | "FESTIVAL"
    | "PERSONAL",
  limit: number = 10,
  activeOnly: boolean = true
): Promise<GalleryImage[]> {
  try {
    const { generateClient } = await import("aws-amplify/api");
    const { listGalleries } = await import("../graphql/queries");

    const client = generateClient();

    // Build filter
    const filter: any = {
      category: { eq: category },
    };

    if (activeOnly) {
      filter.isActive = { eq: true };
    }

    const result = await client.graphql({
      query: listGalleries,
      variables: {
        filter,
        limit,
      },
    });

    if ("data" in result && result.data?.listGalleries?.items) {
      const galleryItems = (result as any).data.listGalleries.items;

      // Sort by sortOrder if available, otherwise by createdAt
      const sortedItems = galleryItems.sort((a: any, b: any) => {
        if (a.sortOrder !== undefined && b.sortOrder !== undefined) {
          return a.sortOrder - b.sortOrder;
        }
        return (
          new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        );
      });

      // Generate PUBLIC S3 URLs for each item (no authentication required)
      const imagesWithUrls = sortedItems.map((item: any) => {
        let imageUrl = "";
        if (item.imageKey) {
          // Use public S3 URL instead of signed URL
          imageUrl = generatePublicS3Url(item.imageKey);
          console.log(
            "Generated public S3 URL for gallery:",
            item.imageKey,
            "→",
            imageUrl
          );
        }

        return {
          ...item,
          imageUrl,
        } as GalleryImage;
      });

      return imagesWithUrls;
    }

    return [];
  } catch (error) {
    console.error("Error fetching public gallery:", error);
    return [];
  }
}

/**
 * Fetch homepage hero gallery images with PUBLIC URLs (no login required)
 */
export async function fetchPublicHomepageHeroGallery(): Promise<
  GalleryImage[]
> {
  return fetchPublicGalleryByCategory("HOME", 10, true);
}

/**
 * Fetch luxury gallery images with PUBLIC URLs (no login required)
 */
export async function fetchPublicLuxuryGallery(): Promise<GalleryImage[]> {
  return fetchPublicGalleryByCategory("LUXURY", 20, true);
}
