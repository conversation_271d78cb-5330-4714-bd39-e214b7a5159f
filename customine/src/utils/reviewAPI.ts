"use client";

import { Amplify } from "aws-amplify";
import { awsConfig } from "../config/aws";

// Configure Amplify
Amplify.configure({
  API: {
    GraphQL: {
      endpoint: awsConfig.graphql.endpoint,
      region: awsConfig.graphql.region,
      defaultAuthMode: 'apiKey', // Change this if your API uses a different auth mode
      apiKey: process.env.NEXT_PUBLIC_GRAPHQL_API_KEY, // Set this in your .env if using API key auth
    },
  },
  Storage: {
    S3: {
      bucket: awsConfig.s3.bucketName,
      region: awsConfig.s3.region,
    },
  },
});

/**
 * Review interface
 */
export interface Review {
  id: string;
  productId: string;
  userId?: string;
  rating: number;
  title: string;
  content: string;
  reviewerName: string;
  reviewerEmail: string;
  isVerifiedPurchase: boolean;
  isApproved: boolean;
  isHelpful?: number;
  isReported?: boolean;
  moderatorNotes?: string;
  approvedBy?: string;
  approvedAt?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Review submission interface
 */
export interface ReviewSubmission {
  productId: string;
  rating: number;
  title: string;
  content: string;
  reviewerName: string;
  reviewerEmail: string;
  userId?: string;
}

/**
 * Review statistics interface
 */
export interface ReviewStats {
  averageRating: number;
  totalReviews: number;
  ratingDistribution: {
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
  };
}

/**
 * Submit a new review
 */
export async function submitReview(reviewData: ReviewSubmission): Promise<Review> {
  try {
    const { generateClient } = await import("aws-amplify/api");
    const { createReview } = await import("../graphql/mutations");

    const client = generateClient();

    const result = await client.graphql({
      query: createReview,
      variables: {
        input: {
          productId: reviewData.productId,
          rating: reviewData.rating,
          title: reviewData.title,
          content: reviewData.content,
          reviewerName: reviewData.reviewerName,
          reviewerEmail: reviewData.reviewerEmail,
          userId: reviewData.userId,
          isVerifiedPurchase: false, // TODO: Check if user actually purchased
          isApproved: false, // Reviews need approval by default
          isHelpful: 0,
          isReported: false,
        },
      },
    });

    if ("data" in result && result.data?.createReview) {
      return (result as any).data.createReview as Review;
    }

    throw new Error("Failed to create review");
  } catch (error) {
    console.error("Error submitting review:", error);
    throw error;
  }
}

/**
 * Fetch reviews for a product
 */
export async function fetchProductReviews(
  productId: string,
  limit: number = 10,
  approvedOnly: boolean = true
): Promise<Review[]> {
  try {
    const { generateClient } = await import("aws-amplify/api");
    const { listReviews } = await import("../graphql/queries");

    const client = generateClient();

    // Build filter
    const filter: any = {
      productId: { eq: productId },
    };

    if (approvedOnly) {
      filter.isApproved = { eq: true };
    }

    const result = await client.graphql({
      query: listReviews,
      variables: {
        filter,
        limit,
      },
    });

    if ("data" in result && result.data?.listReviews?.items) {
      // Sort by creation date (newest first)
      const reviews = (result as any).data.listReviews.items.sort(
        (a: any, b: any) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );

      return reviews as Review[];
    }

    return [];
  } catch (error) {
    console.error("Error fetching product reviews:", error);
    return [];
  }
}

/**
 * Fetch all reviews (for admin dashboard)
 */
export async function fetchAllReviews(
  limit: number = 100,
  approvedOnly: boolean = false
): Promise<Review[]> {
  try {
    console.log('🔍 fetchAllReviews: Starting to fetch all reviews', { limit, approvedOnly });

    const { generateClient } = await import("aws-amplify/api");
    const { listReviews } = await import("../graphql/queries");

    const client = generateClient();

    // Build filter - only filter by approval status if specified
    const filter: any = {};

    if (approvedOnly) {
      filter.isApproved = { eq: true };
    }

    console.log('🔍 fetchAllReviews: Using filter', filter);

    const result = await client.graphql({
      query: listReviews,
      variables: {
        filter: Object.keys(filter).length > 0 ? filter : undefined,
        limit,
      },
    });

    console.log('🔍 fetchAllReviews: GraphQL result', result);

    if ("data" in result && result.data?.listReviews?.items) {
      // Sort by creation date (newest first)
      const reviews = (result as any).data.listReviews.items.sort(
        (a: any, b: any) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );

      console.log(`📊 fetchAllReviews: Found ${reviews.length} reviews`, reviews);
      return reviews as Review[];
    }

    console.log('⚠️ fetchAllReviews: No reviews found in result');
    return [];
  } catch (error) {
    console.error("❌ Error fetching all reviews:", error);
    return [];
  }
}

/**
 * Calculate review statistics for a product
 */
export async function getProductReviewStats(productId: string): Promise<ReviewStats> {
  try {
    const reviews = await fetchProductReviews(productId, 1000, true);

    if (reviews.length === 0) {
      return {
        averageRating: 0,
        totalReviews: 0,
        ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 },
      };
    }

    // Calculate average rating
    const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0);
    const averageRating = totalRating / reviews.length;

    // Calculate rating distribution
    const ratingDistribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
    reviews.forEach((review) => {
      ratingDistribution[review.rating as keyof typeof ratingDistribution]++;
    });

    return {
      averageRating: Math.round(averageRating * 10) / 10, // Round to 1 decimal
      totalReviews: reviews.length,
      ratingDistribution,
    };
  } catch (error) {
    console.error("Error calculating review stats:", error);
    return {
      averageRating: 0,
      totalReviews: 0,
      ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 },
    };
  }
}

/**
 * Mark review as helpful
 */
export async function markReviewHelpful(reviewId: string): Promise<boolean> {
  try {
    const { generateClient } = await import("aws-amplify/api");
    const { updateReview } = await import("../graphql/mutations");
    const { getReview } = await import("../graphql/queries");

    const client = generateClient();

    // First get current helpful count
    const currentReview = await client.graphql({
      query: getReview,
      variables: { id: reviewId },
    });

    if (!("data" in currentReview && currentReview.data?.getReview)) {
      throw new Error("Review not found");
    }

    const currentHelpful = (currentReview as any).data.getReview.isHelpful || 0;

    // Update helpful count
    await client.graphql({
      query: updateReview,
      variables: {
        input: {
          id: reviewId,
          isHelpful: currentHelpful + 1,
        },
      },
    });

    return true;
  } catch (error) {
    console.error("Error marking review as helpful:", error);
    return false;
  }
}

/**
 * Report a review
 */
export async function reportReview(reviewId: string): Promise<boolean> {
  try {
    const { generateClient } = await import("aws-amplify/api");
    const { updateReview } = await import("../graphql/mutations");

    const client = generateClient();

    await client.graphql({
      query: updateReview,
      variables: {
        input: {
          id: reviewId,
          isReported: true,
        },
      },
    });

    return true;
  } catch (error) {
    console.error("Error reporting review:", error);
    return false;
  }
}

/**
 * Approve a review (admin function)
 */
export async function approveReview(reviewId: string, approvedBy: string = 'admin'): Promise<boolean> {
  try {
    const { generateClient } = await import("aws-amplify/api");
    const { updateReview } = await import("../graphql/mutations");

    const client = generateClient();

    await client.graphql({
      query: updateReview,
      variables: {
        input: {
          id: reviewId,
          isApproved: true,
          approvedBy,
          approvedAt: new Date().toISOString(),
        },
      },
    });

    return true;
  } catch (error) {
    console.error("Error approving review:", error);
    return false;
  }
}

/**
 * Delete a review (admin function)
 */
export async function deleteReview(reviewId: string): Promise<boolean> {
  try {
    const { generateClient } = await import("aws-amplify/api");
    const { deleteReview: deleteReviewMutation } = await import("../graphql/mutations");

    const client = generateClient();

    await client.graphql({
      query: deleteReviewMutation,
      variables: {
        input: {
          id: reviewId,
        },
      },
    });

    return true;
  } catch (error) {
    console.error("Error deleting review:", error);
    return false;
  }
}
