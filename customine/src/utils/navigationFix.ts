/**
 * Navigation fix utilities for static exports
 * Helps handle RSC payload and navigation errors
 */

// Override console.error to catch and handle navigation errors
export const setupNavigationErrorHandling = () => {
  if (typeof window === 'undefined') return;

  const originalError = console.error;
  console.error = (...args) => {
    const message = args[0]?.toString() || '';
    
    // Check for RSC payload errors
    if (message.includes('Failed to fetch RSC payload') || 
        message.includes('e[o] is not a function') ||
        message.includes('TypeError: e[o] is not a function')) {
      
      console.warn('🔧 Navigation error detected and handled:', message);
      
      // Try to extract the URL from the error and navigate manually
      const urlMatch = message.match(/for (\/[^\s]+)/);
      if (urlMatch && urlMatch[1]) {
        const url = urlMatch[1];
        console.log('🔄 Attempting manual navigation to:', url);
        
        // Use setTimeout to avoid blocking the current execution
        setTimeout(() => {
          window.location.href = url;
        }, 100);
      }
      
      // Don't log the original error to avoid console spam
      return;
    }
    
    // For other errors, use original console.error
    originalError(...args);
  };
};

// Function to safely navigate with fallback
export const safeNavigate = (url: string) => {
  try {
    // Try using Next.js router first
    if (typeof window !== 'undefined' && window.history) {
      window.history.pushState(null, '', url);
      window.dispatchEvent(new PopStateEvent('popstate'));
    } else {
      throw new Error('History API not available');
    }
  } catch (error) {
    console.warn('Router navigation failed, using window.location:', error);
    window.location.href = url;
  }
};

// Function to preload critical pages
export const preloadCriticalPages = () => {
  if (typeof window === 'undefined') return;
  
  const criticalPages = ['/cart', '/collections/all', '/pages/about-us'];
  
  criticalPages.forEach(page => {
    const link = document.createElement('link');
    link.rel = 'prefetch';
    link.href = page;
    document.head.appendChild(link);
  });
};

// Initialize navigation fixes
export const initNavigationFixes = () => {
  if (typeof window === 'undefined') return;
  
  setupNavigationErrorHandling();
  preloadCriticalPages();
  
  // Add global error handler for unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    const message = event.reason?.message || '';
    
    if (message.includes('RSC payload') || message.includes('e[o] is not a function')) {
      console.warn('🔧 Unhandled navigation promise rejection handled:', message);
      event.preventDefault();
    }
  });
  
  console.log('🚀 Navigation fixes initialized');
};
