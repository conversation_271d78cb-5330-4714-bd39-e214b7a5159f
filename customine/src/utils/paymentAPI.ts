/**
 * AWS Amplify-based Payment API for Customine
 * Handles Razorpay payment processing through Order model
 */

import { Amplify } from 'aws-amplify';
import { generateClient } from 'aws-amplify/api';
import amplifyconfig from '../amplifyconfiguration.json';
import { createOrder, updateOrder, createOrderItem } from '../graphql/mutations';
import { getOrder, listOrders } from '../graphql/queries';

// Configure Amplify
Amplify.configure(amplifyconfig);

// Payment interfaces
export interface PaymentOrderData {
  userId: string;
  items: Array<{
    productId: string;
    quantity: number;
    price: number;
    giftType?: string;
    customization?: any;
    notes?: string;
  }>;
  subtotal: number;
  tax: number;
  shipping: number;
  total: number;
  shippingAddress: {
    fullName: string;
    addressLine1: string;
    addressLine2?: string;
    city: string;
    state: string;
    pincode: string;
    country: string;
    phone: string;
    email?: string;
  };
  billingAddress?: {
    fullName: string;
    addressLine1: string;
    addressLine2?: string;
    city: string;
    state: string;
    pincode: string;
    country: string;
    phone: string;
    email?: string;
  };
  paymentMethod: string;
  notes?: string;
}

export interface RazorpayOrderResponse {
  id: string;
  amount: number;
  currency: string;
  receipt: string;
  status: string;
}

export interface PaymentVerificationData {
  orderId: string;
  razorpayOrderId: string;
  razorpayPaymentId: string;
  razorpaySignature: string;
}

export interface PaymentResponse {
  success: boolean;
  data?: any;
  error?: string;
}

export const paymentAPI = {
  /**
   * Create a new order with payment information
   */
  async createPaymentOrder(orderData: PaymentOrderData): Promise<PaymentResponse> {
    try {
      const client = generateClient();

      // Generate order number
      const orderNumber = `CUST-${Date.now()}-${Math.random().toString(36).substr(2, 4).toUpperCase()}`;

      // Create order in database
      const orderInput = {
        userId: orderData.userId,
        orderNumber,
        status: 'PENDING',
        subtotal: orderData.subtotal,
        tax: orderData.tax,
        shipping: orderData.shipping,
        discount: 0,
        total: orderData.total,
        currency: 'INR',
        shippingAddress: {
          name: orderData.shippingAddress.fullName,
          phone: orderData.shippingAddress.phone,
          email: orderData.shippingAddress.email || '',
          addressLine1: orderData.shippingAddress.addressLine1,
          addressLine2: orderData.shippingAddress.addressLine2 || '',
          city: orderData.shippingAddress.city,
          state: orderData.shippingAddress.state,
          pincode: orderData.shippingAddress.pincode,
          country: orderData.shippingAddress.country,
          addressType: 'home'
        },
        billingAddress: orderData.billingAddress ? {
          name: orderData.billingAddress.fullName,
          phone: orderData.billingAddress.phone,
          email: orderData.billingAddress.email || '',
          addressLine1: orderData.billingAddress.addressLine1,
          addressLine2: orderData.billingAddress.addressLine2 || '',
          city: orderData.billingAddress.city,
          state: orderData.billingAddress.state,
          pincode: orderData.billingAddress.pincode,
          country: orderData.billingAddress.country,
          addressType: 'home'
        } : {
          name: orderData.shippingAddress.fullName,
          phone: orderData.shippingAddress.phone,
          email: orderData.shippingAddress.email || '',
          addressLine1: orderData.shippingAddress.addressLine1,
          addressLine2: orderData.shippingAddress.addressLine2 || '',
          city: orderData.shippingAddress.city,
          state: orderData.shippingAddress.state,
          pincode: orderData.shippingAddress.pincode,
          country: orderData.shippingAddress.country,
          addressType: 'home'
        },
        paymentMethod: orderData.paymentMethod,
        paymentStatus: 'PENDING',
        customerNotes: orderData.notes,
        specialInstructions: orderData.notes
      };

      const result = await client.graphql({
        query: createOrder,
        variables: { input: orderInput }
      });

      const createdOrder = result.data?.createOrder;
      if (!createdOrder) {
        throw new Error('Failed to create order in database');
      }

      // Create order items
      for (const item of orderData.items) {
        await client.graphql({
          query: createOrderItem,
          variables: {
            input: {
              orderId: createdOrder.id,
              productId: item.productId,
              userId: orderData.userId,
              quantity: item.quantity,
              price: item.price,
              giftType: item.giftType,
              customization: item.customization ? JSON.stringify(item.customization) : null,
              notes: item.notes
            }
          }
        });
      }

      console.log('Order created successfully:', createdOrder.id);
      return {
        success: true,
        data: createdOrder
      };

    } catch (error) {
      console.error('Error creating payment order:', error);
      return {
        success: false,
        error: 'Failed to create order'
      };
    }
  },

  /**
   * Create Razorpay order (client-side integration)
   */
  createRazorpayOrder(order: any): RazorpayOrderResponse {
    try {
      // Since we can't use server-side API routes in static export,
      // we'll create a mock Razorpay order structure that works with client-side integration
      const razorpayOrder: RazorpayOrderResponse = {
        id: `order_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        amount: Math.round(order.total * 100), // Convert to paise
        currency: 'INR',
        receipt: order.orderNumber,
        status: 'created'
      };

      console.log('Razorpay order created:', razorpayOrder);
      return razorpayOrder;

    } catch (error) {
      console.error('Error creating Razorpay order:', error);
      throw error;
    }
  },

  /**
   * Verify payment and update order status
   */
  async verifyPayment(verificationData: PaymentVerificationData): Promise<PaymentResponse> {
    try {
      const client = generateClient();

      // For client-side verification, we'll do basic validation
      // In production, you should implement server-side signature verification
      const isValid = verificationData.razorpayOrderId &&
                     verificationData.razorpayPaymentId &&
                     verificationData.razorpaySignature;

      if (!isValid) {
        throw new Error('Invalid payment verification data');
      }

      // Update order with payment information
      const updateInput = {
        id: verificationData.orderId,
        paymentStatus: 'PAID',
        status: 'CONFIRMED',
        paymentId: verificationData.razorpayPaymentId,
        razorpayOrderId: verificationData.razorpayOrderId,
        razorpayPaymentId: verificationData.razorpayPaymentId
      };

      const result = await client.graphql({
        query: updateOrder,
        variables: { input: updateInput }
      });

      const updatedOrder = result.data?.updateOrder;
      if (!updatedOrder) {
        throw new Error('Failed to update order after payment verification');
      }

      console.log('Payment verified and order updated:', updatedOrder.id);
      return {
        success: true,
        data: updatedOrder
      };

    } catch (error) {
      console.error('Error verifying payment:', error);
      return {
        success: false,
        error: 'Failed to verify payment'
      };
    }
  },

  /**
   * Get order details by ID
   */
  async getOrderDetails(orderId: string): Promise<PaymentResponse> {
    try {
      const client = generateClient();

      const result = await client.graphql({
        query: getOrder,
        variables: { id: orderId }
      });

      const order = result.data?.getOrder;
      if (!order) {
        throw new Error('Order not found');
      }

      return {
        success: true,
        data: order
      };

    } catch (error) {
      console.error('Error fetching order details:', error);
      return {
        success: false,
        error: 'Failed to fetch order'
      };
    }
  },

  /**
   * Get user orders
   */
  async getUserOrders(userId: string): Promise<PaymentResponse> {
    try {
      const client = generateClient();

      const result = await client.graphql({
        query: listOrders,
        variables: {
          filter: {
            userId: { eq: userId }
          }
        }
      });

      return {
        success: true,
        data: result.data?.listOrders?.items || []
      };
    } catch (error) {
      console.error('Error fetching user orders:', error);
      return {
        success: false,
        error: 'Failed to fetch orders'
      };
    }
  },

  /**
   * Handle payment failure
   */
  async handlePaymentFailure(orderId: string, errorMessage: string): Promise<PaymentResponse> {
    try {
      const client = generateClient();

      const updateInput = {
        id: orderId,
        paymentStatus: 'FAILED',
        status: 'CANCELLED',
        notes: `Payment failed: ${errorMessage}`
      };

      const result = await client.graphql({
        query: updateOrder,
        variables: { input: updateInput }
      });

      console.log('Order marked as failed:', orderId);
      return {
        success: true,
        data: result.data?.updateOrder
      };

    } catch (error) {
      console.error('Error handling payment failure:', error);
      return {
        success: false,
        error: 'Failed to update order status'
      };
    }
  },

  /**
   * Razorpay configuration for client-side integration
   */
  getRazorpayConfig() {
    const keyId = process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID;

    if (!keyId) {
      throw new Error('Razorpay key ID not configured');
    }

    return {
      key: keyId,
      currency: 'INR',
      name: 'Customine',
      description: 'Curated Gift Boxes',
      image: '/logo.png',
      theme: {
        color: '#3B82F6'
      }
    };
  },

  /**
   * Load Razorpay script dynamically
   */
  loadRazorpayScript(): Promise<boolean> {
    return new Promise((resolve) => {
      // Check if script is already loaded
      if (typeof window !== 'undefined' && window.Razorpay) {
        resolve(true);
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://checkout.razorpay.com/v1/checkout.js';
      script.onload = () => resolve(true);
      script.onerror = () => resolve(false);
      document.body.appendChild(script);
    });
  }
};

// Type declarations for Razorpay
declare global {
  interface Window {
    Razorpay: any;
  }
}
