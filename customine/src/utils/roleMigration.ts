/**
 * Role Migration Utility
 * 
 * This utility helps migrate existing users to have the role property set.
 * Run this once to ensure all existing users have a default role.
 */

import { generateClient } from 'aws-amplify/api';
import { listUserProfiles } from '../graphql/queries';
import { updateUserProfile } from '../graphql/mutations';

const client = generateClient();

export interface MigrationResult {
  success: boolean;
  message: string;
  processed: number;
  updated: number;
  errors: string[];
}

/**
 * Migrate all existing users to have a default role
 */
export async function migrateUsersToHaveRoles(): Promise<MigrationResult> {
  const result: MigrationResult = {
    success: false,
    message: '',
    processed: 0,
    updated: 0,
    errors: []
  };

  try {
    console.log('🔄 Starting role migration for existing users...');

    // Get all user profiles
    const response = await client.graphql({
      query: listUserProfiles,
      variables: {
        limit: 1000 // Adjust based on your user count
      }
    });

    const users = (response.data as any)?.listUserProfiles?.items || [];
    result.processed = users.length;

    console.log(`📊 Found ${users.length} users to check`);

    if (users.length === 0) {
      result.success = true;
      result.message = 'No users found to migrate';
      return result;
    }

    // Process each user
    for (const user of users) {
      try {
        // Check if user already has a role
        if (user.role && user.role.trim() !== '') {
          console.log(`✅ User ${user.email} already has role: ${user.role}`);
          continue;
        }

        // Update user to have default 'user' role
        console.log(`🔧 Updating role for user: ${user.email}`);
        
        await client.graphql({
          query: updateUserProfile,
          variables: {
            input: {
              id: user.id,
              role: 'user'
            }
          }
        });

        result.updated++;
        console.log(`✅ Updated ${user.email} to have 'user' role`);

      } catch (userError) {
        const errorMsg = `Failed to update ${user.email}: ${userError instanceof Error ? userError.message : 'Unknown error'}`;
        console.error(`❌ ${errorMsg}`);
        result.errors.push(errorMsg);
      }
    }

    result.success = result.errors.length === 0;
    result.message = `Migration completed. Processed: ${result.processed}, Updated: ${result.updated}, Errors: ${result.errors.length}`;

    console.log(`🎉 ${result.message}`);
    
    if (result.errors.length > 0) {
      console.log('❌ Errors encountered:');
      result.errors.forEach(error => console.log(`  - ${error}`));
    }

    return result;

  } catch (error) {
    const errorMsg = `Migration failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
    console.error(`❌ ${errorMsg}`);
    
    result.success = false;
    result.message = errorMsg;
    result.errors.push(errorMsg);
    
    return result;
  }
}

/**
 * Check how many users need role migration
 */
export async function checkUsersNeedingRoleMigration(): Promise<{
  total: number;
  withRoles: number;
  needingRoles: number;
  users: Array<{ email: string; name: string; role?: string }>;
}> {
  try {
    console.log('🔍 Checking users needing role migration...');

    const response = await client.graphql({
      query: listUserProfiles,
      variables: {
        limit: 1000
      }
    });

    const users = (response.data as any)?.listUserProfiles?.items || [];
    
    const usersNeedingRoles = users.filter((user: any) => !user.role || user.role.trim() === '');
    const usersWithRoles = users.filter((user: any) => user.role && user.role.trim() !== '');

    const summary = {
      total: users.length,
      withRoles: usersWithRoles.length,
      needingRoles: usersNeedingRoles.length,
      users: users.map((user: any) => ({
        email: user.email,
        name: user.name,
        role: user.role
      }))
    };

    console.log(`📊 Migration Status:`);
    console.log(`  Total users: ${summary.total}`);
    console.log(`  Users with roles: ${summary.withRoles}`);
    console.log(`  Users needing roles: ${summary.needingRoles}`);

    return summary;

  } catch (error) {
    console.error('❌ Error checking users:', error);
    return {
      total: 0,
      withRoles: 0,
      needingRoles: 0,
      users: []
    };
  }
}

/**
 * Set a specific user as admin (useful for initial setup)
 */
export async function setFirstAdmin(email: string): Promise<{ success: boolean; message: string }> {
  try {
    console.log(`🔑 Setting ${email} as admin...`);

    // First check if user exists
    const response = await client.graphql({
      query: listUserProfiles,
      variables: {
        filter: {
          email: { eq: email }
        }
      }
    });

    const users = (response.data as any)?.listUserProfiles?.items || [];
    
    if (users.length === 0) {
      return {
        success: false,
        message: `User with email ${email} not found`
      };
    }

    const user = users[0];

    // Update to admin role
    await client.graphql({
      query: updateUserProfile,
      variables: {
        input: {
          id: user.id,
          role: 'admin'
        }
      }
    });

    return {
      success: true,
      message: `Successfully set ${user.name} (${email}) as admin`
    };

  } catch (error) {
    return {
      success: false,
      message: `Failed to set admin: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

// Export for console usage
if (typeof window !== 'undefined') {
  (window as any).roleMigration = {
    migrateUsersToHaveRoles,
    checkUsersNeedingRoleMigration,
    setFirstAdmin
  };
  
  console.log('🔧 Role Migration Helper loaded!');
  console.log('Available commands:');
  console.log('- window.roleMigration.checkUsersNeedingRoleMigration()');
  console.log('- window.roleMigration.migrateUsersToHaveRoles()');
  console.log('- window.roleMigration.setFirstAdmin("<EMAIL>")');
}
