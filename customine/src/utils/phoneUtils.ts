/**
 * Phone Number Utilities for AWS Cognito
 * Handles formatting and validation for Indian phone numbers
 */

/**
 * Format phone number to E.164 format for AWS Cognito
 * Supports Indian phone numbers with various input formats
 */
export function formatPhoneForCognito(phone: string): string {
  if (!phone) return '';
  
  // Remove all non-digit characters
  const digits = phone.replace(/\D/g, '');
  
  // Handle different Indian phone number formats
  if (digits.length === 10) {
    // 10 digits: assume Indian mobile number, add +91
    return `+91${digits}`;
  } else if (digits.length === 12 && digits.startsWith('91')) {
    // 12 digits starting with 91: add + prefix
    return `+${digits}`;
  } else if (digits.length === 13 && digits.startsWith('91')) {
    // 13 digits starting with 91: likely already has country code
    return `+${digits}`;
  } else if (phone.startsWith('+91') && digits.length === 12) {
    // Already in correct format
    return phone;
  } else if (phone.startsWith('+') && digits.length >= 10) {
    // International format with + prefix
    return phone;
  }
  
  // Default: assume Indian number and add +91
  return `+91${digits}`;
}

/**
 * Validate phone number format
 */
export function validatePhoneNumber(phone: string): { isValid: boolean; error?: string } {
  if (!phone) {
    return { isValid: true }; // Phone is optional
  }
  
  const digits = phone.replace(/\D/g, '');
  
  // Check for valid Indian mobile number patterns
  if (digits.length === 10) {
    // Indian mobile numbers start with 6, 7, 8, or 9
    if (!/^[6-9]/.test(digits)) {
      return {
        isValid: false,
        error: 'Indian mobile numbers should start with 6, 7, 8, or 9'
      };
    }
    return { isValid: true };
  } else if (digits.length === 12 && digits.startsWith('91')) {
    // Check if the mobile number part is valid
    const mobileNumber = digits.substring(2);
    if (!/^[6-9]/.test(mobileNumber)) {
      return {
        isValid: false,
        error: 'Indian mobile numbers should start with 6, 7, 8, or 9'
      };
    }
    return { isValid: true };
  } else if (phone.startsWith('+') && digits.length >= 10) {
    // International format - basic validation
    return { isValid: true };
  } else {
    return {
      isValid: false,
      error: 'Please enter a valid 10-digit mobile number'
    };
  }
}

/**
 * Format phone number for display (user-friendly format)
 */
export function formatPhoneForDisplay(phone: string): string {
  if (!phone) return '';
  
  const digits = phone.replace(/\D/g, '');
  
  if (digits.length === 10) {
    // Format as: 98765 43210
    return `${digits.substring(0, 5)} ${digits.substring(5)}`;
  } else if (digits.length === 12 && digits.startsWith('91')) {
    // Format as: +91 98765 43210
    const mobileNumber = digits.substring(2);
    return `+91 ${mobileNumber.substring(0, 5)} ${mobileNumber.substring(5)}`;
  }
  
  return phone;
}

/**
 * Clean phone number input (remove invalid characters)
 */
export function cleanPhoneInput(phone: string): string {
  // Allow digits, +, spaces, hyphens, and parentheses
  return phone.replace(/[^\d+\s\-()]/g, '');
}

/**
 * Get phone number placeholder text
 */
export function getPhonePlaceholder(): string {
  return '+91 98765 43210';
}

/**
 * Example phone numbers for help text
 */
export const phoneExamples = [
  '9876543210',
  '+91 9876543210',
  '+91-9876-543-210',
  '91 9876543210'
];
