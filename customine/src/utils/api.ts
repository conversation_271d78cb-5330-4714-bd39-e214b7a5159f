/**
 * AWS Amplify API Configuration for Customine
 * Centralized API exports for all functionality
 */

import { Amplify } from 'aws-amplify';
import awsconfig from '../aws-exports';
import {
  getProduct,
  listProducts,
  listCartItems,
  cartItemsByUserId
} from '../graphql/queries';
import {
  createProduct,
  updateProduct,
  deleteProduct,
  createCartItem,
  updateCartItem,
  deleteCartItem
} from '../graphql/mutations';

// Configure Amplify
Amplify.configure(awsconfig);

// Re-export all API modules
export * from './authAPI';
export * from './inquiryAPI';
export * from './orderAPI';

// Re-export GraphQL operations
export * from '../graphql';

/**
 * User Profile interface
 */
export interface UserProfile {
  id?: string;
  email: string;
  name: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  alternatePhone?: string;
  address?: string;
  addressLine2?: string;
  city?: string;
  state?: string;
  pincode?: string;
  country?: string;
  landmark?: string;
  dateOfBirth?: string;
  gender?: string;
  maritalStatus?: string;
  anniversary?: string;
  occupation?: string;
  company?: string;
  avatar?: string;
  bio?: string;
  website?: string;
  socialMedia?: Record<string, string>;
  preferences?: Record<string, unknown>;
  communicationPreferences?: Record<string, boolean>;
  language?: string;
  currency?: string;
  timezone?: string;
  businessName?: string;
  businessType?: string;
  gstNumber?: string;
  panNumber?: string;
  businessAddress?: string;
  businessPhone?: string;
  businessEmail?: string;
  tags?: string[];
  notes?: string;
  customerType?: string;
  loyaltyPoints?: number;
  totalSpent?: number;
  isActive?: boolean;
  isVerified?: boolean;
  isVIP?: boolean;
  allowMarketing?: boolean;
  role?: string;
  lastLoginAt?: string;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * User interface for authentication
 */
export interface User {
  id: string;
  name: string;
  email: string;
  phone?: string;
  profile?: UserProfile;
}

/**
 * Cart Item interface
 */
export interface CartItem {
  id: string;
  productId: string;
  name: string;
  price: number;
  image: string;
  quantity: number;
  userId: string;
}

/**
 * Product interface
 */
export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  originalPrice?: number;
  images: string[];
  category: string;
  inStock: boolean;
  createdAt: string;
  updatedAt: string;
}

// Product API
export const productAPI = {
  getAll: async (filter?: Record<string, unknown>, limit = 20) => {
    try {
      const { generateClient } = await import('aws-amplify/api');
      const client = generateClient();
      const result = await client.graphql({
        query: listProducts,
        variables: { filter, limit }
      });
      return result;
    } catch (error) {
      console.error('Error fetching products:', error);
      throw error;
    }
  },

  getBySlug: async (slug: string) => {
    try {
      const { generateClient } = await import('aws-amplify/api');
      const client = generateClient();
      const result = await client.graphql({
        query: getProduct,
        variables: { slug }
      });
      return result;
    } catch (error) {
      console.error('Error fetching product by slug:', error);
      throw error;
    }
  },

  getByCategory: async (category: string, limit = 20) => {
    try {
      const { generateClient } = await import('aws-amplify/api');
      const client = generateClient();
      const result = await client.graphql({
        query: listProducts,
        variables: {
          filter: { category: { eq: category }, isActive: { eq: true } },
          limit
        }
      });
      return result;
    } catch (error) {
      console.error('Error fetching products by category:', error);
      throw error;
    }
  },

  create: async (productData: {
    name: string;
    description: string;
    price: number;
    originalPrice?: number;
    images: string[];
    category: string;
    inStock: boolean;
    slug: string;
    isActive: boolean;
  }) => {
    try {
      const { generateClient } = await import('aws-amplify/api');
      const client = generateClient();
      const result = await client.graphql({
        query: createProduct,
        variables: { input: productData }
      });
      return result;
    } catch (error) {
      console.error('Error creating product:', error);
      throw error;
    }
  },

  update: async (id: string, updates: Partial<{
    name: string;
    description: string;
    price: number;
    originalPrice?: number;
    images: string[];
    category: string;
    inStock: boolean;
    slug: string;
    isActive: boolean;
  }>) => {
    try {
      const { generateClient } = await import('aws-amplify/api');
      const client = generateClient();
      const result = await client.graphql({
        query: updateProduct,
        variables: { input: { id, ...updates } }
      });
      return result;
    } catch (error) {
      console.error('Error updating product:', error);
      throw error;
    }
  },

  delete: async (id: string) => {
    try {
      const { generateClient } = await import('aws-amplify/api');
      const client = generateClient();
      const result = await client.graphql({
        query: deleteProduct,
        variables: { input: { id } }
      });
      return result;
    } catch (error) {
      console.error('Error deleting product:', error);
      throw error;
    }
  }
};

// Cart API
export const cartAPI = {
  getItems: async (userId: string) => {
    try {
      const { generateClient } = await import('aws-amplify/api');
      const client = generateClient();
      const result = await client.graphql({
        query: cartItemsByUserId,
        variables: { userId }
      });
      console.log('Cart items fetched successfully:', result.data);
      return result;
    } catch (error) {
      console.error('Error fetching cart items:', error);
      console.error('Error details:', JSON.stringify(error, null, 2));
      throw error;
    }
  },

  addItem: async (cartItem: {
    userId: string;
    productId: string;
    quantity: number;
    price: number;
    giftType?: string;
  }) => {
    try {
      const { generateClient } = await import('aws-amplify/api');
      const client = generateClient();
      const result = await client.graphql({
        query: createCartItem,
        variables: { input: cartItem }
      });
      return result;
    } catch (error) {
      console.error('Error adding item to cart:', error);
      throw error;
    }
  },

  updateItem: async (id: string, updates: { quantity?: number; giftType?: string }) => {
    try {
      const { generateClient } = await import('aws-amplify/api');
      const client = generateClient();
      const result = await client.graphql({
        query: updateCartItem,
        variables: { input: { id, ...updates } }
      });
      return result;
    } catch (error) {
      console.error('Error updating cart item:', error);
      throw error;
    }
  },

  removeItem: async (id: string) => {
    try {
      const { generateClient } = await import('aws-amplify/api');
      const client = generateClient();
      const result = await client.graphql({
        query: deleteCartItem,
        variables: { input: { id } }
      });
      return result;
    } catch (error) {
      console.error('Error removing cart item:', error);
      throw error;
    }
  }
};

// Dashboard API
export const dashboardAPI = {
  getStats: async () => {
    try {
      const { generateClient } = await import('aws-amplify/api');
      const client = generateClient();
      const result = await client.graphql({
        query: GET_DASHBOARD_STATS
      });

      // Process the data to return useful statistics
      const data = result.data as any;
      const products = data?.listProducts?.items || [];
      const cartItems = data?.listCartItems?.items || [];

      const stats = {
        totalProducts: products.length,
        activeProducts: products.filter((p: any) => p.isActive).length,
        inactiveProducts: products.filter((p: any) => !p.isActive).length,
        totalCartItems: cartItems.length,
        totalCartValue: cartItems.reduce((sum: number, item: any) => sum + (item.price * item.quantity), 0),
        averageCartValue: cartItems.length > 0
          ? cartItems.reduce((sum: number, item: any) => sum + (item.price * item.quantity), 0) / cartItems.length
          : 0,
        recentActivity: {
          products: products.slice(0, 5),
          cartItems: cartItems.slice(0, 10)
        }
      };

      return { success: true, data: stats };
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      return { success: false, error: 'Failed to fetch dashboard statistics' };
    }
  },

  getProductAnalytics: async () => {
    try {
      const { generateClient } = await import('aws-amplify/api');
      const client = generateClient();
      const result = await client.graphql({
        query: listProducts
      });

      const data = result.data as any;
      const products = data?.listProducts?.items || [];

      // Analyze products by category
      const categoryStats = products.reduce((acc: Record<string, number>, product: any) => {
        acc[product.category] = (acc[product.category] || 0) + 1;
        return acc;
      }, {});

      // Price range analysis
      const priceRanges = {
        under1000: products.filter((p: any) => p.price < 1000).length,
        range1000to5000: products.filter((p: any) => p.price >= 1000 && p.price < 5000).length,
        range5000to10000: products.filter((p: any) => p.price >= 5000 && p.price < 10000).length,
        above10000: products.filter((p: any) => p.price >= 10000).length
      };

      return {
        success: true,
        data: {
          categoryStats,
          priceRanges,
          totalProducts: products.length,
          averagePrice: products.length > 0
            ? products.reduce((sum: number, p: any) => sum + p.price, 0) / products.length
            : 0
        }
      };
    } catch (error) {
      console.error('Error fetching product analytics:', error);
      return { success: false, error: 'Failed to fetch product analytics' };
    }
  },

  getCartAnalytics: async () => {
    try {
      const { generateClient } = await import('aws-amplify/api');
      const client = generateClient();
      const result = await client.graphql({
        query: listCartItems,
        variables: { limit: 1000 } // Get all cart items
      });

      const data = result.data as any;
      const cartItems = data?.listCartItems?.items || [];

      // Group by user
      const userCarts = cartItems.reduce((acc: Record<string, any[]>, item: any) => {
        if (!acc[item.userId]) acc[item.userId] = [];
        acc[item.userId].push(item);
        return acc;
      }, {});

      const analytics = {
        totalUsers: Object.keys(userCarts).length,
        totalItems: cartItems.length,
        averageItemsPerUser: Object.keys(userCarts).length > 0
          ? cartItems.length / Object.keys(userCarts).length
          : 0,
        totalValue: cartItems.reduce((sum: number, item: any) => sum + (item.price * item.quantity), 0),
        mostPopularProducts: {} // Would need more complex aggregation
      };

      return { success: true, data: analytics };
    } catch (error) {
      console.error('Error fetching cart analytics:', error);
      return { success: false, error: 'Failed to fetch cart analytics' };
    }
  }
};
