{"data": {"__schema": {"queryType": {"name": "Query"}, "mutationType": {"name": "Mutation"}, "subscriptionType": {"name": "Subscription"}, "types": [{"kind": "OBJECT", "name": "Query", "description": null, "fields": [{"name": "getTodo", "description": null, "args": [{"name": "id", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "Todo", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "listTodos", "description": null, "args": [{"name": "filter", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "ModelTodoFilterInput", "ofType": null}, "defaultValue": null}, {"name": "limit", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null}, {"name": "nextToken", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "ModelTodoConnection", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "Todo", "description": null, "fields": [{"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "createdAt", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "AWSDateTime", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "updatedAt", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "AWSDateTime", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "ID", "description": "Built-in ID", "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "String", "description": "Built-in String", "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "AWSDateTime", "description": "The `AWSDateTime` scalar type provided by AWS AppSync, represents a valid ***extended*** [ISO 8601 DateTime](https://en.wikipedia.org/wiki/ISO_8601#Combined_date_and_time_representations) string. In other words, this scalar type accepts datetime strings of the form `YYYY-MM-DDThh:mm:ss.SSSZ`.  The scalar can also accept \"negative years\" of the form `-YYYY` which correspond to years before `0000`. For example, \"**-2017-01-01T00:00Z**\" and \"**-9999-01-01T00:00Z**\" are both valid datetime strings.  The field after the two digit seconds field is a nanoseconds field. It can accept between 1 and 9 digits. So, for example, \"**1970-01-01T12:00:00.2Z**\", \"**1970-01-01T12:00:00.277Z**\" and \"**1970-01-01T12:00:00.123456789Z**\" are all valid datetime strings.  The seconds and nanoseconds fields are optional (the seconds field must be specified if the nanoseconds field is to be used).  The [time zone offset](https://en.wikipedia.org/wiki/ISO_8601#Time_zone_designators) is compulsory for this scalar. The time zone offset must either be `Z` (representing the UTC time zone) or be in the format `±hh:mm:ss`. The seconds field in the timezone offset will be considered valid even though it is not part of the ISO 8601 standard.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "ModelTodoConnection", "description": null, "fields": [{"name": "items", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "OBJECT", "name": "Todo", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "nextToken", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "ModelTodoFilterInput", "description": null, "fields": null, "inputFields": [{"name": "id", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "ModelIDInput", "ofType": null}, "defaultValue": null}, {"name": "name", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "ModelStringInput", "ofType": null}, "defaultValue": null}, {"name": "description", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "ModelStringInput", "ofType": null}, "defaultValue": null}, {"name": "createdAt", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "ModelStringInput", "ofType": null}, "defaultValue": null}, {"name": "updatedAt", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "ModelStringInput", "ofType": null}, "defaultValue": null}, {"name": "and", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "ModelTodoFilterInput", "ofType": null}}, "defaultValue": null}, {"name": "or", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "ModelTodoFilterInput", "ofType": null}}, "defaultValue": null}, {"name": "not", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "ModelTodoFilterInput", "ofType": null}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "ModelIDInput", "description": null, "fields": null, "inputFields": [{"name": "ne", "description": null, "type": {"kind": "SCALAR", "name": "ID", "ofType": null}, "defaultValue": null}, {"name": "eq", "description": null, "type": {"kind": "SCALAR", "name": "ID", "ofType": null}, "defaultValue": null}, {"name": "le", "description": null, "type": {"kind": "SCALAR", "name": "ID", "ofType": null}, "defaultValue": null}, {"name": "lt", "description": null, "type": {"kind": "SCALAR", "name": "ID", "ofType": null}, "defaultValue": null}, {"name": "ge", "description": null, "type": {"kind": "SCALAR", "name": "ID", "ofType": null}, "defaultValue": null}, {"name": "gt", "description": null, "type": {"kind": "SCALAR", "name": "ID", "ofType": null}, "defaultValue": null}, {"name": "contains", "description": null, "type": {"kind": "SCALAR", "name": "ID", "ofType": null}, "defaultValue": null}, {"name": "notContains", "description": null, "type": {"kind": "SCALAR", "name": "ID", "ofType": null}, "defaultValue": null}, {"name": "between", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null}, {"name": "beginsWith", "description": null, "type": {"kind": "SCALAR", "name": "ID", "ofType": null}, "defaultValue": null}, {"name": "attributeExists", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": null}, {"name": "attributeType", "description": null, "type": {"kind": "ENUM", "name": "ModelAttributeTypes", "ofType": null}, "defaultValue": null}, {"name": "size", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "ModelSizeInput", "ofType": null}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "Boolean", "description": "Built-in Boolean", "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "ModelAttributeTypes", "description": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "binary", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "binarySet", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "bool", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "list", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "map", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "number", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "numberSet", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "string", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "stringSet", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "_null", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "ModelSizeInput", "description": null, "fields": null, "inputFields": [{"name": "ne", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null}, {"name": "eq", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null}, {"name": "le", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null}, {"name": "lt", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null}, {"name": "ge", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null}, {"name": "gt", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null}, {"name": "between", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "Int", "description": "Built-in Int", "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "ModelStringInput", "description": null, "fields": null, "inputFields": [{"name": "ne", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "eq", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "le", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "lt", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "ge", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "gt", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "contains", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "notContains", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "between", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}, {"name": "beginsWith", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "attributeExists", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": null}, {"name": "attributeType", "description": null, "type": {"kind": "ENUM", "name": "ModelAttributeTypes", "ofType": null}, "defaultValue": null}, {"name": "size", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "ModelSizeInput", "ofType": null}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "Mutation", "description": null, "fields": [{"name": "createTodo", "description": null, "args": [{"name": "input", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "CreateTodoInput", "ofType": null}}, "defaultValue": null}, {"name": "condition", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "ModelTodoConditionInput", "ofType": null}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "Todo", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "updateTodo", "description": null, "args": [{"name": "input", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "UpdateTodoInput", "ofType": null}}, "defaultValue": null}, {"name": "condition", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "ModelTodoConditionInput", "ofType": null}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "Todo", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "deleteTodo", "description": null, "args": [{"name": "input", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "DeleteTodoInput", "ofType": null}}, "defaultValue": null}, {"name": "condition", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "ModelTodoConditionInput", "ofType": null}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "Todo", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "CreateTodoInput", "description": null, "fields": null, "inputFields": [{"name": "id", "description": null, "type": {"kind": "SCALAR", "name": "ID", "ofType": null}, "defaultValue": null}, {"name": "name", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}, {"name": "description", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "ModelTodoConditionInput", "description": null, "fields": null, "inputFields": [{"name": "name", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "ModelStringInput", "ofType": null}, "defaultValue": null}, {"name": "description", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "ModelStringInput", "ofType": null}, "defaultValue": null}, {"name": "and", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "ModelTodoConditionInput", "ofType": null}}, "defaultValue": null}, {"name": "or", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "ModelTodoConditionInput", "ofType": null}}, "defaultValue": null}, {"name": "not", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "ModelTodoConditionInput", "ofType": null}, "defaultValue": null}, {"name": "createdAt", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "ModelStringInput", "ofType": null}, "defaultValue": null}, {"name": "updatedAt", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "ModelStringInput", "ofType": null}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "UpdateTodoInput", "description": null, "fields": null, "inputFields": [{"name": "id", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null}, {"name": "name", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "description", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "DeleteTodoInput", "description": null, "fields": null, "inputFields": [{"name": "id", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "Subscription", "description": null, "fields": [{"name": "onCreateTodo", "description": null, "args": [{"name": "filter", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "ModelSubscriptionTodoFilterInput", "ofType": null}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "Todo", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "onUpdateTodo", "description": null, "args": [{"name": "filter", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "ModelSubscriptionTodoFilterInput", "ofType": null}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "Todo", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "onDeleteTodo", "description": null, "args": [{"name": "filter", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "ModelSubscriptionTodoFilterInput", "ofType": null}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "Todo", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "ModelSubscriptionTodoFilterInput", "description": null, "fields": null, "inputFields": [{"name": "id", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "ModelSubscriptionIDInput", "ofType": null}, "defaultValue": null}, {"name": "name", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "ModelSubscriptionStringInput", "ofType": null}, "defaultValue": null}, {"name": "description", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "ModelSubscriptionStringInput", "ofType": null}, "defaultValue": null}, {"name": "createdAt", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "ModelSubscriptionStringInput", "ofType": null}, "defaultValue": null}, {"name": "updatedAt", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "ModelSubscriptionStringInput", "ofType": null}, "defaultValue": null}, {"name": "and", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "ModelSubscriptionTodoFilterInput", "ofType": null}}, "defaultValue": null}, {"name": "or", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "ModelSubscriptionTodoFilterInput", "ofType": null}}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "ModelSubscriptionIDInput", "description": null, "fields": null, "inputFields": [{"name": "ne", "description": null, "type": {"kind": "SCALAR", "name": "ID", "ofType": null}, "defaultValue": null}, {"name": "eq", "description": null, "type": {"kind": "SCALAR", "name": "ID", "ofType": null}, "defaultValue": null}, {"name": "le", "description": null, "type": {"kind": "SCALAR", "name": "ID", "ofType": null}, "defaultValue": null}, {"name": "lt", "description": null, "type": {"kind": "SCALAR", "name": "ID", "ofType": null}, "defaultValue": null}, {"name": "ge", "description": null, "type": {"kind": "SCALAR", "name": "ID", "ofType": null}, "defaultValue": null}, {"name": "gt", "description": null, "type": {"kind": "SCALAR", "name": "ID", "ofType": null}, "defaultValue": null}, {"name": "contains", "description": null, "type": {"kind": "SCALAR", "name": "ID", "ofType": null}, "defaultValue": null}, {"name": "notContains", "description": null, "type": {"kind": "SCALAR", "name": "ID", "ofType": null}, "defaultValue": null}, {"name": "between", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null}, {"name": "beginsWith", "description": null, "type": {"kind": "SCALAR", "name": "ID", "ofType": null}, "defaultValue": null}, {"name": "in", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null}, {"name": "notIn", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "ModelSubscriptionStringInput", "description": null, "fields": null, "inputFields": [{"name": "ne", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "eq", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "le", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "lt", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "ge", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "gt", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "contains", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "notContains", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "between", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}, {"name": "beginsWith", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "in", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}, {"name": "notIn", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "ModelSortDirection", "description": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "ASC", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "DESC", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "ModelIntInput", "description": null, "fields": null, "inputFields": [{"name": "ne", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null}, {"name": "eq", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null}, {"name": "le", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null}, {"name": "lt", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null}, {"name": "ge", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null}, {"name": "gt", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null}, {"name": "between", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null}, {"name": "attributeExists", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": null}, {"name": "attributeType", "description": null, "type": {"kind": "ENUM", "name": "ModelAttributeTypes", "ofType": null}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "ModelFloatInput", "description": null, "fields": null, "inputFields": [{"name": "ne", "description": null, "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null}, {"name": "eq", "description": null, "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null}, {"name": "le", "description": null, "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null}, {"name": "lt", "description": null, "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null}, {"name": "ge", "description": null, "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null}, {"name": "gt", "description": null, "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null}, {"name": "between", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "defaultValue": null}, {"name": "attributeExists", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": null}, {"name": "attributeType", "description": null, "type": {"kind": "ENUM", "name": "ModelAttributeTypes", "ofType": null}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "Float", "description": "Built-in Float", "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "ModelSubscriptionBooleanInput", "description": null, "fields": null, "inputFields": [{"name": "ne", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": null}, {"name": "eq", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "ModelSubscriptionIntInput", "description": null, "fields": null, "inputFields": [{"name": "ne", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null}, {"name": "eq", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null}, {"name": "le", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null}, {"name": "lt", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null}, {"name": "ge", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null}, {"name": "gt", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null}, {"name": "between", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null}, {"name": "in", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null}, {"name": "notIn", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "ModelBooleanInput", "description": null, "fields": null, "inputFields": [{"name": "ne", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": null}, {"name": "eq", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": null}, {"name": "attributeExists", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": null}, {"name": "attributeType", "description": null, "type": {"kind": "ENUM", "name": "ModelAttributeTypes", "ofType": null}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "ModelSubscriptionFloatInput", "description": null, "fields": null, "inputFields": [{"name": "ne", "description": null, "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null}, {"name": "eq", "description": null, "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null}, {"name": "le", "description": null, "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null}, {"name": "lt", "description": null, "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null}, {"name": "ge", "description": null, "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null}, {"name": "gt", "description": null, "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null}, {"name": "between", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "defaultValue": null}, {"name": "in", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "defaultValue": null}, {"name": "notIn", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "__<PERSON><PERSON><PERSON>", "description": "A GraphQL Introspection defines the capabilities of a GraphQL server. It exposes all available types and directives on the server, the entry points for query, mutation, and subscription operations.", "fields": [{"name": "types", "description": "A list of all types supported by this server.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "queryType", "description": "The type that query operations will be rooted at.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "mutationType", "description": "If this server supports mutation, the type that mutation operations will be rooted at.", "args": [], "type": {"kind": "OBJECT", "name": "__Type", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "directives", "description": "'A list of all directives supported by this server.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Directive", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "subscriptionType", "description": "'If this server support subscription, the type that subscription operations will be rooted at.", "args": [], "type": {"kind": "OBJECT", "name": "__Type", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "__Type", "description": null, "fields": [{"name": "kind", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "__TypeKind", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "fields", "description": null, "args": [{"name": "includeDeprecated", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false"}], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Field", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "interfaces", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "possibleTypes", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "enum<PERSON><PERSON><PERSON>", "description": null, "args": [{"name": "includeDeprecated", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false"}], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__<PERSON>umV<PERSON><PERSON>", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "inputFields", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__InputValue", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "ofType", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "__Type", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "__TypeKind", "description": "An enum describing what kind of type a given __Type is", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "SCALAR", "description": "Indicates this type is a scalar.", "isDeprecated": false, "deprecationReason": null}, {"name": "OBJECT", "description": "Indicates this type is an object. `fields` and `interfaces` are valid fields.", "isDeprecated": false, "deprecationReason": null}, {"name": "INTERFACE", "description": "Indicates this type is an interface. `fields` and `possibleTypes` are valid fields.", "isDeprecated": false, "deprecationReason": null}, {"name": "UNION", "description": "Indicates this type is a union. `possibleTypes` is a valid field.", "isDeprecated": false, "deprecationReason": null}, {"name": "ENUM", "description": "Indicates this type is an enum. `enumValues` is a valid field.", "isDeprecated": false, "deprecationReason": null}, {"name": "INPUT_OBJECT", "description": "Indicates this type is an input object. `inputFields` is a valid field.", "isDeprecated": false, "deprecationReason": null}, {"name": "LIST", "description": "Indicates this type is a list. `ofType` is a valid field.", "isDeprecated": false, "deprecationReason": null}, {"name": "NON_NULL", "description": "Indicates this type is a non-null. `ofType` is a valid field.", "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "OBJECT", "name": "__Field", "description": null, "fields": [{"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "args", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__InputValue", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "type", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "isDeprecated", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "deprecationReason", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "__InputValue", "description": null, "fields": [{"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "type", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "defaultValue", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "__<PERSON>umV<PERSON><PERSON>", "description": null, "fields": [{"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "isDeprecated", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "deprecationReason", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "__Directive", "description": null, "fields": [{"name": "name", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "locations", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "__DirectiveLocation", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "args", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__InputValue", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "onOperation", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": true, "deprecationReason": "Use `locations`."}, {"name": "onFragment", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": true, "deprecationReason": "Use `locations`."}, {"name": "onField", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": true, "deprecationReason": "Use `locations`."}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "__DirectiveLocation", "description": "An enum describing valid locations where a directive can be placed", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "QUERY", "description": "Indicates the directive is valid on queries.", "isDeprecated": false, "deprecationReason": null}, {"name": "MUTATION", "description": "Indicates the directive is valid on mutations.", "isDeprecated": false, "deprecationReason": null}, {"name": "FIELD", "description": "Indicates the directive is valid on fields.", "isDeprecated": false, "deprecationReason": null}, {"name": "FRAGMENT_DEFINITION", "description": "Indicates the directive is valid on fragment definitions.", "isDeprecated": false, "deprecationReason": null}, {"name": "FRAGMENT_SPREAD", "description": "Indicates the directive is valid on fragment spreads.", "isDeprecated": false, "deprecationReason": null}, {"name": "INLINE_FRAGMENT", "description": "Indicates the directive is valid on inline fragments.", "isDeprecated": false, "deprecationReason": null}, {"name": "SCHEMA", "description": "Indicates the directive is valid on a schema SDL definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "SCALAR", "description": "Indicates the directive is valid on a scalar SDL definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "OBJECT", "description": "Indicates the directive is valid on an object SDL definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "FIELD_DEFINITION", "description": "Indicates the directive is valid on a field SDL definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "ARGUMENT_DEFINITION", "description": "Indicates the directive is valid on a field argument SDL definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "INTERFACE", "description": "Indicates the directive is valid on an interface SDL definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "UNION", "description": "Indicates the directive is valid on an union SDL definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "ENUM", "description": "Indicates the directive is valid on an enum SDL definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "ENUM_VALUE", "description": "Indicates the directive is valid on an enum value SDL definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "INPUT_OBJECT", "description": "Indicates the directive is valid on an input object SDL definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "INPUT_FIELD_DEFINITION", "description": "Indicates the directive is valid on an input object field SDL definition.", "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}], "directives": [{"name": "include", "description": "Directs the executor to include this field or fragment only when the `if` argument is true", "locations": ["FIELD", "FRAGMENT_SPREAD", "INLINE_FRAGMENT"], "args": [{"name": "if", "description": "Included when true.", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "defaultValue": null}], "onOperation": false, "onFragment": true, "onField": true}, {"name": "skip", "description": "Directs the executor to skip this field or fragment when the `if`'argument is true.", "locations": ["FIELD", "FRAGMENT_SPREAD", "INLINE_FRAGMENT"], "args": [{"name": "if", "description": "Skipped when true.", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "defaultValue": null}], "onOperation": false, "onFragment": true, "onField": true}, {"name": "defer", "description": "This directive allows results to be deferred during execution", "locations": ["FIELD"], "args": [], "onOperation": false, "onFragment": false, "onField": true}, {"name": "aws_oidc", "description": "Tells the service this field/object has access authorized by an OIDC token.", "locations": ["OBJECT", "FIELD_DEFINITION"], "args": [], "onOperation": false, "onFragment": false, "onField": false}, {"name": "aws_subscribe", "description": "Tells the service which mutation triggers this subscription.", "locations": ["FIELD_DEFINITION"], "args": [{"name": "mutations", "description": "List of mutations which will trigger this subscription when they are called.", "type": {"kind": "LIST", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}], "onOperation": false, "onFragment": false, "onField": false}, {"name": "aws_cognito_user_pools", "description": "Tells the service this field/object has access authorized by a Cognito User Pools token.", "locations": ["OBJECT", "FIELD_DEFINITION"], "args": [{"name": "cognito_groups", "description": "List of cognito user pool groups which have access on this field", "type": {"kind": "LIST", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}], "onOperation": false, "onFragment": false, "onField": false}, {"name": "aws_publish", "description": "Tells the service which subscriptions will be published to when this mutation is called. This directive is deprecated use @aws_susbscribe directive instead.", "locations": ["FIELD_DEFINITION"], "args": [{"name": "subscriptions", "description": "List of subscriptions which will be published to when this mutation is called.", "type": {"kind": "LIST", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}], "onOperation": false, "onFragment": false, "onField": false}, {"name": "aws_auth", "description": "Directs the schema to enforce authorization on a field", "locations": ["FIELD_DEFINITION"], "args": [{"name": "cognito_groups", "description": "List of cognito user pool groups which have access on this field", "type": {"kind": "LIST", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}], "onOperation": false, "onFragment": false, "onField": false}, {"name": "aws_iam", "description": "Tells the service this field/object has access authorized by sigv4 signing.", "locations": ["OBJECT", "FIELD_DEFINITION"], "args": [], "onOperation": false, "onFragment": false, "onField": false}, {"name": "deprecated", "description": null, "locations": ["FIELD_DEFINITION", "ENUM_VALUE"], "args": [{"name": "reason", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": "\"No longer supported\""}], "onOperation": false, "onFragment": false, "onField": false}, {"name": "aws_api_key", "description": "Tells the service this field/object has access authorized by an API key.", "locations": ["OBJECT", "FIELD_DEFINITION"], "args": [], "onOperation": false, "onFragment": false, "onField": false}, {"name": "aws_lambda", "description": "Tells the service this field/object has access authorized by a Lambda Authorizer.", "locations": ["OBJECT", "FIELD_DEFINITION"], "args": [], "onOperation": false, "onFragment": false, "onField": false}]}}}