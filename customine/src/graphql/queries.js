/* eslint-disable */
// this is an auto generated file. This will be overwritten

export const getUserProfile = /* GraphQL */ `
  query GetUserProfile($id: ID!) {
    getUserProfile(id: $id) {
      id
      email
      name
      firstName
      lastName
      phone
      alternatePhone
      address
      addressLine2
      city
      state
      pincode
      country
      landmark
      dateOfBirth
      gender
      maritalStatus
      anniversary
      occupation
      company
      avatar
      bio
      website
      socialMedia
      preferences
      communicationPreferences
      language
      currency
      timezone
      businessName
      businessType
      gstNumber
      panNumber
      businessAddress
      businessPhone
      businessEmail
      tags
      notes
      customerType
      loyaltyPoints
      totalSpent
      lastOrderDate
      isActive
      isVerified
      isVIP
      allowMarketing
      role
      createdAt
      updatedAt
      lastLoginAt
      cartItems {
        nextToken
        __typename
      }
      orders {
        nextToken
        __typename
      }
      inquiries {
        nextToken
        __typename
      }
      reviews {
        nextToken
        __typename
      }
      __typename
    }
  }
`;
export const listUserProfiles = /* GraphQL */ `
  query ListUserProfiles(
    $filter: ModelUserProfileFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listUserProfiles(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        email
        name
        firstName
        lastName
        phone
        alternatePhone
        address
        addressLine2
        city
        state
        pincode
        country
        landmark
        dateOfBirth
        gender
        maritalStatus
        anniversary
        occupation
        company
        avatar
        bio
        website
        socialMedia
        preferences
        communicationPreferences
        language
        currency
        timezone
        businessName
        businessType
        gstNumber
        panNumber
        businessAddress
        businessPhone
        businessEmail
        tags
        notes
        customerType
        loyaltyPoints
        totalSpent
        lastOrderDate
        isActive
        isVerified
        isVIP
        allowMarketing
        role
        createdAt
        updatedAt
        lastLoginAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const getGallery = /* GraphQL */ `
  query GetGallery($id: ID!) {
    getGallery(id: $id) {
      id
      title
      description
      imageKey
      imageUrl
      category
      subcategory
      tags
      isActive
      sortOrder
      altText
      metaTitle
      metaDescription
      createdBy
      updatedBy
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const listGalleries = /* GraphQL */ `
  query ListGalleries(
    $filter: ModelGalleryFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listGalleries(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        title
        description
        imageKey
        imageUrl
        category
        subcategory
        tags
        isActive
        sortOrder
        altText
        metaTitle
        metaDescription
        createdBy
        updatedBy
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const getGalleryStats = /* GraphQL */ `
  query GetGalleryStats($id: ID!) {
    getGalleryStats(id: $id) {
      id
      category
      totalImages
      activeImages
      lastUpdated
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const listGalleryStats = /* GraphQL */ `
  query ListGalleryStats(
    $filter: ModelGalleryStatsFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listGalleryStats(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        category
        totalImages
        activeImages
        lastUpdated
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const getProduct = /* GraphQL */ `
  query GetProduct($id: ID!) {
    getProduct(id: $id) {
      id
      name
      description
      shortDescription
      price
      originalPrice
      images
      category
      subcategory
      tags
      inStock
      stockQuantity
      slug
      sku
      weight
      dimensions
      luxuryDescription
      budgetDescription
      narration
      features
      luxuryFeatures
      budgetFeatures
      specifications
      materials
      careInstructions
      warranty
      shippingInfo
      returnPolicy
      rating
      reviewCount
      badge
      relatedProductIds
      metaTitle
      metaDescription
      isActive
      isFeatured
      sortOrder
      createdBy
      createdAt
      updatedAt
      cartItems {
        nextToken
        __typename
      }
      orderItems {
        nextToken
        __typename
      }
      reviews {
        nextToken
        __typename
      }
      __typename
    }
  }
`;
export const listProducts = /* GraphQL */ `
  query ListProducts(
    $filter: ModelProductFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listProducts(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        name
        description
        shortDescription
        price
        originalPrice
        images
        category
        subcategory
        tags
        inStock
        stockQuantity
        slug
        sku
        weight
        dimensions
        luxuryDescription
        budgetDescription
        narration
        features
        luxuryFeatures
        budgetFeatures
        specifications
        materials
        careInstructions
        warranty
        shippingInfo
        returnPolicy
        rating
        reviewCount
        badge
        relatedProductIds
        metaTitle
        metaDescription
        isActive
        isFeatured
        sortOrder
        createdBy
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const getCartItem = /* GraphQL */ `
  query GetCartItem($id: ID!) {
    getCartItem(id: $id) {
      id
      userId
      productId
      quantity
      price
      giftType
      customization
      notes
      createdAt
      updatedAt
      user {
        id
        email
        name
        firstName
        lastName
        phone
        alternatePhone
        address
        addressLine2
        city
        state
        pincode
        country
        landmark
        dateOfBirth
        gender
        maritalStatus
        anniversary
        occupation
        company
        avatar
        bio
        website
        socialMedia
        preferences
        communicationPreferences
        language
        currency
        timezone
        businessName
        businessType
        gstNumber
        panNumber
        businessAddress
        businessPhone
        businessEmail
        tags
        notes
        customerType
        loyaltyPoints
        totalSpent
        lastOrderDate
        isActive
        isVerified
        isVIP
        allowMarketing
        role
        createdAt
        updatedAt
        lastLoginAt
        __typename
      }
      product {
        id
        name
        description
        shortDescription
        price
        originalPrice
        images
        category
        subcategory
        tags
        inStock
        stockQuantity
        slug
        sku
        weight
        dimensions
        luxuryDescription
        budgetDescription
        narration
        features
        luxuryFeatures
        budgetFeatures
        specifications
        materials
        careInstructions
        warranty
        shippingInfo
        returnPolicy
        rating
        reviewCount
        badge
        relatedProductIds
        metaTitle
        metaDescription
        isActive
        isFeatured
        sortOrder
        createdBy
        createdAt
        updatedAt
        __typename
      }
      __typename
    }
  }
`;
export const listCartItems = /* GraphQL */ `
  query ListCartItems(
    $filter: ModelCartItemFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listCartItems(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        userId
        productId
        quantity
        price
        giftType
        customization
        notes
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const getOrder = /* GraphQL */ `
  query GetOrder($id: ID!) {
    getOrder(id: $id) {
      id
      userId
      orderNumber
      status
      subtotal
      tax
      shipping
      discount
      total
      currency
      shippingAddress {
        name
        phone
        email
        addressLine1
        addressLine2
        landmark
        city
        state
        pincode
        country
        addressType
        __typename
      }
      billingAddress {
        name
        phone
        email
        addressLine1
        addressLine2
        landmark
        city
        state
        pincode
        country
        addressType
        __typename
      }
      paymentMethod
      paymentStatus
      paymentId
      razorpayOrderId
      razorpayPaymentId
      trackingNumber
      estimatedDelivery
      deliveredAt
      customerNotes
      adminNotes
      giftMessage
      specialInstructions
      createdAt
      updatedAt
      user {
        id
        email
        name
        firstName
        lastName
        phone
        alternatePhone
        address
        addressLine2
        city
        state
        pincode
        country
        landmark
        dateOfBirth
        gender
        maritalStatus
        anniversary
        occupation
        company
        avatar
        bio
        website
        socialMedia
        preferences
        communicationPreferences
        language
        currency
        timezone
        businessName
        businessType
        gstNumber
        panNumber
        businessAddress
        businessPhone
        businessEmail
        tags
        notes
        customerType
        loyaltyPoints
        totalSpent
        lastOrderDate
        isActive
        isVerified
        isVIP
        allowMarketing
        role
        createdAt
        updatedAt
        lastLoginAt
        __typename
      }
      items {
        nextToken
        __typename
      }
      __typename
    }
  }
`;
export const listOrders = /* GraphQL */ `
  query ListOrders(
    $filter: ModelOrderFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listOrders(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        userId
        orderNumber
        status
        subtotal
        tax
        shipping
        discount
        total
        currency
        paymentMethod
        paymentStatus
        paymentId
        razorpayOrderId
        razorpayPaymentId
        trackingNumber
        estimatedDelivery
        deliveredAt
        customerNotes
        adminNotes
        giftMessage
        specialInstructions
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const getOrderItem = /* GraphQL */ `
  query GetOrderItem($id: ID!) {
    getOrderItem(id: $id) {
      id
      orderId
      productId
      userId
      quantity
      price
      giftType
      customization
      notes
      createdAt
      updatedAt
      order {
        id
        userId
        orderNumber
        status
        subtotal
        tax
        shipping
        discount
        total
        currency
        paymentMethod
        paymentStatus
        paymentId
        razorpayOrderId
        razorpayPaymentId
        trackingNumber
        estimatedDelivery
        deliveredAt
        customerNotes
        adminNotes
        giftMessage
        specialInstructions
        createdAt
        updatedAt
        __typename
      }
      product {
        id
        name
        description
        shortDescription
        price
        originalPrice
        images
        category
        subcategory
        tags
        inStock
        stockQuantity
        slug
        sku
        weight
        dimensions
        luxuryDescription
        budgetDescription
        narration
        features
        luxuryFeatures
        budgetFeatures
        specifications
        materials
        careInstructions
        warranty
        shippingInfo
        returnPolicy
        rating
        reviewCount
        badge
        relatedProductIds
        metaTitle
        metaDescription
        isActive
        isFeatured
        sortOrder
        createdBy
        createdAt
        updatedAt
        __typename
      }
      __typename
    }
  }
`;
export const listOrderItems = /* GraphQL */ `
  query ListOrderItems(
    $filter: ModelOrderItemFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listOrderItems(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        orderId
        productId
        userId
        quantity
        price
        giftType
        customization
        notes
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const getInquiry = /* GraphQL */ `
  query GetInquiry($id: ID!) {
    getInquiry(id: $id) {
      id
      userId
      type
      name
      firstName
      lastName
      email
      phone
      company
      subject
      message
      category
      priority
      eventType
      giftingType
      eventDate
      eventLocation
      guestCount
      totalGifts
      totalBudget
      budget
      location
      eventDetails
      source
      referral
      status
      assignedTo
      isRead
      isReplied
      adminNotes
      internalNotes
      attachments
      relatedOrderId
      relatedProductId
      createdAt
      updatedAt
      user {
        id
        email
        name
        firstName
        lastName
        phone
        alternatePhone
        address
        addressLine2
        city
        state
        pincode
        country
        landmark
        dateOfBirth
        gender
        maritalStatus
        anniversary
        occupation
        company
        avatar
        bio
        website
        socialMedia
        preferences
        communicationPreferences
        language
        currency
        timezone
        businessName
        businessType
        gstNumber
        panNumber
        businessAddress
        businessPhone
        businessEmail
        tags
        notes
        customerType
        loyaltyPoints
        totalSpent
        lastOrderDate
        isActive
        isVerified
        isVIP
        allowMarketing
        role
        createdAt
        updatedAt
        lastLoginAt
        __typename
      }
      responses {
        nextToken
        __typename
      }
      __typename
    }
  }
`;
export const listInquiries = /* GraphQL */ `
  query ListInquiries(
    $filter: ModelInquiryFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listInquiries(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        userId
        type
        name
        firstName
        lastName
        email
        phone
        company
        subject
        message
        category
        priority
        eventType
        giftingType
        eventDate
        eventLocation
        guestCount
        totalGifts
        totalBudget
        budget
        location
        eventDetails
        source
        referral
        status
        assignedTo
        isRead
        isReplied
        adminNotes
        internalNotes
        attachments
        relatedOrderId
        relatedProductId
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const getInquiryResponse = /* GraphQL */ `
  query GetInquiryResponse($id: ID!) {
    getInquiryResponse(id: $id) {
      id
      inquiryId
      responderId
      responderName
      responderEmail
      message
      isInternal
      attachments
      createdAt
      updatedAt
      inquiry {
        id
        userId
        type
        name
        firstName
        lastName
        email
        phone
        company
        subject
        message
        category
        priority
        eventType
        giftingType
        eventDate
        eventLocation
        guestCount
        totalGifts
        totalBudget
        budget
        location
        eventDetails
        source
        referral
        status
        assignedTo
        isRead
        isReplied
        adminNotes
        internalNotes
        attachments
        relatedOrderId
        relatedProductId
        createdAt
        updatedAt
        __typename
      }
      __typename
    }
  }
`;
export const listInquiryResponses = /* GraphQL */ `
  query ListInquiryResponses(
    $filter: ModelInquiryResponseFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listInquiryResponses(
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        inquiryId
        responderId
        responderName
        responderEmail
        message
        isInternal
        attachments
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const getNewsletter = /* GraphQL */ `
  query GetNewsletter($id: ID!) {
    getNewsletter(id: $id) {
      id
      email
      name
      phone
      preferences
      isActive
      source
      tags
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const listNewsletters = /* GraphQL */ `
  query ListNewsletters(
    $filter: ModelNewsletterFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listNewsletters(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        email
        name
        phone
        preferences
        isActive
        source
        tags
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const getReview = /* GraphQL */ `
  query GetReview($id: ID!) {
    getReview(id: $id) {
      id
      productId
      userId
      rating
      title
      content
      reviewerName
      reviewerEmail
      isVerifiedPurchase
      isApproved
      isHelpful
      isReported
      moderatorNotes
      approvedBy
      approvedAt
      createdAt
      updatedAt
      product {
        id
        name
        description
        shortDescription
        price
        originalPrice
        images
        category
        subcategory
        tags
        inStock
        stockQuantity
        slug
        sku
        weight
        dimensions
        luxuryDescription
        budgetDescription
        narration
        features
        luxuryFeatures
        budgetFeatures
        specifications
        materials
        careInstructions
        warranty
        shippingInfo
        returnPolicy
        rating
        reviewCount
        badge
        relatedProductIds
        metaTitle
        metaDescription
        isActive
        isFeatured
        sortOrder
        createdBy
        createdAt
        updatedAt
        __typename
      }
      user {
        id
        email
        name
        firstName
        lastName
        phone
        alternatePhone
        address
        addressLine2
        city
        state
        pincode
        country
        landmark
        dateOfBirth
        gender
        maritalStatus
        anniversary
        occupation
        company
        avatar
        bio
        website
        socialMedia
        preferences
        communicationPreferences
        language
        currency
        timezone
        businessName
        businessType
        gstNumber
        panNumber
        businessAddress
        businessPhone
        businessEmail
        tags
        notes
        customerType
        loyaltyPoints
        totalSpent
        lastOrderDate
        isActive
        isVerified
        isVIP
        allowMarketing
        role
        createdAt
        updatedAt
        lastLoginAt
        __typename
      }
      __typename
    }
  }
`;
export const listReviews = /* GraphQL */ `
  query ListReviews(
    $filter: ModelReviewFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listReviews(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        productId
        userId
        rating
        title
        content
        reviewerName
        reviewerEmail
        isVerifiedPurchase
        isApproved
        isHelpful
        isReported
        moderatorNotes
        approvedBy
        approvedAt
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const userProfilesByEmail = /* GraphQL */ `
  query UserProfilesByEmail(
    $email: String!
    $sortDirection: ModelSortDirection
    $filter: ModelUserProfileFilterInput
    $limit: Int
    $nextToken: String
  ) {
    userProfilesByEmail(
      email: $email
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        email
        name
        firstName
        lastName
        phone
        alternatePhone
        address
        addressLine2
        city
        state
        pincode
        country
        landmark
        dateOfBirth
        gender
        maritalStatus
        anniversary
        occupation
        company
        avatar
        bio
        website
        socialMedia
        preferences
        communicationPreferences
        language
        currency
        timezone
        businessName
        businessType
        gstNumber
        panNumber
        businessAddress
        businessPhone
        businessEmail
        tags
        notes
        customerType
        loyaltyPoints
        totalSpent
        lastOrderDate
        isActive
        isVerified
        isVIP
        allowMarketing
        role
        createdAt
        updatedAt
        lastLoginAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const galleriesByCreatedBy = /* GraphQL */ `
  query GalleriesByCreatedBy(
    $createdBy: String!
    $sortDirection: ModelSortDirection
    $filter: ModelGalleryFilterInput
    $limit: Int
    $nextToken: String
  ) {
    galleriesByCreatedBy(
      createdBy: $createdBy
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        title
        description
        imageKey
        imageUrl
        category
        subcategory
        tags
        isActive
        sortOrder
        altText
        metaTitle
        metaDescription
        createdBy
        updatedBy
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const productsByCategory = /* GraphQL */ `
  query ProductsByCategory(
    $category: String!
    $sortDirection: ModelSortDirection
    $filter: ModelProductFilterInput
    $limit: Int
    $nextToken: String
  ) {
    productsByCategory(
      category: $category
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        name
        description
        shortDescription
        price
        originalPrice
        images
        category
        subcategory
        tags
        inStock
        stockQuantity
        slug
        sku
        weight
        dimensions
        luxuryDescription
        budgetDescription
        narration
        features
        luxuryFeatures
        budgetFeatures
        specifications
        materials
        careInstructions
        warranty
        shippingInfo
        returnPolicy
        rating
        reviewCount
        badge
        relatedProductIds
        metaTitle
        metaDescription
        isActive
        isFeatured
        sortOrder
        createdBy
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const productsBySlug = /* GraphQL */ `
  query ProductsBySlug(
    $slug: String!
    $sortDirection: ModelSortDirection
    $filter: ModelProductFilterInput
    $limit: Int
    $nextToken: String
  ) {
    productsBySlug(
      slug: $slug
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        name
        description
        shortDescription
        price
        originalPrice
        images
        category
        subcategory
        tags
        inStock
        stockQuantity
        slug
        sku
        weight
        dimensions
        luxuryDescription
        budgetDescription
        narration
        features
        luxuryFeatures
        budgetFeatures
        specifications
        materials
        careInstructions
        warranty
        shippingInfo
        returnPolicy
        rating
        reviewCount
        badge
        relatedProductIds
        metaTitle
        metaDescription
        isActive
        isFeatured
        sortOrder
        createdBy
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const productsBySku = /* GraphQL */ `
  query ProductsBySku(
    $sku: String!
    $sortDirection: ModelSortDirection
    $filter: ModelProductFilterInput
    $limit: Int
    $nextToken: String
  ) {
    productsBySku(
      sku: $sku
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        name
        description
        shortDescription
        price
        originalPrice
        images
        category
        subcategory
        tags
        inStock
        stockQuantity
        slug
        sku
        weight
        dimensions
        luxuryDescription
        budgetDescription
        narration
        features
        luxuryFeatures
        budgetFeatures
        specifications
        materials
        careInstructions
        warranty
        shippingInfo
        returnPolicy
        rating
        reviewCount
        badge
        relatedProductIds
        metaTitle
        metaDescription
        isActive
        isFeatured
        sortOrder
        createdBy
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const cartItemsByUserId = /* GraphQL */ `
  query CartItemsByUserId(
    $userId: ID!
    $sortDirection: ModelSortDirection
    $filter: ModelCartItemFilterInput
    $limit: Int
    $nextToken: String
  ) {
    cartItemsByUserId(
      userId: $userId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        userId
        productId
        quantity
        price
        giftType
        customization
        notes
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const cartItemsByProductId = /* GraphQL */ `
  query CartItemsByProductId(
    $productId: ID!
    $sortDirection: ModelSortDirection
    $filter: ModelCartItemFilterInput
    $limit: Int
    $nextToken: String
  ) {
    cartItemsByProductId(
      productId: $productId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        userId
        productId
        quantity
        price
        giftType
        customization
        notes
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const ordersByUserId = /* GraphQL */ `
  query OrdersByUserId(
    $userId: ID!
    $sortDirection: ModelSortDirection
    $filter: ModelOrderFilterInput
    $limit: Int
    $nextToken: String
  ) {
    ordersByUserId(
      userId: $userId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        userId
        orderNumber
        status
        subtotal
        tax
        shipping
        discount
        total
        currency
        paymentMethod
        paymentStatus
        paymentId
        razorpayOrderId
        razorpayPaymentId
        trackingNumber
        estimatedDelivery
        deliveredAt
        customerNotes
        adminNotes
        giftMessage
        specialInstructions
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const ordersByOrderNumber = /* GraphQL */ `
  query OrdersByOrderNumber(
    $orderNumber: String!
    $sortDirection: ModelSortDirection
    $filter: ModelOrderFilterInput
    $limit: Int
    $nextToken: String
  ) {
    ordersByOrderNumber(
      orderNumber: $orderNumber
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        userId
        orderNumber
        status
        subtotal
        tax
        shipping
        discount
        total
        currency
        paymentMethod
        paymentStatus
        paymentId
        razorpayOrderId
        razorpayPaymentId
        trackingNumber
        estimatedDelivery
        deliveredAt
        customerNotes
        adminNotes
        giftMessage
        specialInstructions
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const orderItemsByOrderId = /* GraphQL */ `
  query OrderItemsByOrderId(
    $orderId: ID!
    $sortDirection: ModelSortDirection
    $filter: ModelOrderItemFilterInput
    $limit: Int
    $nextToken: String
  ) {
    orderItemsByOrderId(
      orderId: $orderId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        orderId
        productId
        userId
        quantity
        price
        giftType
        customization
        notes
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const orderItemsByProductId = /* GraphQL */ `
  query OrderItemsByProductId(
    $productId: ID!
    $sortDirection: ModelSortDirection
    $filter: ModelOrderItemFilterInput
    $limit: Int
    $nextToken: String
  ) {
    orderItemsByProductId(
      productId: $productId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        orderId
        productId
        userId
        quantity
        price
        giftType
        customization
        notes
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const inquiriesByUserId = /* GraphQL */ `
  query InquiriesByUserId(
    $userId: ID!
    $sortDirection: ModelSortDirection
    $filter: ModelInquiryFilterInput
    $limit: Int
    $nextToken: String
  ) {
    inquiriesByUserId(
      userId: $userId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        userId
        type
        name
        firstName
        lastName
        email
        phone
        company
        subject
        message
        category
        priority
        eventType
        giftingType
        eventDate
        eventLocation
        guestCount
        totalGifts
        totalBudget
        budget
        location
        eventDetails
        source
        referral
        status
        assignedTo
        isRead
        isReplied
        adminNotes
        internalNotes
        attachments
        relatedOrderId
        relatedProductId
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const inquiriesByType = /* GraphQL */ `
  query InquiriesByType(
    $type: InquiryType!
    $sortDirection: ModelSortDirection
    $filter: ModelInquiryFilterInput
    $limit: Int
    $nextToken: String
  ) {
    inquiriesByType(
      type: $type
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        userId
        type
        name
        firstName
        lastName
        email
        phone
        company
        subject
        message
        category
        priority
        eventType
        giftingType
        eventDate
        eventLocation
        guestCount
        totalGifts
        totalBudget
        budget
        location
        eventDetails
        source
        referral
        status
        assignedTo
        isRead
        isReplied
        adminNotes
        internalNotes
        attachments
        relatedOrderId
        relatedProductId
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const inquiriesByEmail = /* GraphQL */ `
  query InquiriesByEmail(
    $email: String!
    $sortDirection: ModelSortDirection
    $filter: ModelInquiryFilterInput
    $limit: Int
    $nextToken: String
  ) {
    inquiriesByEmail(
      email: $email
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        userId
        type
        name
        firstName
        lastName
        email
        phone
        company
        subject
        message
        category
        priority
        eventType
        giftingType
        eventDate
        eventLocation
        guestCount
        totalGifts
        totalBudget
        budget
        location
        eventDetails
        source
        referral
        status
        assignedTo
        isRead
        isReplied
        adminNotes
        internalNotes
        attachments
        relatedOrderId
        relatedProductId
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const inquiryResponsesByInquiryId = /* GraphQL */ `
  query InquiryResponsesByInquiryId(
    $inquiryId: ID!
    $sortDirection: ModelSortDirection
    $filter: ModelInquiryResponseFilterInput
    $limit: Int
    $nextToken: String
  ) {
    inquiryResponsesByInquiryId(
      inquiryId: $inquiryId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        inquiryId
        responderId
        responderName
        responderEmail
        message
        isInternal
        attachments
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const newslettersByEmail = /* GraphQL */ `
  query NewslettersByEmail(
    $email: String!
    $sortDirection: ModelSortDirection
    $filter: ModelNewsletterFilterInput
    $limit: Int
    $nextToken: String
  ) {
    newslettersByEmail(
      email: $email
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        email
        name
        phone
        preferences
        isActive
        source
        tags
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const reviewsByProductId = /* GraphQL */ `
  query ReviewsByProductId(
    $productId: ID!
    $sortDirection: ModelSortDirection
    $filter: ModelReviewFilterInput
    $limit: Int
    $nextToken: String
  ) {
    reviewsByProductId(
      productId: $productId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        productId
        userId
        rating
        title
        content
        reviewerName
        reviewerEmail
        isVerifiedPurchase
        isApproved
        isHelpful
        isReported
        moderatorNotes
        approvedBy
        approvedAt
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const reviewsByUserId = /* GraphQL */ `
  query ReviewsByUserId(
    $userId: ID!
    $sortDirection: ModelSortDirection
    $filter: ModelReviewFilterInput
    $limit: Int
    $nextToken: String
  ) {
    reviewsByUserId(
      userId: $userId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        productId
        userId
        rating
        title
        content
        reviewerName
        reviewerEmail
        isVerifiedPurchase
        isApproved
        isHelpful
        isReported
        moderatorNotes
        approvedBy
        approvedAt
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
