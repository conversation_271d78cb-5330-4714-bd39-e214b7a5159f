/* eslint-disable */
// this is an auto generated file. This will be overwritten

export const onCreateUserProfile = /* GraphQL */ `
  subscription OnCreateUserProfile(
    $filter: ModelSubscriptionUserProfileFilterInput
  ) {
    onCreateUserProfile(filter: $filter) {
      id
      email
      name
      firstName
      lastName
      phone
      alternatePhone
      address
      addressLine2
      city
      state
      pincode
      country
      landmark
      dateOfBirth
      gender
      maritalStatus
      anniversary
      occupation
      company
      avatar
      bio
      website
      socialMedia
      preferences
      communicationPreferences
      language
      currency
      timezone
      businessName
      businessType
      gstNumber
      panNumber
      businessAddress
      businessPhone
      businessEmail
      tags
      notes
      customerType
      loyaltyPoints
      totalSpent
      lastOrderDate
      isActive
      isVerified
      isVIP
      allowMarketing
      role
      createdAt
      updatedAt
      lastLoginAt
      cartItems {
        nextToken
        __typename
      }
      orders {
        nextToken
        __typename
      }
      inquiries {
        nextToken
        __typename
      }
      reviews {
        nextToken
        __typename
      }
      __typename
    }
  }
`;
export const onUpdateUserProfile = /* GraphQL */ `
  subscription OnUpdateUserProfile(
    $filter: ModelSubscriptionUserProfileFilterInput
  ) {
    onUpdateUserProfile(filter: $filter) {
      id
      email
      name
      firstName
      lastName
      phone
      alternatePhone
      address
      addressLine2
      city
      state
      pincode
      country
      landmark
      dateOfBirth
      gender
      maritalStatus
      anniversary
      occupation
      company
      avatar
      bio
      website
      socialMedia
      preferences
      communicationPreferences
      language
      currency
      timezone
      businessName
      businessType
      gstNumber
      panNumber
      businessAddress
      businessPhone
      businessEmail
      tags
      notes
      customerType
      loyaltyPoints
      totalSpent
      lastOrderDate
      isActive
      isVerified
      isVIP
      allowMarketing
      role
      createdAt
      updatedAt
      lastLoginAt
      cartItems {
        nextToken
        __typename
      }
      orders {
        nextToken
        __typename
      }
      inquiries {
        nextToken
        __typename
      }
      reviews {
        nextToken
        __typename
      }
      __typename
    }
  }
`;
export const onDeleteUserProfile = /* GraphQL */ `
  subscription OnDeleteUserProfile(
    $filter: ModelSubscriptionUserProfileFilterInput
  ) {
    onDeleteUserProfile(filter: $filter) {
      id
      email
      name
      firstName
      lastName
      phone
      alternatePhone
      address
      addressLine2
      city
      state
      pincode
      country
      landmark
      dateOfBirth
      gender
      maritalStatus
      anniversary
      occupation
      company
      avatar
      bio
      website
      socialMedia
      preferences
      communicationPreferences
      language
      currency
      timezone
      businessName
      businessType
      gstNumber
      panNumber
      businessAddress
      businessPhone
      businessEmail
      tags
      notes
      customerType
      loyaltyPoints
      totalSpent
      lastOrderDate
      isActive
      isVerified
      isVIP
      allowMarketing
      role
      createdAt
      updatedAt
      lastLoginAt
      cartItems {
        nextToken
        __typename
      }
      orders {
        nextToken
        __typename
      }
      inquiries {
        nextToken
        __typename
      }
      reviews {
        nextToken
        __typename
      }
      __typename
    }
  }
`;
export const onCreateGallery = /* GraphQL */ `
  subscription OnCreateGallery($filter: ModelSubscriptionGalleryFilterInput) {
    onCreateGallery(filter: $filter) {
      id
      title
      description
      imageKey
      imageUrl
      category
      subcategory
      tags
      isActive
      sortOrder
      altText
      metaTitle
      metaDescription
      createdBy
      updatedBy
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onUpdateGallery = /* GraphQL */ `
  subscription OnUpdateGallery($filter: ModelSubscriptionGalleryFilterInput) {
    onUpdateGallery(filter: $filter) {
      id
      title
      description
      imageKey
      imageUrl
      category
      subcategory
      tags
      isActive
      sortOrder
      altText
      metaTitle
      metaDescription
      createdBy
      updatedBy
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onDeleteGallery = /* GraphQL */ `
  subscription OnDeleteGallery($filter: ModelSubscriptionGalleryFilterInput) {
    onDeleteGallery(filter: $filter) {
      id
      title
      description
      imageKey
      imageUrl
      category
      subcategory
      tags
      isActive
      sortOrder
      altText
      metaTitle
      metaDescription
      createdBy
      updatedBy
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onCreateGalleryStats = /* GraphQL */ `
  subscription OnCreateGalleryStats(
    $filter: ModelSubscriptionGalleryStatsFilterInput
  ) {
    onCreateGalleryStats(filter: $filter) {
      id
      category
      totalImages
      activeImages
      lastUpdated
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onUpdateGalleryStats = /* GraphQL */ `
  subscription OnUpdateGalleryStats(
    $filter: ModelSubscriptionGalleryStatsFilterInput
  ) {
    onUpdateGalleryStats(filter: $filter) {
      id
      category
      totalImages
      activeImages
      lastUpdated
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onDeleteGalleryStats = /* GraphQL */ `
  subscription OnDeleteGalleryStats(
    $filter: ModelSubscriptionGalleryStatsFilterInput
  ) {
    onDeleteGalleryStats(filter: $filter) {
      id
      category
      totalImages
      activeImages
      lastUpdated
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onCreateProduct = /* GraphQL */ `
  subscription OnCreateProduct($filter: ModelSubscriptionProductFilterInput) {
    onCreateProduct(filter: $filter) {
      id
      name
      description
      shortDescription
      price
      originalPrice
      images
      category
      subcategory
      tags
      inStock
      stockQuantity
      slug
      sku
      weight
      dimensions
      luxuryDescription
      budgetDescription
      narration
      features
      luxuryFeatures
      budgetFeatures
      specifications
      materials
      careInstructions
      warranty
      shippingInfo
      returnPolicy
      rating
      reviewCount
      badge
      relatedProductIds
      metaTitle
      metaDescription
      isActive
      isFeatured
      sortOrder
      createdBy
      createdAt
      updatedAt
      cartItems {
        nextToken
        __typename
      }
      orderItems {
        nextToken
        __typename
      }
      reviews {
        nextToken
        __typename
      }
      __typename
    }
  }
`;
export const onUpdateProduct = /* GraphQL */ `
  subscription OnUpdateProduct($filter: ModelSubscriptionProductFilterInput) {
    onUpdateProduct(filter: $filter) {
      id
      name
      description
      shortDescription
      price
      originalPrice
      images
      category
      subcategory
      tags
      inStock
      stockQuantity
      slug
      sku
      weight
      dimensions
      luxuryDescription
      budgetDescription
      narration
      features
      luxuryFeatures
      budgetFeatures
      specifications
      materials
      careInstructions
      warranty
      shippingInfo
      returnPolicy
      rating
      reviewCount
      badge
      relatedProductIds
      metaTitle
      metaDescription
      isActive
      isFeatured
      sortOrder
      createdBy
      createdAt
      updatedAt
      cartItems {
        nextToken
        __typename
      }
      orderItems {
        nextToken
        __typename
      }
      reviews {
        nextToken
        __typename
      }
      __typename
    }
  }
`;
export const onDeleteProduct = /* GraphQL */ `
  subscription OnDeleteProduct($filter: ModelSubscriptionProductFilterInput) {
    onDeleteProduct(filter: $filter) {
      id
      name
      description
      shortDescription
      price
      originalPrice
      images
      category
      subcategory
      tags
      inStock
      stockQuantity
      slug
      sku
      weight
      dimensions
      luxuryDescription
      budgetDescription
      narration
      features
      luxuryFeatures
      budgetFeatures
      specifications
      materials
      careInstructions
      warranty
      shippingInfo
      returnPolicy
      rating
      reviewCount
      badge
      relatedProductIds
      metaTitle
      metaDescription
      isActive
      isFeatured
      sortOrder
      createdBy
      createdAt
      updatedAt
      cartItems {
        nextToken
        __typename
      }
      orderItems {
        nextToken
        __typename
      }
      reviews {
        nextToken
        __typename
      }
      __typename
    }
  }
`;
export const onCreateCartItem = /* GraphQL */ `
  subscription OnCreateCartItem($filter: ModelSubscriptionCartItemFilterInput) {
    onCreateCartItem(filter: $filter) {
      id
      userId
      productId
      quantity
      price
      giftType
      customization
      notes
      createdAt
      updatedAt
      user {
        id
        email
        name
        firstName
        lastName
        phone
        alternatePhone
        address
        addressLine2
        city
        state
        pincode
        country
        landmark
        dateOfBirth
        gender
        maritalStatus
        anniversary
        occupation
        company
        avatar
        bio
        website
        socialMedia
        preferences
        communicationPreferences
        language
        currency
        timezone
        businessName
        businessType
        gstNumber
        panNumber
        businessAddress
        businessPhone
        businessEmail
        tags
        notes
        customerType
        loyaltyPoints
        totalSpent
        lastOrderDate
        isActive
        isVerified
        isVIP
        allowMarketing
        role
        createdAt
        updatedAt
        lastLoginAt
        __typename
      }
      product {
        id
        name
        description
        shortDescription
        price
        originalPrice
        images
        category
        subcategory
        tags
        inStock
        stockQuantity
        slug
        sku
        weight
        dimensions
        luxuryDescription
        budgetDescription
        narration
        features
        luxuryFeatures
        budgetFeatures
        specifications
        materials
        careInstructions
        warranty
        shippingInfo
        returnPolicy
        rating
        reviewCount
        badge
        relatedProductIds
        metaTitle
        metaDescription
        isActive
        isFeatured
        sortOrder
        createdBy
        createdAt
        updatedAt
        __typename
      }
      __typename
    }
  }
`;
export const onUpdateCartItem = /* GraphQL */ `
  subscription OnUpdateCartItem($filter: ModelSubscriptionCartItemFilterInput) {
    onUpdateCartItem(filter: $filter) {
      id
      userId
      productId
      quantity
      price
      giftType
      customization
      notes
      createdAt
      updatedAt
      user {
        id
        email
        name
        firstName
        lastName
        phone
        alternatePhone
        address
        addressLine2
        city
        state
        pincode
        country
        landmark
        dateOfBirth
        gender
        maritalStatus
        anniversary
        occupation
        company
        avatar
        bio
        website
        socialMedia
        preferences
        communicationPreferences
        language
        currency
        timezone
        businessName
        businessType
        gstNumber
        panNumber
        businessAddress
        businessPhone
        businessEmail
        tags
        notes
        customerType
        loyaltyPoints
        totalSpent
        lastOrderDate
        isActive
        isVerified
        isVIP
        allowMarketing
        role
        createdAt
        updatedAt
        lastLoginAt
        __typename
      }
      product {
        id
        name
        description
        shortDescription
        price
        originalPrice
        images
        category
        subcategory
        tags
        inStock
        stockQuantity
        slug
        sku
        weight
        dimensions
        luxuryDescription
        budgetDescription
        narration
        features
        luxuryFeatures
        budgetFeatures
        specifications
        materials
        careInstructions
        warranty
        shippingInfo
        returnPolicy
        rating
        reviewCount
        badge
        relatedProductIds
        metaTitle
        metaDescription
        isActive
        isFeatured
        sortOrder
        createdBy
        createdAt
        updatedAt
        __typename
      }
      __typename
    }
  }
`;
export const onDeleteCartItem = /* GraphQL */ `
  subscription OnDeleteCartItem($filter: ModelSubscriptionCartItemFilterInput) {
    onDeleteCartItem(filter: $filter) {
      id
      userId
      productId
      quantity
      price
      giftType
      customization
      notes
      createdAt
      updatedAt
      user {
        id
        email
        name
        firstName
        lastName
        phone
        alternatePhone
        address
        addressLine2
        city
        state
        pincode
        country
        landmark
        dateOfBirth
        gender
        maritalStatus
        anniversary
        occupation
        company
        avatar
        bio
        website
        socialMedia
        preferences
        communicationPreferences
        language
        currency
        timezone
        businessName
        businessType
        gstNumber
        panNumber
        businessAddress
        businessPhone
        businessEmail
        tags
        notes
        customerType
        loyaltyPoints
        totalSpent
        lastOrderDate
        isActive
        isVerified
        isVIP
        allowMarketing
        role
        createdAt
        updatedAt
        lastLoginAt
        __typename
      }
      product {
        id
        name
        description
        shortDescription
        price
        originalPrice
        images
        category
        subcategory
        tags
        inStock
        stockQuantity
        slug
        sku
        weight
        dimensions
        luxuryDescription
        budgetDescription
        narration
        features
        luxuryFeatures
        budgetFeatures
        specifications
        materials
        careInstructions
        warranty
        shippingInfo
        returnPolicy
        rating
        reviewCount
        badge
        relatedProductIds
        metaTitle
        metaDescription
        isActive
        isFeatured
        sortOrder
        createdBy
        createdAt
        updatedAt
        __typename
      }
      __typename
    }
  }
`;
export const onCreateOrder = /* GraphQL */ `
  subscription OnCreateOrder($filter: ModelSubscriptionOrderFilterInput) {
    onCreateOrder(filter: $filter) {
      id
      userId
      orderNumber
      status
      subtotal
      tax
      shipping
      discount
      total
      currency
      shippingAddress {
        name
        phone
        email
        addressLine1
        addressLine2
        landmark
        city
        state
        pincode
        country
        addressType
        __typename
      }
      billingAddress {
        name
        phone
        email
        addressLine1
        addressLine2
        landmark
        city
        state
        pincode
        country
        addressType
        __typename
      }
      paymentMethod
      paymentStatus
      paymentId
      razorpayOrderId
      razorpayPaymentId
      trackingNumber
      estimatedDelivery
      deliveredAt
      customerNotes
      adminNotes
      giftMessage
      specialInstructions
      createdAt
      updatedAt
      user {
        id
        email
        name
        firstName
        lastName
        phone
        alternatePhone
        address
        addressLine2
        city
        state
        pincode
        country
        landmark
        dateOfBirth
        gender
        maritalStatus
        anniversary
        occupation
        company
        avatar
        bio
        website
        socialMedia
        preferences
        communicationPreferences
        language
        currency
        timezone
        businessName
        businessType
        gstNumber
        panNumber
        businessAddress
        businessPhone
        businessEmail
        tags
        notes
        customerType
        loyaltyPoints
        totalSpent
        lastOrderDate
        isActive
        isVerified
        isVIP
        allowMarketing
        role
        createdAt
        updatedAt
        lastLoginAt
        __typename
      }
      items {
        nextToken
        __typename
      }
      __typename
    }
  }
`;
export const onUpdateOrder = /* GraphQL */ `
  subscription OnUpdateOrder($filter: ModelSubscriptionOrderFilterInput) {
    onUpdateOrder(filter: $filter) {
      id
      userId
      orderNumber
      status
      subtotal
      tax
      shipping
      discount
      total
      currency
      shippingAddress {
        name
        phone
        email
        addressLine1
        addressLine2
        landmark
        city
        state
        pincode
        country
        addressType
        __typename
      }
      billingAddress {
        name
        phone
        email
        addressLine1
        addressLine2
        landmark
        city
        state
        pincode
        country
        addressType
        __typename
      }
      paymentMethod
      paymentStatus
      paymentId
      razorpayOrderId
      razorpayPaymentId
      trackingNumber
      estimatedDelivery
      deliveredAt
      customerNotes
      adminNotes
      giftMessage
      specialInstructions
      createdAt
      updatedAt
      user {
        id
        email
        name
        firstName
        lastName
        phone
        alternatePhone
        address
        addressLine2
        city
        state
        pincode
        country
        landmark
        dateOfBirth
        gender
        maritalStatus
        anniversary
        occupation
        company
        avatar
        bio
        website
        socialMedia
        preferences
        communicationPreferences
        language
        currency
        timezone
        businessName
        businessType
        gstNumber
        panNumber
        businessAddress
        businessPhone
        businessEmail
        tags
        notes
        customerType
        loyaltyPoints
        totalSpent
        lastOrderDate
        isActive
        isVerified
        isVIP
        allowMarketing
        role
        createdAt
        updatedAt
        lastLoginAt
        __typename
      }
      items {
        nextToken
        __typename
      }
      __typename
    }
  }
`;
export const onDeleteOrder = /* GraphQL */ `
  subscription OnDeleteOrder($filter: ModelSubscriptionOrderFilterInput) {
    onDeleteOrder(filter: $filter) {
      id
      userId
      orderNumber
      status
      subtotal
      tax
      shipping
      discount
      total
      currency
      shippingAddress {
        name
        phone
        email
        addressLine1
        addressLine2
        landmark
        city
        state
        pincode
        country
        addressType
        __typename
      }
      billingAddress {
        name
        phone
        email
        addressLine1
        addressLine2
        landmark
        city
        state
        pincode
        country
        addressType
        __typename
      }
      paymentMethod
      paymentStatus
      paymentId
      razorpayOrderId
      razorpayPaymentId
      trackingNumber
      estimatedDelivery
      deliveredAt
      customerNotes
      adminNotes
      giftMessage
      specialInstructions
      createdAt
      updatedAt
      user {
        id
        email
        name
        firstName
        lastName
        phone
        alternatePhone
        address
        addressLine2
        city
        state
        pincode
        country
        landmark
        dateOfBirth
        gender
        maritalStatus
        anniversary
        occupation
        company
        avatar
        bio
        website
        socialMedia
        preferences
        communicationPreferences
        language
        currency
        timezone
        businessName
        businessType
        gstNumber
        panNumber
        businessAddress
        businessPhone
        businessEmail
        tags
        notes
        customerType
        loyaltyPoints
        totalSpent
        lastOrderDate
        isActive
        isVerified
        isVIP
        allowMarketing
        role
        createdAt
        updatedAt
        lastLoginAt
        __typename
      }
      items {
        nextToken
        __typename
      }
      __typename
    }
  }
`;
export const onCreateOrderItem = /* GraphQL */ `
  subscription OnCreateOrderItem(
    $filter: ModelSubscriptionOrderItemFilterInput
  ) {
    onCreateOrderItem(filter: $filter) {
      id
      orderId
      productId
      userId
      quantity
      price
      giftType
      customization
      notes
      createdAt
      updatedAt
      order {
        id
        userId
        orderNumber
        status
        subtotal
        tax
        shipping
        discount
        total
        currency
        paymentMethod
        paymentStatus
        paymentId
        razorpayOrderId
        razorpayPaymentId
        trackingNumber
        estimatedDelivery
        deliveredAt
        customerNotes
        adminNotes
        giftMessage
        specialInstructions
        createdAt
        updatedAt
        __typename
      }
      product {
        id
        name
        description
        shortDescription
        price
        originalPrice
        images
        category
        subcategory
        tags
        inStock
        stockQuantity
        slug
        sku
        weight
        dimensions
        luxuryDescription
        budgetDescription
        narration
        features
        luxuryFeatures
        budgetFeatures
        specifications
        materials
        careInstructions
        warranty
        shippingInfo
        returnPolicy
        rating
        reviewCount
        badge
        relatedProductIds
        metaTitle
        metaDescription
        isActive
        isFeatured
        sortOrder
        createdBy
        createdAt
        updatedAt
        __typename
      }
      __typename
    }
  }
`;
export const onUpdateOrderItem = /* GraphQL */ `
  subscription OnUpdateOrderItem(
    $filter: ModelSubscriptionOrderItemFilterInput
  ) {
    onUpdateOrderItem(filter: $filter) {
      id
      orderId
      productId
      userId
      quantity
      price
      giftType
      customization
      notes
      createdAt
      updatedAt
      order {
        id
        userId
        orderNumber
        status
        subtotal
        tax
        shipping
        discount
        total
        currency
        paymentMethod
        paymentStatus
        paymentId
        razorpayOrderId
        razorpayPaymentId
        trackingNumber
        estimatedDelivery
        deliveredAt
        customerNotes
        adminNotes
        giftMessage
        specialInstructions
        createdAt
        updatedAt
        __typename
      }
      product {
        id
        name
        description
        shortDescription
        price
        originalPrice
        images
        category
        subcategory
        tags
        inStock
        stockQuantity
        slug
        sku
        weight
        dimensions
        luxuryDescription
        budgetDescription
        narration
        features
        luxuryFeatures
        budgetFeatures
        specifications
        materials
        careInstructions
        warranty
        shippingInfo
        returnPolicy
        rating
        reviewCount
        badge
        relatedProductIds
        metaTitle
        metaDescription
        isActive
        isFeatured
        sortOrder
        createdBy
        createdAt
        updatedAt
        __typename
      }
      __typename
    }
  }
`;
export const onDeleteOrderItem = /* GraphQL */ `
  subscription OnDeleteOrderItem(
    $filter: ModelSubscriptionOrderItemFilterInput
  ) {
    onDeleteOrderItem(filter: $filter) {
      id
      orderId
      productId
      userId
      quantity
      price
      giftType
      customization
      notes
      createdAt
      updatedAt
      order {
        id
        userId
        orderNumber
        status
        subtotal
        tax
        shipping
        discount
        total
        currency
        paymentMethod
        paymentStatus
        paymentId
        razorpayOrderId
        razorpayPaymentId
        trackingNumber
        estimatedDelivery
        deliveredAt
        customerNotes
        adminNotes
        giftMessage
        specialInstructions
        createdAt
        updatedAt
        __typename
      }
      product {
        id
        name
        description
        shortDescription
        price
        originalPrice
        images
        category
        subcategory
        tags
        inStock
        stockQuantity
        slug
        sku
        weight
        dimensions
        luxuryDescription
        budgetDescription
        narration
        features
        luxuryFeatures
        budgetFeatures
        specifications
        materials
        careInstructions
        warranty
        shippingInfo
        returnPolicy
        rating
        reviewCount
        badge
        relatedProductIds
        metaTitle
        metaDescription
        isActive
        isFeatured
        sortOrder
        createdBy
        createdAt
        updatedAt
        __typename
      }
      __typename
    }
  }
`;
export const onCreateInquiry = /* GraphQL */ `
  subscription OnCreateInquiry($filter: ModelSubscriptionInquiryFilterInput) {
    onCreateInquiry(filter: $filter) {
      id
      userId
      type
      name
      firstName
      lastName
      email
      phone
      company
      subject
      message
      category
      priority
      eventType
      giftingType
      eventDate
      eventLocation
      guestCount
      totalGifts
      totalBudget
      budget
      location
      eventDetails
      source
      referral
      status
      assignedTo
      isRead
      isReplied
      adminNotes
      internalNotes
      attachments
      relatedOrderId
      relatedProductId
      createdAt
      updatedAt
      user {
        id
        email
        name
        firstName
        lastName
        phone
        alternatePhone
        address
        addressLine2
        city
        state
        pincode
        country
        landmark
        dateOfBirth
        gender
        maritalStatus
        anniversary
        occupation
        company
        avatar
        bio
        website
        socialMedia
        preferences
        communicationPreferences
        language
        currency
        timezone
        businessName
        businessType
        gstNumber
        panNumber
        businessAddress
        businessPhone
        businessEmail
        tags
        notes
        customerType
        loyaltyPoints
        totalSpent
        lastOrderDate
        isActive
        isVerified
        isVIP
        allowMarketing
        role
        createdAt
        updatedAt
        lastLoginAt
        __typename
      }
      responses {
        nextToken
        __typename
      }
      __typename
    }
  }
`;
export const onUpdateInquiry = /* GraphQL */ `
  subscription OnUpdateInquiry($filter: ModelSubscriptionInquiryFilterInput) {
    onUpdateInquiry(filter: $filter) {
      id
      userId
      type
      name
      firstName
      lastName
      email
      phone
      company
      subject
      message
      category
      priority
      eventType
      giftingType
      eventDate
      eventLocation
      guestCount
      totalGifts
      totalBudget
      budget
      location
      eventDetails
      source
      referral
      status
      assignedTo
      isRead
      isReplied
      adminNotes
      internalNotes
      attachments
      relatedOrderId
      relatedProductId
      createdAt
      updatedAt
      user {
        id
        email
        name
        firstName
        lastName
        phone
        alternatePhone
        address
        addressLine2
        city
        state
        pincode
        country
        landmark
        dateOfBirth
        gender
        maritalStatus
        anniversary
        occupation
        company
        avatar
        bio
        website
        socialMedia
        preferences
        communicationPreferences
        language
        currency
        timezone
        businessName
        businessType
        gstNumber
        panNumber
        businessAddress
        businessPhone
        businessEmail
        tags
        notes
        customerType
        loyaltyPoints
        totalSpent
        lastOrderDate
        isActive
        isVerified
        isVIP
        allowMarketing
        role
        createdAt
        updatedAt
        lastLoginAt
        __typename
      }
      responses {
        nextToken
        __typename
      }
      __typename
    }
  }
`;
export const onDeleteInquiry = /* GraphQL */ `
  subscription OnDeleteInquiry($filter: ModelSubscriptionInquiryFilterInput) {
    onDeleteInquiry(filter: $filter) {
      id
      userId
      type
      name
      firstName
      lastName
      email
      phone
      company
      subject
      message
      category
      priority
      eventType
      giftingType
      eventDate
      eventLocation
      guestCount
      totalGifts
      totalBudget
      budget
      location
      eventDetails
      source
      referral
      status
      assignedTo
      isRead
      isReplied
      adminNotes
      internalNotes
      attachments
      relatedOrderId
      relatedProductId
      createdAt
      updatedAt
      user {
        id
        email
        name
        firstName
        lastName
        phone
        alternatePhone
        address
        addressLine2
        city
        state
        pincode
        country
        landmark
        dateOfBirth
        gender
        maritalStatus
        anniversary
        occupation
        company
        avatar
        bio
        website
        socialMedia
        preferences
        communicationPreferences
        language
        currency
        timezone
        businessName
        businessType
        gstNumber
        panNumber
        businessAddress
        businessPhone
        businessEmail
        tags
        notes
        customerType
        loyaltyPoints
        totalSpent
        lastOrderDate
        isActive
        isVerified
        isVIP
        allowMarketing
        role
        createdAt
        updatedAt
        lastLoginAt
        __typename
      }
      responses {
        nextToken
        __typename
      }
      __typename
    }
  }
`;
export const onCreateInquiryResponse = /* GraphQL */ `
  subscription OnCreateInquiryResponse(
    $filter: ModelSubscriptionInquiryResponseFilterInput
  ) {
    onCreateInquiryResponse(filter: $filter) {
      id
      inquiryId
      responderId
      responderName
      responderEmail
      message
      isInternal
      attachments
      createdAt
      updatedAt
      inquiry {
        id
        userId
        type
        name
        firstName
        lastName
        email
        phone
        company
        subject
        message
        category
        priority
        eventType
        giftingType
        eventDate
        eventLocation
        guestCount
        totalGifts
        totalBudget
        budget
        location
        eventDetails
        source
        referral
        status
        assignedTo
        isRead
        isReplied
        adminNotes
        internalNotes
        attachments
        relatedOrderId
        relatedProductId
        createdAt
        updatedAt
        __typename
      }
      __typename
    }
  }
`;
export const onUpdateInquiryResponse = /* GraphQL */ `
  subscription OnUpdateInquiryResponse(
    $filter: ModelSubscriptionInquiryResponseFilterInput
  ) {
    onUpdateInquiryResponse(filter: $filter) {
      id
      inquiryId
      responderId
      responderName
      responderEmail
      message
      isInternal
      attachments
      createdAt
      updatedAt
      inquiry {
        id
        userId
        type
        name
        firstName
        lastName
        email
        phone
        company
        subject
        message
        category
        priority
        eventType
        giftingType
        eventDate
        eventLocation
        guestCount
        totalGifts
        totalBudget
        budget
        location
        eventDetails
        source
        referral
        status
        assignedTo
        isRead
        isReplied
        adminNotes
        internalNotes
        attachments
        relatedOrderId
        relatedProductId
        createdAt
        updatedAt
        __typename
      }
      __typename
    }
  }
`;
export const onDeleteInquiryResponse = /* GraphQL */ `
  subscription OnDeleteInquiryResponse(
    $filter: ModelSubscriptionInquiryResponseFilterInput
  ) {
    onDeleteInquiryResponse(filter: $filter) {
      id
      inquiryId
      responderId
      responderName
      responderEmail
      message
      isInternal
      attachments
      createdAt
      updatedAt
      inquiry {
        id
        userId
        type
        name
        firstName
        lastName
        email
        phone
        company
        subject
        message
        category
        priority
        eventType
        giftingType
        eventDate
        eventLocation
        guestCount
        totalGifts
        totalBudget
        budget
        location
        eventDetails
        source
        referral
        status
        assignedTo
        isRead
        isReplied
        adminNotes
        internalNotes
        attachments
        relatedOrderId
        relatedProductId
        createdAt
        updatedAt
        __typename
      }
      __typename
    }
  }
`;
export const onCreateNewsletter = /* GraphQL */ `
  subscription OnCreateNewsletter(
    $filter: ModelSubscriptionNewsletterFilterInput
  ) {
    onCreateNewsletter(filter: $filter) {
      id
      email
      name
      phone
      preferences
      isActive
      source
      tags
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onUpdateNewsletter = /* GraphQL */ `
  subscription OnUpdateNewsletter(
    $filter: ModelSubscriptionNewsletterFilterInput
  ) {
    onUpdateNewsletter(filter: $filter) {
      id
      email
      name
      phone
      preferences
      isActive
      source
      tags
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onDeleteNewsletter = /* GraphQL */ `
  subscription OnDeleteNewsletter(
    $filter: ModelSubscriptionNewsletterFilterInput
  ) {
    onDeleteNewsletter(filter: $filter) {
      id
      email
      name
      phone
      preferences
      isActive
      source
      tags
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onCreateReview = /* GraphQL */ `
  subscription OnCreateReview($filter: ModelSubscriptionReviewFilterInput) {
    onCreateReview(filter: $filter) {
      id
      productId
      userId
      rating
      title
      content
      reviewerName
      reviewerEmail
      isVerifiedPurchase
      isApproved
      isHelpful
      isReported
      moderatorNotes
      approvedBy
      approvedAt
      createdAt
      updatedAt
      product {
        id
        name
        description
        shortDescription
        price
        originalPrice
        images
        category
        subcategory
        tags
        inStock
        stockQuantity
        slug
        sku
        weight
        dimensions
        luxuryDescription
        budgetDescription
        narration
        features
        luxuryFeatures
        budgetFeatures
        specifications
        materials
        careInstructions
        warranty
        shippingInfo
        returnPolicy
        rating
        reviewCount
        badge
        relatedProductIds
        metaTitle
        metaDescription
        isActive
        isFeatured
        sortOrder
        createdBy
        createdAt
        updatedAt
        __typename
      }
      user {
        id
        email
        name
        firstName
        lastName
        phone
        alternatePhone
        address
        addressLine2
        city
        state
        pincode
        country
        landmark
        dateOfBirth
        gender
        maritalStatus
        anniversary
        occupation
        company
        avatar
        bio
        website
        socialMedia
        preferences
        communicationPreferences
        language
        currency
        timezone
        businessName
        businessType
        gstNumber
        panNumber
        businessAddress
        businessPhone
        businessEmail
        tags
        notes
        customerType
        loyaltyPoints
        totalSpent
        lastOrderDate
        isActive
        isVerified
        isVIP
        allowMarketing
        role
        createdAt
        updatedAt
        lastLoginAt
        __typename
      }
      __typename
    }
  }
`;
export const onUpdateReview = /* GraphQL */ `
  subscription OnUpdateReview($filter: ModelSubscriptionReviewFilterInput) {
    onUpdateReview(filter: $filter) {
      id
      productId
      userId
      rating
      title
      content
      reviewerName
      reviewerEmail
      isVerifiedPurchase
      isApproved
      isHelpful
      isReported
      moderatorNotes
      approvedBy
      approvedAt
      createdAt
      updatedAt
      product {
        id
        name
        description
        shortDescription
        price
        originalPrice
        images
        category
        subcategory
        tags
        inStock
        stockQuantity
        slug
        sku
        weight
        dimensions
        luxuryDescription
        budgetDescription
        narration
        features
        luxuryFeatures
        budgetFeatures
        specifications
        materials
        careInstructions
        warranty
        shippingInfo
        returnPolicy
        rating
        reviewCount
        badge
        relatedProductIds
        metaTitle
        metaDescription
        isActive
        isFeatured
        sortOrder
        createdBy
        createdAt
        updatedAt
        __typename
      }
      user {
        id
        email
        name
        firstName
        lastName
        phone
        alternatePhone
        address
        addressLine2
        city
        state
        pincode
        country
        landmark
        dateOfBirth
        gender
        maritalStatus
        anniversary
        occupation
        company
        avatar
        bio
        website
        socialMedia
        preferences
        communicationPreferences
        language
        currency
        timezone
        businessName
        businessType
        gstNumber
        panNumber
        businessAddress
        businessPhone
        businessEmail
        tags
        notes
        customerType
        loyaltyPoints
        totalSpent
        lastOrderDate
        isActive
        isVerified
        isVIP
        allowMarketing
        role
        createdAt
        updatedAt
        lastLoginAt
        __typename
      }
      __typename
    }
  }
`;
export const onDeleteReview = /* GraphQL */ `
  subscription OnDeleteReview($filter: ModelSubscriptionReviewFilterInput) {
    onDeleteReview(filter: $filter) {
      id
      productId
      userId
      rating
      title
      content
      reviewerName
      reviewerEmail
      isVerifiedPurchase
      isApproved
      isHelpful
      isReported
      moderatorNotes
      approvedBy
      approvedAt
      createdAt
      updatedAt
      product {
        id
        name
        description
        shortDescription
        price
        originalPrice
        images
        category
        subcategory
        tags
        inStock
        stockQuantity
        slug
        sku
        weight
        dimensions
        luxuryDescription
        budgetDescription
        narration
        features
        luxuryFeatures
        budgetFeatures
        specifications
        materials
        careInstructions
        warranty
        shippingInfo
        returnPolicy
        rating
        reviewCount
        badge
        relatedProductIds
        metaTitle
        metaDescription
        isActive
        isFeatured
        sortOrder
        createdBy
        createdAt
        updatedAt
        __typename
      }
      user {
        id
        email
        name
        firstName
        lastName
        phone
        alternatePhone
        address
        addressLine2
        city
        state
        pincode
        country
        landmark
        dateOfBirth
        gender
        maritalStatus
        anniversary
        occupation
        company
        avatar
        bio
        website
        socialMedia
        preferences
        communicationPreferences
        language
        currency
        timezone
        businessName
        businessType
        gstNumber
        panNumber
        businessAddress
        businessPhone
        businessEmail
        tags
        notes
        customerType
        loyaltyPoints
        totalSpent
        lastOrderDate
        isActive
        isVerified
        isVIP
        allowMarketing
        role
        createdAt
        updatedAt
        lastLoginAt
        __typename
      }
      __typename
    }
  }
`;
