/* eslint-disable */
// this is an auto generated file. This will be overwritten

export const createUserProfile = /* GraphQL */ `
  mutation CreateUserProfile(
    $input: CreateUserProfileInput!
    $condition: ModelUserProfileConditionInput
  ) {
    createUserProfile(input: $input, condition: $condition) {
      id
      email
      name
      firstName
      lastName
      phone
      alternatePhone
      address
      addressLine2
      city
      state
      pincode
      country
      landmark
      dateOfBirth
      gender
      maritalStatus
      anniversary
      occupation
      company
      avatar
      bio
      website
      socialMedia
      preferences
      communicationPreferences
      language
      currency
      timezone
      businessName
      businessType
      gstNumber
      panNumber
      businessAddress
      businessPhone
      businessEmail
      tags
      notes
      customerType
      loyaltyPoints
      totalSpent
      lastOrderDate
      isActive
      isVerified
      isVIP
      allowMarketing
      role
      createdAt
      updatedAt
      lastLoginAt
      cartItems {
        nextToken
        __typename
      }
      orders {
        nextToken
        __typename
      }
      inquiries {
        nextToken
        __typename
      }
      reviews {
        nextToken
        __typename
      }
      __typename
    }
  }
`;
export const updateUserProfile = /* GraphQL */ `
  mutation UpdateUserProfile(
    $input: UpdateUserProfileInput!
    $condition: ModelUserProfileConditionInput
  ) {
    updateUserProfile(input: $input, condition: $condition) {
      id
      email
      name
      firstName
      lastName
      phone
      alternatePhone
      address
      addressLine2
      city
      state
      pincode
      country
      landmark
      dateOfBirth
      gender
      maritalStatus
      anniversary
      occupation
      company
      avatar
      bio
      website
      socialMedia
      preferences
      communicationPreferences
      language
      currency
      timezone
      businessName
      businessType
      gstNumber
      panNumber
      businessAddress
      businessPhone
      businessEmail
      tags
      notes
      customerType
      loyaltyPoints
      totalSpent
      lastOrderDate
      isActive
      isVerified
      isVIP
      allowMarketing
      role
      createdAt
      updatedAt
      lastLoginAt
      cartItems {
        nextToken
        __typename
      }
      orders {
        nextToken
        __typename
      }
      inquiries {
        nextToken
        __typename
      }
      reviews {
        nextToken
        __typename
      }
      __typename
    }
  }
`;
export const deleteUserProfile = /* GraphQL */ `
  mutation DeleteUserProfile(
    $input: DeleteUserProfileInput!
    $condition: ModelUserProfileConditionInput
  ) {
    deleteUserProfile(input: $input, condition: $condition) {
      id
      email
      name
      firstName
      lastName
      phone
      alternatePhone
      address
      addressLine2
      city
      state
      pincode
      country
      landmark
      dateOfBirth
      gender
      maritalStatus
      anniversary
      occupation
      company
      avatar
      bio
      website
      socialMedia
      preferences
      communicationPreferences
      language
      currency
      timezone
      businessName
      businessType
      gstNumber
      panNumber
      businessAddress
      businessPhone
      businessEmail
      tags
      notes
      customerType
      loyaltyPoints
      totalSpent
      lastOrderDate
      isActive
      isVerified
      isVIP
      allowMarketing
      role
      createdAt
      updatedAt
      lastLoginAt
      cartItems {
        nextToken
        __typename
      }
      orders {
        nextToken
        __typename
      }
      inquiries {
        nextToken
        __typename
      }
      reviews {
        nextToken
        __typename
      }
      __typename
    }
  }
`;
export const createGallery = /* GraphQL */ `
  mutation CreateGallery(
    $input: CreateGalleryInput!
    $condition: ModelGalleryConditionInput
  ) {
    createGallery(input: $input, condition: $condition) {
      id
      title
      description
      imageKey
      imageUrl
      category
      subcategory
      tags
      isActive
      sortOrder
      altText
      metaTitle
      metaDescription
      createdBy
      updatedBy
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const updateGallery = /* GraphQL */ `
  mutation UpdateGallery(
    $input: UpdateGalleryInput!
    $condition: ModelGalleryConditionInput
  ) {
    updateGallery(input: $input, condition: $condition) {
      id
      title
      description
      imageKey
      imageUrl
      category
      subcategory
      tags
      isActive
      sortOrder
      altText
      metaTitle
      metaDescription
      createdBy
      updatedBy
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const deleteGallery = /* GraphQL */ `
  mutation DeleteGallery(
    $input: DeleteGalleryInput!
    $condition: ModelGalleryConditionInput
  ) {
    deleteGallery(input: $input, condition: $condition) {
      id
      title
      description
      imageKey
      imageUrl
      category
      subcategory
      tags
      isActive
      sortOrder
      altText
      metaTitle
      metaDescription
      createdBy
      updatedBy
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const createGalleryStats = /* GraphQL */ `
  mutation CreateGalleryStats(
    $input: CreateGalleryStatsInput!
    $condition: ModelGalleryStatsConditionInput
  ) {
    createGalleryStats(input: $input, condition: $condition) {
      id
      category
      totalImages
      activeImages
      lastUpdated
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const updateGalleryStats = /* GraphQL */ `
  mutation UpdateGalleryStats(
    $input: UpdateGalleryStatsInput!
    $condition: ModelGalleryStatsConditionInput
  ) {
    updateGalleryStats(input: $input, condition: $condition) {
      id
      category
      totalImages
      activeImages
      lastUpdated
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const deleteGalleryStats = /* GraphQL */ `
  mutation DeleteGalleryStats(
    $input: DeleteGalleryStatsInput!
    $condition: ModelGalleryStatsConditionInput
  ) {
    deleteGalleryStats(input: $input, condition: $condition) {
      id
      category
      totalImages
      activeImages
      lastUpdated
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const createProduct = /* GraphQL */ `
  mutation CreateProduct(
    $input: CreateProductInput!
    $condition: ModelProductConditionInput
  ) {
    createProduct(input: $input, condition: $condition) {
      id
      name
      description
      shortDescription
      price
      originalPrice
      images
      category
      subcategory
      tags
      inStock
      stockQuantity
      slug
      sku
      weight
      dimensions
      luxuryDescription
      budgetDescription
      narration
      features
      luxuryFeatures
      budgetFeatures
      specifications
      materials
      careInstructions
      warranty
      shippingInfo
      returnPolicy
      rating
      reviewCount
      badge
      relatedProductIds
      metaTitle
      metaDescription
      isActive
      isFeatured
      sortOrder
      createdBy
      createdAt
      updatedAt
      cartItems {
        nextToken
        __typename
      }
      orderItems {
        nextToken
        __typename
      }
      reviews {
        nextToken
        __typename
      }
      __typename
    }
  }
`;
export const updateProduct = /* GraphQL */ `
  mutation UpdateProduct(
    $input: UpdateProductInput!
    $condition: ModelProductConditionInput
  ) {
    updateProduct(input: $input, condition: $condition) {
      id
      name
      description
      shortDescription
      price
      originalPrice
      images
      category
      subcategory
      tags
      inStock
      stockQuantity
      slug
      sku
      weight
      dimensions
      luxuryDescription
      budgetDescription
      narration
      features
      luxuryFeatures
      budgetFeatures
      specifications
      materials
      careInstructions
      warranty
      shippingInfo
      returnPolicy
      rating
      reviewCount
      badge
      relatedProductIds
      metaTitle
      metaDescription
      isActive
      isFeatured
      sortOrder
      createdBy
      createdAt
      updatedAt
      cartItems {
        nextToken
        __typename
      }
      orderItems {
        nextToken
        __typename
      }
      reviews {
        nextToken
        __typename
      }
      __typename
    }
  }
`;
export const deleteProduct = /* GraphQL */ `
  mutation DeleteProduct(
    $input: DeleteProductInput!
    $condition: ModelProductConditionInput
  ) {
    deleteProduct(input: $input, condition: $condition) {
      id
      name
      description
      shortDescription
      price
      originalPrice
      images
      category
      subcategory
      tags
      inStock
      stockQuantity
      slug
      sku
      weight
      dimensions
      luxuryDescription
      budgetDescription
      narration
      features
      luxuryFeatures
      budgetFeatures
      specifications
      materials
      careInstructions
      warranty
      shippingInfo
      returnPolicy
      rating
      reviewCount
      badge
      relatedProductIds
      metaTitle
      metaDescription
      isActive
      isFeatured
      sortOrder
      createdBy
      createdAt
      updatedAt
      cartItems {
        nextToken
        __typename
      }
      orderItems {
        nextToken
        __typename
      }
      reviews {
        nextToken
        __typename
      }
      __typename
    }
  }
`;
export const createCartItem = /* GraphQL */ `
  mutation CreateCartItem(
    $input: CreateCartItemInput!
    $condition: ModelCartItemConditionInput
  ) {
    createCartItem(input: $input, condition: $condition) {
      id
      userId
      productId
      quantity
      price
      giftType
      customization
      notes
      createdAt
      updatedAt
      user {
        id
        email
        name
        firstName
        lastName
        phone
        alternatePhone
        address
        addressLine2
        city
        state
        pincode
        country
        landmark
        dateOfBirth
        gender
        maritalStatus
        anniversary
        occupation
        company
        avatar
        bio
        website
        socialMedia
        preferences
        communicationPreferences
        language
        currency
        timezone
        businessName
        businessType
        gstNumber
        panNumber
        businessAddress
        businessPhone
        businessEmail
        tags
        notes
        customerType
        loyaltyPoints
        totalSpent
        lastOrderDate
        isActive
        isVerified
        isVIP
        allowMarketing
        role
        createdAt
        updatedAt
        lastLoginAt
        __typename
      }
      product {
        id
        name
        description
        shortDescription
        price
        originalPrice
        images
        category
        subcategory
        tags
        inStock
        stockQuantity
        slug
        sku
        weight
        dimensions
        luxuryDescription
        budgetDescription
        narration
        features
        luxuryFeatures
        budgetFeatures
        specifications
        materials
        careInstructions
        warranty
        shippingInfo
        returnPolicy
        rating
        reviewCount
        badge
        relatedProductIds
        metaTitle
        metaDescription
        isActive
        isFeatured
        sortOrder
        createdBy
        createdAt
        updatedAt
        __typename
      }
      __typename
    }
  }
`;
export const updateCartItem = /* GraphQL */ `
  mutation UpdateCartItem(
    $input: UpdateCartItemInput!
    $condition: ModelCartItemConditionInput
  ) {
    updateCartItem(input: $input, condition: $condition) {
      id
      userId
      productId
      quantity
      price
      giftType
      customization
      notes
      createdAt
      updatedAt
      user {
        id
        email
        name
        firstName
        lastName
        phone
        alternatePhone
        address
        addressLine2
        city
        state
        pincode
        country
        landmark
        dateOfBirth
        gender
        maritalStatus
        anniversary
        occupation
        company
        avatar
        bio
        website
        socialMedia
        preferences
        communicationPreferences
        language
        currency
        timezone
        businessName
        businessType
        gstNumber
        panNumber
        businessAddress
        businessPhone
        businessEmail
        tags
        notes
        customerType
        loyaltyPoints
        totalSpent
        lastOrderDate
        isActive
        isVerified
        isVIP
        allowMarketing
        role
        createdAt
        updatedAt
        lastLoginAt
        __typename
      }
      product {
        id
        name
        description
        shortDescription
        price
        originalPrice
        images
        category
        subcategory
        tags
        inStock
        stockQuantity
        slug
        sku
        weight
        dimensions
        luxuryDescription
        budgetDescription
        narration
        features
        luxuryFeatures
        budgetFeatures
        specifications
        materials
        careInstructions
        warranty
        shippingInfo
        returnPolicy
        rating
        reviewCount
        badge
        relatedProductIds
        metaTitle
        metaDescription
        isActive
        isFeatured
        sortOrder
        createdBy
        createdAt
        updatedAt
        __typename
      }
      __typename
    }
  }
`;
export const deleteCartItem = /* GraphQL */ `
  mutation DeleteCartItem(
    $input: DeleteCartItemInput!
    $condition: ModelCartItemConditionInput
  ) {
    deleteCartItem(input: $input, condition: $condition) {
      id
      userId
      productId
      quantity
      price
      giftType
      customization
      notes
      createdAt
      updatedAt
      user {
        id
        email
        name
        firstName
        lastName
        phone
        alternatePhone
        address
        addressLine2
        city
        state
        pincode
        country
        landmark
        dateOfBirth
        gender
        maritalStatus
        anniversary
        occupation
        company
        avatar
        bio
        website
        socialMedia
        preferences
        communicationPreferences
        language
        currency
        timezone
        businessName
        businessType
        gstNumber
        panNumber
        businessAddress
        businessPhone
        businessEmail
        tags
        notes
        customerType
        loyaltyPoints
        totalSpent
        lastOrderDate
        isActive
        isVerified
        isVIP
        allowMarketing
        role
        createdAt
        updatedAt
        lastLoginAt
        __typename
      }
      product {
        id
        name
        description
        shortDescription
        price
        originalPrice
        images
        category
        subcategory
        tags
        inStock
        stockQuantity
        slug
        sku
        weight
        dimensions
        luxuryDescription
        budgetDescription
        narration
        features
        luxuryFeatures
        budgetFeatures
        specifications
        materials
        careInstructions
        warranty
        shippingInfo
        returnPolicy
        rating
        reviewCount
        badge
        relatedProductIds
        metaTitle
        metaDescription
        isActive
        isFeatured
        sortOrder
        createdBy
        createdAt
        updatedAt
        __typename
      }
      __typename
    }
  }
`;
export const createOrder = /* GraphQL */ `
  mutation CreateOrder(
    $input: CreateOrderInput!
    $condition: ModelOrderConditionInput
  ) {
    createOrder(input: $input, condition: $condition) {
      id
      userId
      orderNumber
      status
      subtotal
      tax
      shipping
      discount
      total
      currency
      shippingAddress {
        name
        phone
        email
        addressLine1
        addressLine2
        landmark
        city
        state
        pincode
        country
        addressType
        __typename
      }
      billingAddress {
        name
        phone
        email
        addressLine1
        addressLine2
        landmark
        city
        state
        pincode
        country
        addressType
        __typename
      }
      paymentMethod
      paymentStatus
      paymentId
      razorpayOrderId
      razorpayPaymentId
      trackingNumber
      estimatedDelivery
      deliveredAt
      customerNotes
      adminNotes
      giftMessage
      specialInstructions
      createdAt
      updatedAt
      user {
        id
        email
        name
        firstName
        lastName
        phone
        alternatePhone
        address
        addressLine2
        city
        state
        pincode
        country
        landmark
        dateOfBirth
        gender
        maritalStatus
        anniversary
        occupation
        company
        avatar
        bio
        website
        socialMedia
        preferences
        communicationPreferences
        language
        currency
        timezone
        businessName
        businessType
        gstNumber
        panNumber
        businessAddress
        businessPhone
        businessEmail
        tags
        notes
        customerType
        loyaltyPoints
        totalSpent
        lastOrderDate
        isActive
        isVerified
        isVIP
        allowMarketing
        role
        createdAt
        updatedAt
        lastLoginAt
        __typename
      }
      items {
        nextToken
        __typename
      }
      __typename
    }
  }
`;
export const updateOrder = /* GraphQL */ `
  mutation UpdateOrder(
    $input: UpdateOrderInput!
    $condition: ModelOrderConditionInput
  ) {
    updateOrder(input: $input, condition: $condition) {
      id
      userId
      orderNumber
      status
      subtotal
      tax
      shipping
      discount
      total
      currency
      shippingAddress {
        name
        phone
        email
        addressLine1
        addressLine2
        landmark
        city
        state
        pincode
        country
        addressType
        __typename
      }
      billingAddress {
        name
        phone
        email
        addressLine1
        addressLine2
        landmark
        city
        state
        pincode
        country
        addressType
        __typename
      }
      paymentMethod
      paymentStatus
      paymentId
      razorpayOrderId
      razorpayPaymentId
      trackingNumber
      estimatedDelivery
      deliveredAt
      customerNotes
      adminNotes
      giftMessage
      specialInstructions
      createdAt
      updatedAt
      user {
        id
        email
        name
        firstName
        lastName
        phone
        alternatePhone
        address
        addressLine2
        city
        state
        pincode
        country
        landmark
        dateOfBirth
        gender
        maritalStatus
        anniversary
        occupation
        company
        avatar
        bio
        website
        socialMedia
        preferences
        communicationPreferences
        language
        currency
        timezone
        businessName
        businessType
        gstNumber
        panNumber
        businessAddress
        businessPhone
        businessEmail
        tags
        notes
        customerType
        loyaltyPoints
        totalSpent
        lastOrderDate
        isActive
        isVerified
        isVIP
        allowMarketing
        role
        createdAt
        updatedAt
        lastLoginAt
        __typename
      }
      items {
        nextToken
        __typename
      }
      __typename
    }
  }
`;
export const deleteOrder = /* GraphQL */ `
  mutation DeleteOrder(
    $input: DeleteOrderInput!
    $condition: ModelOrderConditionInput
  ) {
    deleteOrder(input: $input, condition: $condition) {
      id
      userId
      orderNumber
      status
      subtotal
      tax
      shipping
      discount
      total
      currency
      shippingAddress {
        name
        phone
        email
        addressLine1
        addressLine2
        landmark
        city
        state
        pincode
        country
        addressType
        __typename
      }
      billingAddress {
        name
        phone
        email
        addressLine1
        addressLine2
        landmark
        city
        state
        pincode
        country
        addressType
        __typename
      }
      paymentMethod
      paymentStatus
      paymentId
      razorpayOrderId
      razorpayPaymentId
      trackingNumber
      estimatedDelivery
      deliveredAt
      customerNotes
      adminNotes
      giftMessage
      specialInstructions
      createdAt
      updatedAt
      user {
        id
        email
        name
        firstName
        lastName
        phone
        alternatePhone
        address
        addressLine2
        city
        state
        pincode
        country
        landmark
        dateOfBirth
        gender
        maritalStatus
        anniversary
        occupation
        company
        avatar
        bio
        website
        socialMedia
        preferences
        communicationPreferences
        language
        currency
        timezone
        businessName
        businessType
        gstNumber
        panNumber
        businessAddress
        businessPhone
        businessEmail
        tags
        notes
        customerType
        loyaltyPoints
        totalSpent
        lastOrderDate
        isActive
        isVerified
        isVIP
        allowMarketing
        role
        createdAt
        updatedAt
        lastLoginAt
        __typename
      }
      items {
        nextToken
        __typename
      }
      __typename
    }
  }
`;
export const createOrderItem = /* GraphQL */ `
  mutation CreateOrderItem(
    $input: CreateOrderItemInput!
    $condition: ModelOrderItemConditionInput
  ) {
    createOrderItem(input: $input, condition: $condition) {
      id
      orderId
      productId
      userId
      quantity
      price
      giftType
      customization
      notes
      createdAt
      updatedAt
      order {
        id
        userId
        orderNumber
        status
        subtotal
        tax
        shipping
        discount
        total
        currency
        paymentMethod
        paymentStatus
        paymentId
        razorpayOrderId
        razorpayPaymentId
        trackingNumber
        estimatedDelivery
        deliveredAt
        customerNotes
        adminNotes
        giftMessage
        specialInstructions
        createdAt
        updatedAt
        __typename
      }
      product {
        id
        name
        description
        shortDescription
        price
        originalPrice
        images
        category
        subcategory
        tags
        inStock
        stockQuantity
        slug
        sku
        weight
        dimensions
        luxuryDescription
        budgetDescription
        narration
        features
        luxuryFeatures
        budgetFeatures
        specifications
        materials
        careInstructions
        warranty
        shippingInfo
        returnPolicy
        rating
        reviewCount
        badge
        relatedProductIds
        metaTitle
        metaDescription
        isActive
        isFeatured
        sortOrder
        createdBy
        createdAt
        updatedAt
        __typename
      }
      __typename
    }
  }
`;
export const updateOrderItem = /* GraphQL */ `
  mutation UpdateOrderItem(
    $input: UpdateOrderItemInput!
    $condition: ModelOrderItemConditionInput
  ) {
    updateOrderItem(input: $input, condition: $condition) {
      id
      orderId
      productId
      userId
      quantity
      price
      giftType
      customization
      notes
      createdAt
      updatedAt
      order {
        id
        userId
        orderNumber
        status
        subtotal
        tax
        shipping
        discount
        total
        currency
        paymentMethod
        paymentStatus
        paymentId
        razorpayOrderId
        razorpayPaymentId
        trackingNumber
        estimatedDelivery
        deliveredAt
        customerNotes
        adminNotes
        giftMessage
        specialInstructions
        createdAt
        updatedAt
        __typename
      }
      product {
        id
        name
        description
        shortDescription
        price
        originalPrice
        images
        category
        subcategory
        tags
        inStock
        stockQuantity
        slug
        sku
        weight
        dimensions
        luxuryDescription
        budgetDescription
        narration
        features
        luxuryFeatures
        budgetFeatures
        specifications
        materials
        careInstructions
        warranty
        shippingInfo
        returnPolicy
        rating
        reviewCount
        badge
        relatedProductIds
        metaTitle
        metaDescription
        isActive
        isFeatured
        sortOrder
        createdBy
        createdAt
        updatedAt
        __typename
      }
      __typename
    }
  }
`;
export const deleteOrderItem = /* GraphQL */ `
  mutation DeleteOrderItem(
    $input: DeleteOrderItemInput!
    $condition: ModelOrderItemConditionInput
  ) {
    deleteOrderItem(input: $input, condition: $condition) {
      id
      orderId
      productId
      userId
      quantity
      price
      giftType
      customization
      notes
      createdAt
      updatedAt
      order {
        id
        userId
        orderNumber
        status
        subtotal
        tax
        shipping
        discount
        total
        currency
        paymentMethod
        paymentStatus
        paymentId
        razorpayOrderId
        razorpayPaymentId
        trackingNumber
        estimatedDelivery
        deliveredAt
        customerNotes
        adminNotes
        giftMessage
        specialInstructions
        createdAt
        updatedAt
        __typename
      }
      product {
        id
        name
        description
        shortDescription
        price
        originalPrice
        images
        category
        subcategory
        tags
        inStock
        stockQuantity
        slug
        sku
        weight
        dimensions
        luxuryDescription
        budgetDescription
        narration
        features
        luxuryFeatures
        budgetFeatures
        specifications
        materials
        careInstructions
        warranty
        shippingInfo
        returnPolicy
        rating
        reviewCount
        badge
        relatedProductIds
        metaTitle
        metaDescription
        isActive
        isFeatured
        sortOrder
        createdBy
        createdAt
        updatedAt
        __typename
      }
      __typename
    }
  }
`;
export const createInquiry = /* GraphQL */ `
  mutation CreateInquiry(
    $input: CreateInquiryInput!
    $condition: ModelInquiryConditionInput
  ) {
    createInquiry(input: $input, condition: $condition) {
      id
      userId
      type
      name
      firstName
      lastName
      email
      phone
      company
      subject
      message
      category
      priority
      eventType
      giftingType
      eventDate
      eventLocation
      guestCount
      totalGifts
      totalBudget
      budget
      location
      eventDetails
      source
      referral
      status
      assignedTo
      isRead
      isReplied
      adminNotes
      internalNotes
      attachments
      relatedOrderId
      relatedProductId
      createdAt
      updatedAt
      user {
        id
        email
        name
        firstName
        lastName
        phone
        alternatePhone
        address
        addressLine2
        city
        state
        pincode
        country
        landmark
        dateOfBirth
        gender
        maritalStatus
        anniversary
        occupation
        company
        avatar
        bio
        website
        socialMedia
        preferences
        communicationPreferences
        language
        currency
        timezone
        businessName
        businessType
        gstNumber
        panNumber
        businessAddress
        businessPhone
        businessEmail
        tags
        notes
        customerType
        loyaltyPoints
        totalSpent
        lastOrderDate
        isActive
        isVerified
        isVIP
        allowMarketing
        role
        createdAt
        updatedAt
        lastLoginAt
        __typename
      }
      responses {
        nextToken
        __typename
      }
      __typename
    }
  }
`;
export const updateInquiry = /* GraphQL */ `
  mutation UpdateInquiry(
    $input: UpdateInquiryInput!
    $condition: ModelInquiryConditionInput
  ) {
    updateInquiry(input: $input, condition: $condition) {
      id
      userId
      type
      name
      firstName
      lastName
      email
      phone
      company
      subject
      message
      category
      priority
      eventType
      giftingType
      eventDate
      eventLocation
      guestCount
      totalGifts
      totalBudget
      budget
      location
      eventDetails
      source
      referral
      status
      assignedTo
      isRead
      isReplied
      adminNotes
      internalNotes
      attachments
      relatedOrderId
      relatedProductId
      createdAt
      updatedAt
      user {
        id
        email
        name
        firstName
        lastName
        phone
        alternatePhone
        address
        addressLine2
        city
        state
        pincode
        country
        landmark
        dateOfBirth
        gender
        maritalStatus
        anniversary
        occupation
        company
        avatar
        bio
        website
        socialMedia
        preferences
        communicationPreferences
        language
        currency
        timezone
        businessName
        businessType
        gstNumber
        panNumber
        businessAddress
        businessPhone
        businessEmail
        tags
        notes
        customerType
        loyaltyPoints
        totalSpent
        lastOrderDate
        isActive
        isVerified
        isVIP
        allowMarketing
        role
        createdAt
        updatedAt
        lastLoginAt
        __typename
      }
      responses {
        nextToken
        __typename
      }
      __typename
    }
  }
`;
export const deleteInquiry = /* GraphQL */ `
  mutation DeleteInquiry(
    $input: DeleteInquiryInput!
    $condition: ModelInquiryConditionInput
  ) {
    deleteInquiry(input: $input, condition: $condition) {
      id
      userId
      type
      name
      firstName
      lastName
      email
      phone
      company
      subject
      message
      category
      priority
      eventType
      giftingType
      eventDate
      eventLocation
      guestCount
      totalGifts
      totalBudget
      budget
      location
      eventDetails
      source
      referral
      status
      assignedTo
      isRead
      isReplied
      adminNotes
      internalNotes
      attachments
      relatedOrderId
      relatedProductId
      createdAt
      updatedAt
      user {
        id
        email
        name
        firstName
        lastName
        phone
        alternatePhone
        address
        addressLine2
        city
        state
        pincode
        country
        landmark
        dateOfBirth
        gender
        maritalStatus
        anniversary
        occupation
        company
        avatar
        bio
        website
        socialMedia
        preferences
        communicationPreferences
        language
        currency
        timezone
        businessName
        businessType
        gstNumber
        panNumber
        businessAddress
        businessPhone
        businessEmail
        tags
        notes
        customerType
        loyaltyPoints
        totalSpent
        lastOrderDate
        isActive
        isVerified
        isVIP
        allowMarketing
        role
        createdAt
        updatedAt
        lastLoginAt
        __typename
      }
      responses {
        nextToken
        __typename
      }
      __typename
    }
  }
`;
export const createInquiryResponse = /* GraphQL */ `
  mutation CreateInquiryResponse(
    $input: CreateInquiryResponseInput!
    $condition: ModelInquiryResponseConditionInput
  ) {
    createInquiryResponse(input: $input, condition: $condition) {
      id
      inquiryId
      responderId
      responderName
      responderEmail
      message
      isInternal
      attachments
      createdAt
      updatedAt
      inquiry {
        id
        userId
        type
        name
        firstName
        lastName
        email
        phone
        company
        subject
        message
        category
        priority
        eventType
        giftingType
        eventDate
        eventLocation
        guestCount
        totalGifts
        totalBudget
        budget
        location
        eventDetails
        source
        referral
        status
        assignedTo
        isRead
        isReplied
        adminNotes
        internalNotes
        attachments
        relatedOrderId
        relatedProductId
        createdAt
        updatedAt
        __typename
      }
      __typename
    }
  }
`;
export const updateInquiryResponse = /* GraphQL */ `
  mutation UpdateInquiryResponse(
    $input: UpdateInquiryResponseInput!
    $condition: ModelInquiryResponseConditionInput
  ) {
    updateInquiryResponse(input: $input, condition: $condition) {
      id
      inquiryId
      responderId
      responderName
      responderEmail
      message
      isInternal
      attachments
      createdAt
      updatedAt
      inquiry {
        id
        userId
        type
        name
        firstName
        lastName
        email
        phone
        company
        subject
        message
        category
        priority
        eventType
        giftingType
        eventDate
        eventLocation
        guestCount
        totalGifts
        totalBudget
        budget
        location
        eventDetails
        source
        referral
        status
        assignedTo
        isRead
        isReplied
        adminNotes
        internalNotes
        attachments
        relatedOrderId
        relatedProductId
        createdAt
        updatedAt
        __typename
      }
      __typename
    }
  }
`;
export const deleteInquiryResponse = /* GraphQL */ `
  mutation DeleteInquiryResponse(
    $input: DeleteInquiryResponseInput!
    $condition: ModelInquiryResponseConditionInput
  ) {
    deleteInquiryResponse(input: $input, condition: $condition) {
      id
      inquiryId
      responderId
      responderName
      responderEmail
      message
      isInternal
      attachments
      createdAt
      updatedAt
      inquiry {
        id
        userId
        type
        name
        firstName
        lastName
        email
        phone
        company
        subject
        message
        category
        priority
        eventType
        giftingType
        eventDate
        eventLocation
        guestCount
        totalGifts
        totalBudget
        budget
        location
        eventDetails
        source
        referral
        status
        assignedTo
        isRead
        isReplied
        adminNotes
        internalNotes
        attachments
        relatedOrderId
        relatedProductId
        createdAt
        updatedAt
        __typename
      }
      __typename
    }
  }
`;
export const createNewsletter = /* GraphQL */ `
  mutation CreateNewsletter(
    $input: CreateNewsletterInput!
    $condition: ModelNewsletterConditionInput
  ) {
    createNewsletter(input: $input, condition: $condition) {
      id
      email
      name
      phone
      preferences
      isActive
      source
      tags
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const updateNewsletter = /* GraphQL */ `
  mutation UpdateNewsletter(
    $input: UpdateNewsletterInput!
    $condition: ModelNewsletterConditionInput
  ) {
    updateNewsletter(input: $input, condition: $condition) {
      id
      email
      name
      phone
      preferences
      isActive
      source
      tags
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const deleteNewsletter = /* GraphQL */ `
  mutation DeleteNewsletter(
    $input: DeleteNewsletterInput!
    $condition: ModelNewsletterConditionInput
  ) {
    deleteNewsletter(input: $input, condition: $condition) {
      id
      email
      name
      phone
      preferences
      isActive
      source
      tags
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const createReview = /* GraphQL */ `
  mutation CreateReview(
    $input: CreateReviewInput!
    $condition: ModelReviewConditionInput
  ) {
    createReview(input: $input, condition: $condition) {
      id
      productId
      userId
      rating
      title
      content
      reviewerName
      reviewerEmail
      isVerifiedPurchase
      isApproved
      isHelpful
      isReported
      moderatorNotes
      approvedBy
      approvedAt
      createdAt
      updatedAt
      product {
        id
        name
        description
        shortDescription
        price
        originalPrice
        images
        category
        subcategory
        tags
        inStock
        stockQuantity
        slug
        sku
        weight
        dimensions
        luxuryDescription
        budgetDescription
        narration
        features
        luxuryFeatures
        budgetFeatures
        specifications
        materials
        careInstructions
        warranty
        shippingInfo
        returnPolicy
        rating
        reviewCount
        badge
        relatedProductIds
        metaTitle
        metaDescription
        isActive
        isFeatured
        sortOrder
        createdBy
        createdAt
        updatedAt
        __typename
      }
      user {
        id
        email
        name
        firstName
        lastName
        phone
        alternatePhone
        address
        addressLine2
        city
        state
        pincode
        country
        landmark
        dateOfBirth
        gender
        maritalStatus
        anniversary
        occupation
        company
        avatar
        bio
        website
        socialMedia
        preferences
        communicationPreferences
        language
        currency
        timezone
        businessName
        businessType
        gstNumber
        panNumber
        businessAddress
        businessPhone
        businessEmail
        tags
        notes
        customerType
        loyaltyPoints
        totalSpent
        lastOrderDate
        isActive
        isVerified
        isVIP
        allowMarketing
        role
        createdAt
        updatedAt
        lastLoginAt
        __typename
      }
      __typename
    }
  }
`;
export const updateReview = /* GraphQL */ `
  mutation UpdateReview(
    $input: UpdateReviewInput!
    $condition: ModelReviewConditionInput
  ) {
    updateReview(input: $input, condition: $condition) {
      id
      productId
      userId
      rating
      title
      content
      reviewerName
      reviewerEmail
      isVerifiedPurchase
      isApproved
      isHelpful
      isReported
      moderatorNotes
      approvedBy
      approvedAt
      createdAt
      updatedAt
      product {
        id
        name
        description
        shortDescription
        price
        originalPrice
        images
        category
        subcategory
        tags
        inStock
        stockQuantity
        slug
        sku
        weight
        dimensions
        luxuryDescription
        budgetDescription
        narration
        features
        luxuryFeatures
        budgetFeatures
        specifications
        materials
        careInstructions
        warranty
        shippingInfo
        returnPolicy
        rating
        reviewCount
        badge
        relatedProductIds
        metaTitle
        metaDescription
        isActive
        isFeatured
        sortOrder
        createdBy
        createdAt
        updatedAt
        __typename
      }
      user {
        id
        email
        name
        firstName
        lastName
        phone
        alternatePhone
        address
        addressLine2
        city
        state
        pincode
        country
        landmark
        dateOfBirth
        gender
        maritalStatus
        anniversary
        occupation
        company
        avatar
        bio
        website
        socialMedia
        preferences
        communicationPreferences
        language
        currency
        timezone
        businessName
        businessType
        gstNumber
        panNumber
        businessAddress
        businessPhone
        businessEmail
        tags
        notes
        customerType
        loyaltyPoints
        totalSpent
        lastOrderDate
        isActive
        isVerified
        isVIP
        allowMarketing
        role
        createdAt
        updatedAt
        lastLoginAt
        __typename
      }
      __typename
    }
  }
`;
export const deleteReview = /* GraphQL */ `
  mutation DeleteReview(
    $input: DeleteReviewInput!
    $condition: ModelReviewConditionInput
  ) {
    deleteReview(input: $input, condition: $condition) {
      id
      productId
      userId
      rating
      title
      content
      reviewerName
      reviewerEmail
      isVerifiedPurchase
      isApproved
      isHelpful
      isReported
      moderatorNotes
      approvedBy
      approvedAt
      createdAt
      updatedAt
      product {
        id
        name
        description
        shortDescription
        price
        originalPrice
        images
        category
        subcategory
        tags
        inStock
        stockQuantity
        slug
        sku
        weight
        dimensions
        luxuryDescription
        budgetDescription
        narration
        features
        luxuryFeatures
        budgetFeatures
        specifications
        materials
        careInstructions
        warranty
        shippingInfo
        returnPolicy
        rating
        reviewCount
        badge
        relatedProductIds
        metaTitle
        metaDescription
        isActive
        isFeatured
        sortOrder
        createdBy
        createdAt
        updatedAt
        __typename
      }
      user {
        id
        email
        name
        firstName
        lastName
        phone
        alternatePhone
        address
        addressLine2
        city
        state
        pincode
        country
        landmark
        dateOfBirth
        gender
        maritalStatus
        anniversary
        occupation
        company
        avatar
        bio
        website
        socialMedia
        preferences
        communicationPreferences
        language
        currency
        timezone
        businessName
        businessType
        gstNumber
        panNumber
        businessAddress
        businessPhone
        businessEmail
        tags
        notes
        customerType
        loyaltyPoints
        totalSpent
        lastOrderDate
        isActive
        isVerified
        isVIP
        allowMarketing
        role
        createdAt
        updatedAt
        lastLoginAt
        __typename
      }
      __typename
    }
  }
`;
