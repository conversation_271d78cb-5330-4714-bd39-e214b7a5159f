/**
 * Quick Sample Product Import Script
 * 
 * This script adds just a few sample products for testing purposes.
 * Use this to test the import functionality before running the full bulk import.
 */

import { adminApi } from '../services/adminApi';
import type { ProductInput } from '../services/adminApi';

// Sample products for testing
const sampleProducts: ProductInput[] = [
  {
    name: "Creative Canvas",
    slug: "creative-canvas",
    description: "Perfect creative gift set for artists and art enthusiasts",
    shortDescription: "Ignite your inner artist with a burst of colors and tools that inspire endless creativity.",
    narration: "Step into a world where creativity knows no bounds. Each brushstroke tells a story, every color choice reflects your soul.",
    luxuryDescription: "Indulge in the finest artistic experience with professional-grade materials sourced from renowned art suppliers.",
    budgetDescription: "Start your artistic journey with quality basics that won't break the bank.",
    price: 2499.00,
    originalPrice: 2999.00,
    images: ["/Creative_Canvas.png", "/Custom_Gardening_Hamper.png"],
    category: "creative",
    tags: ["art", "creative", "painting", "gift", "artist", "canvas", "luxury"],
    inStock: true,
    stockQuantity: 15,
    sku: "CUS-0001",
    weight: 1.2,
    dimensions: "12\" x 8\" x 4\"",
    features: [
      "Watercolor or acrylic paint set",
      "Canvas panels or sketchbooks",
      "Paintbrush set",
      "Palette and mixing tray",
      "Apron and cleaning cloths",
      "Instructional art book or online class voucher"
    ],
    luxuryFeatures: [
      "Watercolor or acrylic paint set",
      "Canvas panels or sketchbooks",
      "Paintbrush set",
      "Palette and mixing tray",
      "Apron and cleaning cloths",
      "Instructional art book or online class voucher"
    ],
    budgetFeatures: [
      "Set of basic watercolor paints",
      "Small sketchpad",
      "Affordable paintbrushes",
      "Mixing palette",
      "Simple apron"
    ],
    specifications: JSON.stringify({
      "Box Dimensions": "12\" x 8\" x 4\"",
      "Weight": "1.2 kg",
      "Materials": "Eco-friendly packaging",
      "Origin": "Curated from artisans across India",
      "Shelf Life": "24 months"
    }),
    materials: ["Canvas", "Acrylic Paints", "Natural Brushes", "Eco-friendly Packaging"],
    careInstructions: "Store in a cool, dry place. Keep paints sealed when not in use.",
    warranty: "30-day satisfaction guarantee",
    shippingInfo: "Free shipping on orders above ₹2000. Delivered in 3-5 business days.",
    returnPolicy: "30-day return policy. Items must be in original condition.",
    rating: 4.8,
    reviewCount: 24,
    badge: "Bestseller",
    relatedProductIds: [],
    metaTitle: "Creative Canvas - Art Gift Set | Customine",
    metaDescription: "Perfect creative gift set for artists and art enthusiasts. Premium art supplies with canvas, paints, and brushes.",
    isActive: true,
    isFeatured: true,
    sortOrder: 1
  },
  {
    name: "Green Thumb Haven",
    slug: "green-thumb-haven",
    description: "Perfect gardening gift set for plant lovers and garden enthusiasts",
    shortDescription: "Cultivate joy and watch your garden dreams blossom with every seed and tool in this set.",
    narration: "Feel the earth between your fingers, watch tiny seeds transform into flourishing plants.",
    luxuryDescription: "Experience premium gardening with heirloom seeds, professional-grade tools, and organic fertilizers.",
    budgetDescription: "Begin your gardening adventure with essential tools and easy-to-grow seeds.",
    price: 1899.00,
    originalPrice: 2299.00,
    images: ["/Custom_Gardening_Hamper.png", "/Personalized_Gift_Hamper.png"],
    category: "gardening",
    tags: ["gardening", "plants", "seeds", "nature", "green", "organic"],
    inStock: true,
    stockQuantity: 20,
    sku: "CUS-0002",
    weight: 1.5,
    dimensions: "14\" x 10\" x 6\"",
    features: [
      "Organic seed packets (herbs, vegetables, flowers)",
      "Gardening gloves and tools",
      "Decorative plant markers",
      "Watering can or spray bottle",
      "Gardening journal",
      "Potted plant or succulent"
    ],
    luxuryFeatures: [
      "Organic seed packets (herbs, vegetables, flowers)",
      "Gardening gloves and tools",
      "Decorative plant markers",
      "Watering can or spray bottle",
      "Gardening journal",
      "Potted plant or succulent"
    ],
    budgetFeatures: [
      "Packets of easy-to-grow seeds",
      "Basic gardening gloves",
      "Small hand trowel",
      "Recycled plant markers",
      "DIY garden journal"
    ],
    specifications: JSON.stringify({
      "Box Dimensions": "14\" x 10\" x 6\"",
      "Weight": "1.5 kg",
      "Materials": "Eco-friendly packaging",
      "Origin": "Curated from local nurseries",
      "Shelf Life": "12 months"
    }),
    materials: ["Seeds", "Garden Tools", "Eco-friendly Packaging"],
    shippingInfo: "Free shipping on orders above ₹1500. Delivered in 3-5 business days.",
    returnPolicy: "30-day return policy. Items must be in original condition.",
    rating: 4.7,
    reviewCount: 18,
    badge: "New",
    relatedProductIds: [],
    metaTitle: "Green Thumb Haven - Gardening Gift Set | Customine",
    metaDescription: "Perfect gardening gift set for plant lovers. Organic seeds, tools, and everything needed to start a beautiful garden.",
    isActive: true,
    isFeatured: false,
    sortOrder: 2
  },
  {
    name: "Home Sweet Nest",
    slug: "home-sweet-nest",
    description: "Cozy home essentials for creating the perfect living space",
    shortDescription: "Transform your house into a warm, welcoming home with thoughtfully curated comfort essentials.",
    narration: "Home is where the heart is, and every corner should tell your story.",
    luxuryDescription: "Elevate your living space with premium home accessories that blend comfort with sophistication.",
    budgetDescription: "Create a cozy atmosphere without the luxury price tag.",
    price: 2199.00,
    originalPrice: 2599.00,
    images: ["/Personalized_Gift_Hamper.png", "/Bottle_of_champagne.png"],
    category: "home",
    tags: ["home", "decor", "cozy", "comfort", "living", "interior"],
    inStock: true,
    stockQuantity: 25,
    sku: "CUS-0003",
    weight: 2.0,
    dimensions: "16\" x 12\" x 8\"",
    features: [
      "Scented candles or diffuser",
      "Throw pillows and blankets",
      "Picture frames",
      "Decorative items (vases, figurines)",
      "Cozy slippers",
      "Tea or coffee set"
    ],
    luxuryFeatures: [
      "Scented candles or diffuser",
      "Throw pillows and blankets",
      "Picture frames",
      "Decorative items (vases, figurines)",
      "Cozy slippers",
      "Tea or coffee set"
    ],
    budgetFeatures: [
      "Small scented candles",
      "Throw pillow covers",
      "Simple picture frames",
      "Basic decorative items",
      "Cozy socks",
      "Tea bags or instant coffee"
    ],
    specifications: JSON.stringify({
      "Box Dimensions": "16\" x 12\" x 8\"",
      "Weight": "2.0 kg",
      "Materials": "Home-safe packaging",
      "Origin": "Curated home essentials",
      "Shelf Life": "36 months"
    }),
    materials: ["Textiles", "Ceramics", "Candles", "Home Accessories"],
    shippingInfo: "Free shipping on orders above ₹2000. Delivered in 3-5 business days.",
    returnPolicy: "30-day return policy. Items must be in original condition.",
    rating: 4.6,
    reviewCount: 32,
    badge: "Popular",
    relatedProductIds: [],
    metaTitle: "Home Sweet Nest - Cozy Home Essentials | Customine",
    metaDescription: "Cozy home essentials for creating the perfect living space. Candles, throws, and decor items for a warm home.",
    isActive: true,
    isFeatured: true,
    sortOrder: 3
  }
];

// Function to add sample products
export const addSampleProducts = async (): Promise<{
  success: boolean;
  imported: number;
  failed: number;
  errors: string[];
}> => {
  const results = {
    success: true,
    imported: 0,
    failed: 0,
    errors: [] as string[]
  };

  console.log(`Starting import of ${sampleProducts.length} sample products...`);

  for (const product of sampleProducts) {
    try {
      console.log(`Importing product: ${product.name}`);
      
      const response = await adminApi.createProduct(product);
      
      if (response.success) {
        results.imported++;
        console.log(`✅ Successfully imported: ${product.name}`);
      } else {
        results.failed++;
        results.errors.push(`Failed to import ${product.name}: ${response.error}`);
        console.error(`❌ Failed to import: ${product.name} - ${response.error}`);
      }
    } catch (error) {
      results.failed++;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      results.errors.push(`Error importing ${product.name}: ${errorMessage}`);
      console.error(`❌ Error importing: ${product.name}`, error);
    }
    
    // Add small delay to avoid overwhelming the API
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  results.success = results.failed === 0;
  
  console.log(`\n📊 Sample Import Summary:`);
  console.log(`✅ Successfully imported: ${results.imported} products`);
  console.log(`❌ Failed to import: ${results.failed} products`);
  
  if (results.errors.length > 0) {
    console.log(`\n🚨 Errors:`);
    results.errors.forEach(error => console.log(`- ${error}`));
  }

  return results;
};

// Export sample products for reference
export { sampleProducts };
