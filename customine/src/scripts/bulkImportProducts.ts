/**
 * Bulk Product Import Script for Customine Admin Dashboard
 * 
 * This script imports all static product data from the products page
 * into the AWS Amplify GraphQL database through the admin API.
 * 
 * Usage:
 * 1. Run this script from the admin dashboard
 * 2. Or call the importAllProducts function from a React component
 */

import { adminApi } from '../services/adminApi';
import type { ProductInput } from '../services/adminApi';

// Static product data from products/[slug]/page.tsx
const localImages = [
  "/Creative_Canvas.png",
  "/Custom_Gardening_Hamper.png", 
  "/Personalized_Gift_Hamper.png",
  "/Bottle_of_champagne.png",
  "/Custom_Gift_Hamper_coffee.png",
  "/Relaxation_Gift_Hamper.png",
  "/Snuggle_Season.png",
  "/Semi_Custom_image.png"
];

// All products data
const productsData = [
  {
    id: 1,
    name: "Creative Canvas",
    slug: "creative-canvas",
    price: 2499.00,
    originalPrice: 2999.00,
    images: localImages.slice(0, 4),
    category: "creative",
    rating: 4.8,
    reviewCount: 24,
    badge: "Bestseller",
    description: "Perfect creative gift set for artists and art enthusiasts",
    shortDescription: "Ignite your inner artist with a burst of colors and tools that inspire endless creativity.",
    narration: "Step into a world where creativity knows no bounds. Each brushstroke tells a story, every color choice reflects your soul. This isn't just an art kit – it's your gateway to self-expression and artistic discovery.",
    luxuryDescription: "Indulge in the finest artistic experience with professional-grade materials sourced from renowned art suppliers. Each item is carefully selected to provide the ultimate creative journey for discerning artists.",
    budgetDescription: "Start your artistic journey with quality basics that won't break the bank. Perfect for beginners and hobbyists who want to explore their creative side without the premium price tag.",
    sku: "CUS-0001",
    weight: 1.2,
    dimensions: "12\" x 8\" x 4\"",
    materials: ["Canvas", "Acrylic Paints", "Natural Brushes", "Eco-friendly Packaging"],
    careInstructions: "Store in a cool, dry place. Keep paints sealed when not in use.",
    warranty: "30-day satisfaction guarantee",
    tags: ["art", "creative", "painting", "gift", "artist", "canvas", "luxury"],
    luxuryFeatures: [
      "Watercolor or acrylic paint set",
      "Canvas panels or sketchbooks", 
      "Paintbrush set",
      "Palette and mixing tray",
      "Apron and cleaning cloths",
      "Instructional art book or online class voucher"
    ],
    budgetFeatures: [
      "Set of basic watercolor paints",
      "Small sketchpad",
      "Affordable paintbrushes",
      "Mixing palette",
      "Simple apron"
    ],
    features: [
      "Watercolor or acrylic paint set",
      "Canvas panels or sketchbooks",
      "Paintbrush set", 
      "Palette and mixing tray",
      "Apron and cleaning cloths",
      "Instructional art book or online class voucher",
      "Set of basic watercolor paints",
      "Small sketchpad",
      "Affordable paintbrushes",
      "Mixing palette",
      "Simple apron"
    ],
    specifications: {
      "Box Dimensions": "12\" x 8\" x 4\"",
      "Weight": "1.2 kg",
      "Materials": "Eco-friendly packaging",
      "Origin": "Curated from artisans across India",
      "Shelf Life": "24 months",
      "Age Group": "12+ years",
      "Skill Level": "Beginner to Intermediate",
      "Package Contents": "Complete art kit with instructions"
    },
    inStock: true,
    stockQuantity: 15,
    shippingInfo: "Free shipping on orders above ₹2000. Delivered in 3-5 business days.",
    returnPolicy: "30-day return policy. Items must be in original condition.",
    relatedProductIds: ["2", "3", "6"],
    isFeatured: true,
    isActive: true,
    metaTitle: "Creative Canvas - Art Gift Set | Customine",
    metaDescription: "Perfect creative gift set for artists and art enthusiasts. Premium art supplies with canvas, paints, and brushes."
  },
  {
    id: 2,
    name: "Green Thumb Haven",
    slug: "green-thumb-haven", 
    price: 1899.00,
    originalPrice: 2299.00,
    images: localImages.slice(0, 2),
    category: "gardening",
    rating: 4.7,
    reviewCount: 18,
    badge: "New",
    description: "Perfect gardening gift set for plant lovers and garden enthusiasts",
    shortDescription: "Cultivate joy and watch your garden dreams blossom with every seed and tool in this set.",
    narration: "Feel the earth between your fingers, watch tiny seeds transform into flourishing plants. This is more than gardening – it's connecting with nature and nurturing life from the ground up.",
    luxuryDescription: "Experience premium gardening with heirloom seeds, professional-grade tools, and organic fertilizers. Everything you need to create a sophisticated garden sanctuary.",
    budgetDescription: "Begin your gardening adventure with essential tools and easy-to-grow seeds. Perfect for apartment dwellers and beginners who want to start small but dream big.",
    luxuryFeatures: [
      "Organic seed packets (herbs, vegetables, flowers)",
      "Gardening gloves and tools",
      "Decorative plant markers",
      "Watering can or spray bottle",
      "Gardening journal",
      "Potted plant or succulent"
    ],
    budgetFeatures: [
      "Packets of easy-to-grow seeds",
      "Basic gardening gloves",
      "Small hand trowel",
      "Recycled plant markers",
      "DIY garden journal"
    ],
    features: [
      "Organic seed packets (herbs, vegetables, flowers)",
      "Gardening gloves and tools",
      "Decorative plant markers",
      "Watering can or spray bottle",
      "Gardening journal",
      "Potted plant or succulent",
      "Packets of easy-to-grow seeds",
      "Basic gardening gloves",
      "Small hand trowel",
      "Recycled plant markers",
      "DIY garden journal"
    ],
    specifications: {
      "Box Dimensions": "14\" x 10\" x 6\"",
      "Weight": "1.5 kg",
      "Materials": "Eco-friendly packaging",
      "Origin": "Curated from local nurseries",
      "Shelf Life": "12 months"
    },
    inStock: true,
    stockQuantity: 20,
    shippingInfo: "Free shipping on orders above ₹1500. Delivered in 3-5 business days.",
    returnPolicy: "30-day return policy. Items must be in original condition.",
    sku: "CUS-0002",
    weight: 1.5,
    dimensions: "14\" x 10\" x 6\"",
    materials: ["Seeds", "Garden Tools", "Eco-friendly Packaging"],
    tags: ["gardening", "plants", "seeds", "nature", "green", "organic"],
    isFeatured: false,
    isActive: true,
    metaTitle: "Green Thumb Haven - Gardening Gift Set | Customine",
    metaDescription: "Perfect gardening gift set for plant lovers. Organic seeds, tools, and everything needed to start a beautiful garden."
  },
  {
    id: 3,
    name: "Home Sweet Nest",
    slug: "home-sweet-nest",
    price: 2199.00,
    originalPrice: 2599.00,
    images: localImages.slice(0, 2),
    category: "home",
    rating: 4.6,
    reviewCount: 32,
    badge: "Popular",
    description: "Cozy home essentials for creating the perfect living space",
    shortDescription: "Transform your house into a warm, welcoming home with thoughtfully curated comfort essentials.",
    narration: "Home is where the heart is, and every corner should tell your story. These carefully chosen pieces will wrap your space in warmth and make every moment feel like a gentle embrace.",
    luxuryDescription: "Elevate your living space with premium home accessories that blend comfort with sophistication. Each piece is selected for its quality and ability to create an atmosphere of refined coziness.",
    budgetDescription: "Create a cozy atmosphere without the luxury price tag. These affordable home essentials will help you build a warm, inviting space that feels like home.",
    luxuryFeatures: [
      "Scented candles or diffuser",
      "Throw pillows and blankets",
      "Picture frames",
      "Decorative items (vases, figurines)",
      "Cozy slippers",
      "Tea or coffee set"
    ],
    budgetFeatures: [
      "Small scented candles",
      "Throw pillow covers",
      "Simple picture frames",
      "Basic decorative items",
      "Cozy socks",
      "Tea bags or instant coffee"
    ],
    features: [
      "Scented candles or diffuser",
      "Throw pillows and blankets",
      "Picture frames",
      "Decorative items (vases, figurines)",
      "Cozy slippers",
      "Tea or coffee set",
      "Small scented candles",
      "Throw pillow covers",
      "Simple picture frames",
      "Basic decorative items",
      "Cozy socks",
      "Tea bags or instant coffee"
    ],
    specifications: {
      "Box Dimensions": "16\" x 12\" x 8\"",
      "Weight": "2.0 kg",
      "Materials": "Home-safe packaging",
      "Origin": "Curated home essentials",
      "Shelf Life": "36 months"
    },
    inStock: true,
    stockQuantity: 25,
    shippingInfo: "Free shipping on orders above ₹2000. Delivered in 3-5 business days.",
    returnPolicy: "30-day return policy. Items must be in original condition.",
    sku: "CUS-0003",
    weight: 2.0,
    dimensions: "16\" x 12\" x 8\"",
    materials: ["Textiles", "Ceramics", "Candles", "Home Accessories"],
    tags: ["home", "decor", "cozy", "comfort", "living", "interior"],
    isFeatured: true,
    isActive: true,
    metaTitle: "Home Sweet Nest - Cozy Home Essentials | Customine",
    metaDescription: "Cozy home essentials for creating the perfect living space. Candles, throws, and decor items for a warm home."
  },
  {
    id: 4,
    name: "Raise Your Glass",
    slug: "raise-your-glass",
    price: 3499.00,
    originalPrice: 3999.00,
    images: localImages.slice(0, 2),
    category: "celebration",
    rating: 4.9,
    reviewCount: 15,
    badge: "Premium",
    description: "Elegant celebration essentials for special occasions and toasts",
    shortDescription: "Celebrate life's precious moments with elegance and style that makes every toast memorable.",
    narration: "Every celebration deserves to be extraordinary. Whether it's a milestone achievement or an intimate gathering, these elegant essentials will elevate your special moments into cherished memories.",
    luxuryDescription: "Indulge in the finest celebration experience with premium glassware and sophisticated accessories. Perfect for those who appreciate the finer things in life and want to celebrate in style.",
    budgetDescription: "Celebrate special moments without breaking the bank. These elegant yet affordable items will help you create memorable toasts and celebrations.",
    luxuryFeatures: [
      "Wine glasses or champagne flutes",
      "Bottle of wine or champagne",
      "Cheese and crackers",
      "Chocolate or gourmet treats",
      "Cocktail napkins",
      "Bottle opener or corkscrew"
    ],
    budgetFeatures: [
      "Basic wine glasses",
      "Sparkling juice or affordable wine",
      "Simple cheese and crackers",
      "Chocolate treats",
      "Cocktail napkins",
      "Basic bottle opener"
    ],
    features: [
      "Wine glasses or champagne flutes",
      "Bottle of wine or champagne",
      "Cheese and crackers",
      "Chocolate or gourmet treats",
      "Cocktail napkins",
      "Bottle opener or corkscrew",
      "Basic wine glasses",
      "Sparkling juice or affordable wine",
      "Simple cheese and crackers",
      "Chocolate treats",
      "Cocktail napkins",
      "Basic bottle opener"
    ],
    specifications: {
      "Box Dimensions": "18\" x 14\" x 10\"",
      "Weight": "3.2 kg",
      "Materials": "Premium celebration packaging",
      "Origin": "Curated celebration items",
      "Shelf Life": "24 months"
    },
    inStock: true,
    stockQuantity: 10,
    shippingInfo: "Free shipping on orders above ₹3000. Delivered in 3-5 business days.",
    returnPolicy: "30-day return policy. Items must be in original condition.",
    sku: "CUS-0004",
    weight: 3.2,
    dimensions: "18\" x 14\" x 10\"",
    materials: ["Glassware", "Gourmet Foods", "Premium Packaging"],
    tags: ["celebration", "wine", "champagne", "toast", "party", "elegant", "premium"],
    isFeatured: true,
    isActive: true,
    metaTitle: "Raise Your Glass - Celebration Gift Set | Customine",
    metaDescription: "Elegant celebration essentials for special occasions. Wine glasses, champagne, and gourmet treats for memorable toasts."
  },
  {
    id: 5,
    name: "Brew Bliss",
    slug: "brew-bliss",
    price: 1799.00,
    originalPrice: 2199.00,
    images: localImages.slice(0, 2),
    category: "beverages",
    rating: 4.7,
    reviewCount: 28,
    badge: "Coffee Lover",
    description: "Perfect coffee and tea essentials for beverage enthusiasts",
    shortDescription: "Awaken your senses with the perfect brew that turns every sip into a moment of pure bliss.",
    narration: "The perfect cup is more than just a drink – it's a ritual, a moment of peace in a busy day. Let these carefully curated brewing essentials transform your daily coffee or tea into a luxurious experience.",
    luxuryDescription: "Experience the finest in coffee and tea culture with premium beans, artisanal teas, and professional brewing equipment. For the true connoisseur who appreciates quality in every cup.",
    budgetDescription: "Enjoy great coffee and tea without the premium price. These quality basics will help you create delicious beverages at home without breaking the budget.",
    luxuryFeatures: [
      "Coffee beans or tea leaves",
      "Coffee mug or tea cup",
      "French press or tea infuser",
      "Biscotti or cookies",
      "Honey or sugar cubes",
      "Coffee or tea recipe book"
    ],
    budgetFeatures: [
      "Ground coffee or tea bags",
      "Simple mug",
      "Basic tea strainer",
      "Cookies or biscuits",
      "Sugar packets",
      "Recipe cards"
    ],
    features: [
      "Coffee beans or tea leaves",
      "Coffee mug or tea cup",
      "French press or tea infuser",
      "Biscotti or cookies",
      "Honey or sugar cubes",
      "Coffee or tea recipe book",
      "Ground coffee or tea bags",
      "Simple mug",
      "Basic tea strainer",
      "Cookies or biscuits",
      "Sugar packets",
      "Recipe cards"
    ],
    specifications: {
      "Box Dimensions": "14\" x 10\" x 8\"",
      "Weight": "1.8 kg",
      "Materials": "Food-safe packaging",
      "Origin": "Premium beverage selection",
      "Shelf Life": "18 months"
    },
    inStock: true,
    stockQuantity: 22,
    shippingInfo: "Free shipping on orders above ₹1500. Delivered in 3-5 business days.",
    returnPolicy: "30-day return policy. Items must be in original condition.",
    sku: "CUS-0005",
    weight: 1.8,
    dimensions: "14\" x 10\" x 8\"",
    materials: ["Coffee Beans", "Tea Leaves", "Brewing Equipment", "Food-safe Packaging"],
    tags: ["coffee", "tea", "beverages", "brewing", "morning", "caffeine"],
    isFeatured: false,
    isActive: true,
    metaTitle: "Brew Bliss - Coffee & Tea Gift Set | Customine",
    metaDescription: "Perfect coffee and tea essentials for beverage enthusiasts. Premium beans, brewing equipment, and accessories."
  },
  {
    id: 6,
    name: "Serene Sanctuary",
    slug: "serene-sanctuary",
    price: 2799.00,
    originalPrice: 3299.00,
    images: localImages.slice(0, 2),
    category: "wellness",
    rating: 4.8,
    reviewCount: 21,
    badge: "Wellness",
    description: "Relaxation and wellness essentials for mind, body, and soul",
    shortDescription: "Create your personal oasis of calm with essentials that nurture your well-being and restore inner peace.",
    narration: "In a world that never stops, finding moments of serenity becomes precious. This collection is your invitation to pause, breathe, and reconnect with yourself through mindful self-care.",
    luxuryDescription: "Transform your space into a luxury wellness retreat with premium aromatherapy, spa-quality products, and meditation essentials. Perfect for those who prioritize self-care and mental well-being.",
    budgetDescription: "Create a peaceful sanctuary without the spa price tag. These affordable wellness essentials will help you establish a self-care routine that fits your budget.",
    luxuryFeatures: [
      "Essential oils or aromatherapy diffuser",
      "Bath salts or bath bombs",
      "Face mask or skincare products",
      "Meditation cushion or yoga mat",
      "Herbal tea or wellness drinks",
      "Journal or mindfulness book"
    ],
    budgetFeatures: [
      "Small essential oil bottles",
      "Bath salts",
      "Simple face mask",
      "Meditation guide",
      "Herbal tea bags",
      "Simple journal"
    ],
    features: [
      "Essential oils or aromatherapy diffuser",
      "Bath salts or bath bombs",
      "Face mask or skincare products",
      "Meditation cushion or yoga mat",
      "Herbal tea or wellness drinks",
      "Journal or mindfulness book",
      "Small essential oil bottles",
      "Bath salts",
      "Simple face mask",
      "Meditation guide",
      "Herbal tea bags",
      "Simple journal"
    ],
    specifications: {
      "Box Dimensions": "16\" x 14\" x 10\"",
      "Weight": "2.5 kg",
      "Materials": "Wellness-grade packaging",
      "Origin": "Curated wellness products",
      "Shelf Life": "24 months"
    },
    inStock: true,
    stockQuantity: 18,
    shippingInfo: "Free shipping on orders above ₹2500. Delivered in 3-5 business days.",
    returnPolicy: "30-day return policy. Items must be in original condition.",
    sku: "CUS-0006",
    weight: 2.5,
    dimensions: "16\" x 14\" x 10\"",
    materials: ["Essential Oils", "Bath Products", "Wellness Accessories", "Natural Ingredients"],
    tags: ["wellness", "relaxation", "spa", "aromatherapy", "self-care", "meditation"],
    isFeatured: true,
    isActive: true,
    metaTitle: "Serene Sanctuary - Wellness Gift Set | Customine",
    metaDescription: "Relaxation and wellness essentials for mind, body, and soul. Essential oils, bath products, and meditation accessories."
  },
  {
    id: 7,
    name: "Snuggle Season",
    slug: "snuggle-season",
    price: 2299.00,
    originalPrice: 2699.00,
    images: localImages.slice(0, 2),
    category: "comfort",
    rating: 4.6,
    reviewCount: 26,
    badge: "Cozy",
    description: "Ultimate comfort essentials for cozy nights and relaxation",
    shortDescription: "Wrap yourself in warmth and comfort with essentials that make every moment feel like a gentle hug.",
    narration: "Sometimes the greatest luxury is simply being comfortable. These carefully chosen comfort essentials will transform any space into your personal haven of coziness and warmth.",
    luxuryDescription: "Experience ultimate comfort with premium soft furnishings and luxury relaxation accessories. Perfect for creating a sophisticated yet cozy atmosphere in any space.",
    budgetDescription: "Enjoy cozy comfort without the premium price. These affordable comfort essentials will help you create a warm, inviting space for relaxation.",
    luxuryFeatures: [
      "Soft blanket or throw",
      "Cozy socks or slippers",
      "Hot chocolate or tea",
      "Scented candles",
      "Pillow or cushion",
      "Book or magazine"
    ],
    budgetFeatures: [
      "Fleece throw blanket",
      "Warm socks",
      "Hot chocolate mix",
      "Small candles",
      "Simple cushion",
      "Magazine or puzzle book"
    ],
    features: [
      "Soft blanket or throw",
      "Cozy socks or slippers",
      "Hot chocolate or tea",
      "Scented candles",
      "Pillow or cushion",
      "Book or magazine",
      "Fleece throw blanket",
      "Warm socks",
      "Hot chocolate mix",
      "Small candles",
      "Simple cushion",
      "Magazine or puzzle book"
    ],
    specifications: {
      "Box Dimensions": "16\" x 12\" x 8\"",
      "Weight": "2.2 kg",
      "Materials": "Comfort-grade packaging",
      "Origin": "Cozy comfort essentials",
      "Shelf Life": "36 months"
    },
    inStock: true,
    stockQuantity: 20,
    shippingInfo: "Free shipping on orders above ₹2000. Delivered in 3-5 business days.",
    returnPolicy: "30-day return policy. Items must be in original condition.",
    sku: "CUS-0007",
    weight: 2.2,
    dimensions: "16\" x 12\" x 8\"",
    materials: ["Soft Textiles", "Comfort Accessories", "Cozy Packaging"],
    tags: ["comfort", "cozy", "relaxation", "soft", "warm", "snuggle"],
    isFeatured: false,
    isActive: true,
    metaTitle: "Snuggle Season - Comfort Gift Set | Customine",
    metaDescription: "Ultimate comfort essentials for cozy nights. Soft blankets, warm socks, and relaxation accessories."
  },
  {
    id: 8,
    name: "Sunday Feast",
    slug: "sunday-feast",
    price: 2899.00,
    originalPrice: 3399.00,
    images: localImages.slice(0, 2),
    category: "culinary",
    rating: 4.8,
    reviewCount: 19,
    badge: "Gourmet",
    description: "Gourmet cooking and dining essentials for food lovers",
    shortDescription: "Transform ordinary meals into extraordinary feasts with gourmet ingredients and cooking essentials.",
    narration: "Food is love made visible, and every meal is an opportunity to create memories. These gourmet essentials will help you craft culinary experiences that bring people together around the table.",
    luxuryDescription: "Elevate your culinary adventures with premium ingredients, professional-grade tools, and gourmet accessories. Perfect for the discerning food enthusiast who appreciates quality.",
    budgetDescription: "Create delicious meals without the gourmet price tag. These quality cooking essentials will help you prepare wonderful dishes on any budget.",
    luxuryFeatures: [
      "Gourmet spices and seasonings",
      "Cooking utensils or gadgets",
      "Recipe book or cooking guide",
      "Artisan ingredients",
      "Kitchen towels or apron",
      "Specialty oils or vinegars"
    ],
    budgetFeatures: [
      "Basic spice collection",
      "Simple cooking utensils",
      "Recipe cards",
      "Pantry staples",
      "Kitchen towels",
      "Cooking oil"
    ],
    features: [
      "Gourmet spices and seasonings",
      "Cooking utensils or gadgets",
      "Recipe book or cooking guide",
      "Artisan ingredients",
      "Kitchen towels or apron",
      "Specialty oils or vinegars",
      "Basic spice collection",
      "Simple cooking utensils",
      "Recipe cards",
      "Pantry staples",
      "Kitchen towels",
      "Cooking oil"
    ],
    specifications: {
      "Box Dimensions": "18\" x 14\" x 10\"",
      "Weight": "2.8 kg",
      "Materials": "Culinary-grade packaging",
      "Origin": "Gourmet cooking items",
      "Shelf Life": "24 months"
    },
    inStock: true,
    stockQuantity: 15,
    shippingInfo: "Free shipping on orders above ₹2800. Delivered in 3-5 business days.",
    returnPolicy: "30-day return policy. Items must be in original condition.",
    sku: "CUS-0008",
    weight: 2.8,
    dimensions: "18\" x 14\" x 10\"",
    materials: ["Gourmet Spices", "Cooking Tools", "Artisan Ingredients", "Culinary Packaging"],
    tags: ["cooking", "gourmet", "culinary", "spices", "kitchen", "food"],
    isFeatured: true,
    isActive: true,
    metaTitle: "Sunday Feast - Gourmet Cooking Set | Customine",
    metaDescription: "Gourmet cooking and dining essentials for food lovers. Premium spices, cooking tools, and artisan ingredients."
  }
];

// Function to convert static product data to API format
const convertToProductInput = (product: any): ProductInput => {
  return {
    name: product.name,
    slug: product.slug,
    description: product.description,
    shortDescription: product.shortDescription,
    narration: product.narration,
    luxuryDescription: product.luxuryDescription,
    budgetDescription: product.budgetDescription,
    price: product.price,
    originalPrice: product.originalPrice,
    images: product.images,
    category: product.category,
    subcategory: product.subcategory,
    tags: product.tags,
    inStock: product.inStock,
    stockQuantity: product.stockQuantity,
    sku: product.sku,
    weight: product.weight,
    dimensions: product.dimensions,
    features: product.features,
    luxuryFeatures: product.luxuryFeatures,
    budgetFeatures: product.budgetFeatures,
    specifications: product.specifications ? JSON.stringify(product.specifications) : undefined,
    materials: product.materials,
    careInstructions: product.careInstructions,
    warranty: product.warranty,
    shippingInfo: product.shippingInfo,
    returnPolicy: product.returnPolicy,
    rating: product.rating,
    reviewCount: product.reviewCount,
    badge: product.badge,
    relatedProductIds: product.relatedProductIds || [],
    metaTitle: product.metaTitle,
    metaDescription: product.metaDescription,
    isActive: product.isActive,
    isFeatured: product.isFeatured,
    sortOrder: product.id
  };
};

// Main import function
export const importAllProducts = async (): Promise<{
  success: boolean;
  imported: number;
  failed: number;
  errors: string[];
}> => {
  const results = {
    success: true,
    imported: 0,
    failed: 0,
    errors: [] as string[]
  };

  console.log(`Starting bulk import of ${productsData.length} products...`);

  for (const product of productsData) {
    try {
      console.log(`Importing product: ${product.name}`);
      
      const productInput = convertToProductInput(product);
      const response = await adminApi.createProduct(productInput);
      
      if (response.success) {
        results.imported++;
        console.log(`✅ Successfully imported: ${product.name}`);
      } else {
        results.failed++;
        results.errors.push(`Failed to import ${product.name}: ${response.error}`);
        console.error(`❌ Failed to import: ${product.name} - ${response.error}`);
      }
    } catch (error) {
      results.failed++;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      results.errors.push(`Error importing ${product.name}: ${errorMessage}`);
      console.error(`❌ Error importing: ${product.name}`, error);
    }
    
    // Add small delay to avoid overwhelming the API
    await new Promise(resolve => setTimeout(resolve, 500));
  }

  results.success = results.failed === 0;
  
  console.log(`\n📊 Import Summary:`);
  console.log(`✅ Successfully imported: ${results.imported} products`);
  console.log(`❌ Failed to import: ${results.failed} products`);
  
  if (results.errors.length > 0) {
    console.log(`\n🚨 Errors:`);
    results.errors.forEach(error => console.log(`- ${error}`));
  }

  return results;
};

// Export individual product data for reference
export { productsData };
