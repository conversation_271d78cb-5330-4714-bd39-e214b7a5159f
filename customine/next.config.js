/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'export', // Enable static export for HostGator
  trailingSlash: true,
  skipTrailingSlashRedirect: true,
  distDir: 'out', // Output directory for static files
  eslint: {
    // Warning: This allows production builds to successfully complete even if
    // your project has ESLint errors.
    ignoreDuringBuilds: true,
  },
  typescript: {
    // Warning: This allows production builds to successfully complete even if
    // your project has type errors.
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true, // Required for static export
  },
  // Ensure all routes are exported as static HTML
  generateBuildId: () => 'build',
  // Configure for HostGator deployment
  assetPrefix: process.env.NODE_ENV === 'production' ? '' : '',
  basePath: '',
};

module.exports = nextConfig;