#!/bin/bash

# Customine Deployment Script for HostGator Static Hosting
# This script builds the Next.js app and prepares it for static hosting

echo "🚀 Starting Customine deployment build..."

# Clean previous builds
echo "🧹 Cleaning previous builds..."
rm -rf .next
rm -rf out

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Build the Next.js app for static export
echo "🔨 Building Next.js app..."
npm run build

# Check if build was successful
if [ ! -d "out" ]; then
    echo "❌ Build failed - 'out' directory not found"
    exit 1
fi

echo "✅ Build completed successfully!"

# Clean up RSC payload .txt files
echo "🧹 Cleaning up RSC payload files..."
npm run cleanup

# Copy .htaccess to output directory
echo "📋 Copying .htaccess to output directory..."
cp public/.htaccess out/.htaccess

# Create a deployment info file
echo "📝 Creating deployment info..."
cat > out/deployment-info.txt << EOF
Deployment Date: $(date)
Build ID: build
Next.js Version: $(npm list next --depth=0 | grep next)
Node Version: $(node --version)
NPM Version: $(npm --version)

Files in build:
$(find out -type f | wc -l) files total
$(find out -name "*.html" | wc -l) HTML files
$(find out -name "*.js" | wc -l) JavaScript files
$(find out -name "*.css" | wc -l) CSS files

Key routes:
- / (index.html)
- /cart (cart.html)
- /collections/all (collections/all.html)
- /pages/* (various page routes)

Upload the contents of the 'out' directory to your HostGator public_html folder.
EOF

# List important files
echo "📁 Important files in build:"
echo "- index.html: $([ -f "out/index.html" ] && echo "✅ Found" || echo "❌ Missing")"
echo "- cart.html: $([ -f "out/cart.html" ] && echo "✅ Found" || echo "❌ Missing")"
echo "- .htaccess: $([ -f "out/.htaccess" ] && echo "✅ Found" || echo "❌ Missing")"
echo "- _next/ folder: $([ -d "out/_next" ] && echo "✅ Found" || echo "❌ Missing")"

# Check for potential issues
echo "🔍 Checking for potential issues..."

# Check if all expected HTML files exist
expected_files=("index.html" "cart.html" "collections/all.html" "pages/about-us.html" "pages/contact-us.html")
missing_files=()

for file in "${expected_files[@]}"; do
    if [ ! -f "out/$file" ]; then
        missing_files+=("$file")
    fi
done

if [ ${#missing_files[@]} -gt 0 ]; then
    echo "⚠️  Warning: Some expected HTML files are missing:"
    for file in "${missing_files[@]}"; do
        echo "   - $file"
    done
    echo "   This might cause 404 errors for these routes."
fi

# Check for .txt files (which shouldn't exist in a proper build)
txt_files=$(find out -name "*.txt" -not -name "deployment-info.txt" | wc -l)
if [ $txt_files -gt 0 ]; then
    echo "⚠️  Warning: Found $txt_files .txt files in build (excluding deployment-info.txt)"
    echo "   This might indicate a build configuration issue."
    find out -name "*.txt" -not -name "deployment-info.txt"
fi

echo ""
echo "🎉 Deployment build complete!"
echo ""
echo "📤 Next steps:"
echo "1. Upload the contents of the 'out' directory to your HostGator public_html folder"
echo "2. Make sure the .htaccess file is uploaded and has proper permissions"
echo "3. Test the routes in your browser:"
echo "   - https://customine.in/"
echo "   - https://customine.in/cart/"
echo "   - https://customine.in/collections/all/"
echo ""
echo "🔧 If you still see .txt extensions in URLs:"
echo "1. Check that .htaccess is uploaded and active"
echo "2. Verify HostGator has mod_rewrite enabled"
echo "3. Contact HostGator support if needed"
echo ""
echo "📊 Build summary:"
echo "   Total files: $(find out -type f | wc -l)"
echo "   Build size: $(du -sh out | cut -f1)"
echo "   Ready for upload: ✅"
