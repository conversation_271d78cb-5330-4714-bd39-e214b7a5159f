#!/usr/bin/env node

/**
 * Post-build cleanup script for Customine static export
 * Removes RSC payload .txt files that cause issues with static hosting
 */

const fs = require('fs');
const path = require('path');

const outDir = path.join(process.cwd(), 'out');

function findAndDeleteTxtFiles(dir) {
  let deletedCount = 0;
  
  try {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Recursively process subdirectories
        deletedCount += findAndDeleteTxtFiles(fullPath);
      } else if (item.endsWith('.txt') && item !== 'robots.txt') {
        // Delete .txt files except robots.txt
        try {
          fs.unlinkSync(fullPath);
          console.log(`🗑️  Deleted: ${path.relative(outDir, fullPath)}`);
          deletedCount++;
        } catch (error) {
          console.warn(`⚠️  Failed to delete ${fullPath}:`, error.message);
        }
      }
    }
  } catch (error) {
    console.warn(`⚠️  Failed to read directory ${dir}:`, error.message);
  }
  
  return deletedCount;
}

function main() {
  console.log('🧹 Starting post-build cleanup...');
  
  if (!fs.existsSync(outDir)) {
    console.error('❌ Output directory "out" not found. Run "npm run build" first.');
    process.exit(1);
  }
  
  console.log(`📁 Scanning ${outDir} for RSC payload .txt files...`);
  
  const deletedCount = findAndDeleteTxtFiles(outDir);
  
  console.log(`\n✅ Cleanup complete!`);
  console.log(`🗑️  Deleted ${deletedCount} RSC payload .txt files`);
  console.log(`📁 Kept robots.txt and other necessary files`);
  
  // Verify critical files still exist
  const criticalFiles = [
    'index.html',
    'cart/index.html',
    'collections/all/index.html',
    'robots.txt'
  ];
  
  console.log(`\n🔍 Verifying critical files...`);
  let allGood = true;
  
  for (const file of criticalFiles) {
    const filePath = path.join(outDir, file);
    if (fs.existsSync(filePath)) {
      console.log(`✅ ${file}`);
    } else {
      console.log(`❌ ${file} - MISSING!`);
      allGood = false;
    }
  }
  
  if (allGood) {
    console.log(`\n🎉 Build is ready for deployment!`);
    console.log(`📤 Upload the contents of the 'out' directory to HostGator`);
  } else {
    console.log(`\n⚠️  Some critical files are missing. Check your build configuration.`);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { findAndDeleteTxtFiles };
