#!/bin/bash

# Customine Deployment Script for HostGator
echo "🚀 Building Customine for static deployment..."

# Clean previous builds
echo "🧹 Cleaning previous builds..."
rm -rf .next out

# Build the application
echo "🔨 Building Next.js application..."
npx next build

# Check if build was successful
if [ $? -eq 0 ]; then
    echo "✅ Build successful!"
else
    echo "❌ Build failed!"
    exit 1
fi

# Copy .htaccess to out directory
echo "📋 Copying .htaccess for URL rewriting..."
cp public/.htaccess out/.htaccess

# Create necessary directories in out folder if they don't exist
echo "📁 Ensuring proper directory structure..."
mkdir -p out/dashboard
mkdir -p out/pages
mkdir -p out/products
mkdir -p out/api

# Copy any additional static files
echo "📄 Copying additional static files..."
# Copy any PHP files if they exist
if [ -d "api" ]; then
    cp -r api/* out/api/ 2>/dev/null || true
fi

# List the contents of out directory
echo "📦 Generated files:"
ls -la out/

echo ""
echo "🎉 Deployment ready!"
echo ""
echo "📋 Next steps:"
echo "1. Upload the contents of the 'out' folder to your HostGator public_html directory"
echo "2. Make sure .htaccess is in the root of public_html"
echo "3. Test the URLs:"
echo "   - http://customine.in/dashboard"
echo "   - http://customine.in/pages/special-occasion-inquiry"
echo ""
echo "🔧 If URLs still don't work:"
echo "1. Check if mod_rewrite is enabled in HostGator"
echo "2. Verify .htaccess permissions (should be 644)"
echo "3. Check HostGator error logs for any issues"
