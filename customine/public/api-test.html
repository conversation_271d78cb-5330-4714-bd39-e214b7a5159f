<!DOCTYPE html>
<html>
<head>
    <title>API Test</title>
</head>
<body>
    <h1>API Connectivity Test</h1>
    <div id="results"></div>
    
    <script>
        async function testAPI() {
            const results = document.getElementById('results');
            
            try {
                console.log('Testing /api/test.php...');
                const response = await fetch('/api/test.php');
                const text = await response.text();
                
                results.innerHTML = `
                    <h2>API Test Results:</h2>
                    <p><strong>Status:</strong> ${response.status}</p>
                    <p><strong>Content-Type:</strong> ${response.headers.get('content-type')}</p>
                    <h3>Response:</h3>
                    <pre style="background: #f5f5f5; padding: 10px; overflow: auto;">${text}</pre>
                `;
                
                console.log('Response:', text);
            } catch (error) {
                results.innerHTML = `<p style="color: red;">Error: ${error.message}</p>`;
                console.error('Error:', error);
            }
        }
        
        // Test immediately
        testAPI();
    </script>
</body>
</html>
