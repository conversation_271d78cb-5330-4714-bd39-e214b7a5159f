// Simple service worker to help with navigation issues in static exports
const CACHE_NAME = 'customine-v1';
const urlsToCache = [
  '/',
  '/cart',
  '/collections/all',
  '/pages/about-us',
  '/pages/contact-us'
];

self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('Opened cache');
        return cache.addAll(urlsToCache);
      })
  );
});

self.addEventListener('fetch', (event) => {
  // Only handle navigation requests
  if (event.request.mode === 'navigate') {
    event.respondWith(
      fetch(event.request)
        .catch(() => {
          // If fetch fails, try to serve from cache
          return caches.match(event.request)
            .then((response) => {
              if (response) {
                return response;
              }
              // If not in cache, serve the index page
              return caches.match('/');
            });
        })
    );
  }
});
