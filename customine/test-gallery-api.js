#!/usr/bin/env node

/**
 * Simple test script to check if the gallery API is working
 * Run with: node test-gallery-api.js
 */

const { Amplify } = require('aws-amplify');
const { generateClient } = require('aws-amplify/api');

// Import configuration
const amplifyconfig = require('./src/amplifyconfiguration.json');

// Configure Amplify
Amplify.configure(amplifyconfig);

// GraphQL query
const listGalleries = `
  query ListGalleries(
    $filter: ModelGalleryFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listGalleries(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        title
        description
        imageKey
        imageUrl
        category
        tags
        isActive
        sortOrder
        altText
        metaTitle
        metaDescription
        createdBy
        updatedBy
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;

async function testGalleryAPI() {
  try {
    console.log('🧪 Testing Gallery API...');
    
    const client = generateClient();
    
    // Test HOME category
    console.log('\n📸 Testing HOME gallery...');
    const homeResult = await client.graphql({
      query: listGalleries,
      variables: {
        filter: {
          category: { eq: 'HOME' },
          isActive: { eq: true }
        },
        limit: 10
      }
    });
    
    const homeImages = homeResult.data?.listGalleries?.items || [];
    console.log(`✅ Found ${homeImages.length} HOME gallery images`);
    
    if (homeImages.length > 0) {
      console.log('📋 HOME Gallery Images:');
      homeImages.forEach((img, idx) => {
        console.log(`  ${idx + 1}. ${img.title} (${img.imageKey}) - Active: ${img.isActive}`);
      });
    } else {
      console.log('⚠️  No HOME gallery images found. Please add some through the admin panel.');
    }
    
    // Test LUXURY category
    console.log('\n👑 Testing LUXURY gallery...');
    const luxuryResult = await client.graphql({
      query: listGalleries,
      variables: {
        filter: {
          category: { eq: 'LUXURY' },
          isActive: { eq: true }
        },
        limit: 10
      }
    });
    
    const luxuryImages = luxuryResult.data?.listGalleries?.items || [];
    console.log(`✅ Found ${luxuryImages.length} LUXURY gallery images`);
    
    if (luxuryImages.length > 0) {
      console.log('📋 LUXURY Gallery Images:');
      luxuryImages.forEach((img, idx) => {
        console.log(`  ${idx + 1}. ${img.title} (${img.imageKey}) - Active: ${img.isActive}`);
      });
    } else {
      console.log('⚠️  No LUXURY gallery images found. Please add some through the admin panel.');
    }
    
    // Test all categories
    console.log('\n🌍 Testing all gallery categories...');
    const allResult = await client.graphql({
      query: listGalleries,
      variables: {
        filter: {
          isActive: { eq: true }
        },
        limit: 50
      }
    });
    
    const allImages = allResult.data?.listGalleries?.items || [];
    console.log(`✅ Found ${allImages.length} total active gallery images`);
    
    // Group by category
    const byCategory = allImages.reduce((acc, img) => {
      acc[img.category] = (acc[img.category] || 0) + 1;
      return acc;
    }, {});
    
    console.log('📊 Images by category:');
    Object.entries(byCategory).forEach(([category, count]) => {
      console.log(`  ${category}: ${count} images`);
    });
    
    console.log('\n🎉 Gallery API test completed successfully!');
    
    if (homeImages.length === 0) {
      console.log('\n💡 Next steps:');
      console.log('1. Start the dev server: npm run dev');
      console.log('2. Go to: http://localhost:3000/dashboard/gallery/home');
      console.log('3. Upload some images to the HOME category');
      console.log('4. Check the homepage: http://localhost:3000/');
      console.log('5. Run this test again to verify');
    }
    
  } catch (error) {
    console.error('❌ Gallery API test failed:', error);
    
    if (error.message?.includes('No credentials')) {
      console.log('\n💡 Authentication required:');
      console.log('1. Make sure you are logged in to the admin panel');
      console.log('2. Or run this test from within the Next.js app context');
    }
    
    if (error.message?.includes('Network')) {
      console.log('\n💡 Network issue:');
      console.log('1. Check your internet connection');
      console.log('2. Verify AWS Amplify configuration');
      console.log('3. Make sure the backend is deployed');
    }
  }
}

// Run the test
testGalleryAPI();
