# Customine E-commerce GraphQL Schema
# This "input" configures a global authorization rule to enable public access to
# all models in this schema. Learn more about authorization rules here: https://docs.amplify.aws/cli/graphql/authorization-rules
input AMPLIFY { globalAuthRule: AuthRule = { allow: public } } # FOR TESTING ONLY!

# User Profile Model (extends Cognito user)
type UserProfile @model {
  id: ID! # Cognito user ID
  email: String! @index(name: "byEmail")
  name: String!
  firstName: String # First name of the user
  lastName: String # Last name of the user
  phone: String
  alternatePhone: String

  # Address Information
  address: String
  addressLine2: String
  city: String
  state: String
  pincode: String
  country: String
  landmark: String

  # Personal Information
  dateOfBirth: AWSDate
  gender: String
  maritalStatus: String
  anniversary: AWSDate
  occupation: String
  company: String

  # Profile Details
  avatar: String # Profile picture URL/S3 key
  bio: String
  website: String
  socialMedia: AWSJSON # {instagram: "", facebook: "", linkedin: ""}

  # Preferences & Settings
  preferences: AWSJSON # Shopping preferences, interests, etc.
  communicationPreferences: AWSJSON # Email, SMS, WhatsApp preferences
  language: String
  currency: String
  timezone: String

  # Business Information (for corporate clients)
  businessName: String
  businessType: String
  gstNumber: String
  panNumber: String
  businessAddress: String
  businessPhone: String
  businessEmail: String

  # Additional Metadata
  tags: [String] # Admin tags for categorization
  notes: String # Admin notes
  customerType: String # Individual, Corporate, VIP, etc.
  loyaltyPoints: Int
  totalSpent: Float
  lastOrderDate: AWSDateTime

  # Status & Flags
  isActive: Boolean!
  isVerified: Boolean
  isVIP: Boolean
  allowMarketing: Boolean
  role: String # admin, user, manager, etc.

  # Timestamps
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
  lastLoginAt: AWSDateTime

  # Relationships
  cartItems: [CartItem] @hasMany(indexName: "byUser", fields: ["id"])
  orders: [Order] @hasMany(indexName: "byUser", fields: ["id"])
  inquiries: [Inquiry] @hasMany(indexName: "byUser", fields: ["id"])
  reviews: [Review] @hasMany(indexName: "byUser", fields: ["id"])
}

type Gallery @model @auth(rules: [{allow: public}]) {
  id: ID!
  title: String!
  description: String
  imageKey: String! # S3 key for the image
  imageUrl: String # Optional: for backward compatibility
  category: GalleryCategory!
  subcategory: String # Subcategory for organizing within main categories
  tags: [String]
  isActive: Boolean!
  sortOrder: Int
  altText: String

  # SEO fields
  metaTitle: String
  metaDescription: String

  # Admin fields
  createdBy: String @index(name: "byCreatedBy")
  updatedBy: String

  # Timestamps
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}

enum GalleryCategory {
  HOME
  LUXURY
  CORPORATE
  WEDDING
  FESTIVAL
  PERSONAL
}

type GalleryStats @model @auth(rules: [{allow: public}]) {
  id: ID!
  category: GalleryCategory!
  totalImages: Int!
  activeImages: Int!
  lastUpdated: AWSDateTime!
}

# Product Model
type Product @model @auth(rules: [{allow: public}]) {
  id: ID!
  name: String!
  description: String!
  shortDescription: String
  price: Float!
  originalPrice: Float
  images: [String!]!
  category: String! @index(name: "byCategory")
  subcategory: String
  tags: [String!]
  inStock: Boolean!
  stockQuantity: Int
  slug: String! @index(name: "bySlug")
  sku: String @index(name: "bySku")
  weight: Float
  dimensions: String

  # Gift box specific fields
  luxuryDescription: String
  budgetDescription: String
  narration: String

  # Product features and specifications
  features: [String!]
  luxuryFeatures: [String!]
  budgetFeatures: [String!]
  specifications: AWSJSON
  materials: [String!]
  careInstructions: String
  warranty: String

  # Shipping and return information
  shippingInfo: String
  returnPolicy: String

  # Rating and review fields
  rating: Float
  reviewCount: Int

  # Additional product fields
  badge: String
  relatedProductIds: [String!]

  # SEO and admin fields
  metaTitle: String
  metaDescription: String
  isActive: Boolean!
  isFeatured: Boolean!
  sortOrder: Int
  createdBy: String

  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!

  # Relationships
  cartItems: [CartItem] @hasMany(indexName: "byProduct", fields: ["id"])
  orderItems: [OrderItem] @hasMany(indexName: "byProduct", fields: ["id"])
  reviews: [Review] @hasMany(indexName: "byProduct", fields: ["id"])
}

# Cart Item Model
type CartItem @model {
  id: ID!
  userId: ID! @index(name: "byUser")
  productId: ID! @index(name: "byProduct")
  quantity: Int!
  price: Float! # Price at time of adding to cart
  giftType: String # "luxury", "budget", "custom"
  customization: AWSJSON # Custom requirements as JSON
  notes: String
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!

  # Relationships
  user: UserProfile @belongsTo(fields: ["userId"])
  product: Product @belongsTo(fields: ["productId"])
}

# Order Model
type Order @model {
  id: ID!
  userId: ID! @index(name: "byUser")
  orderNumber: String! @index(name: "byOrderNumber")
  status: OrderStatus!

  # Pricing
  subtotal: Float!
  tax: Float!
  shipping: Float!
  discount: Float!
  total: Float!
  currency: String!

  # Shipping Information
  shippingAddress: ShippingAddress!
  billingAddress: ShippingAddress

  # Payment Information
  paymentMethod: String!
  paymentStatus: PaymentStatus!
  paymentId: String
  razorpayOrderId: String
  razorpayPaymentId: String

  # Order tracking
  trackingNumber: String
  estimatedDelivery: AWSDateTime
  deliveredAt: AWSDateTime

  # Notes and special instructions
  customerNotes: String
  adminNotes: String
  giftMessage: String
  specialInstructions: String

  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!

  # Relationships
  user: UserProfile @belongsTo(fields: ["userId"])
  items: [OrderItem] @hasMany(indexName: "byOrder", fields: ["id"])
}

# Order Item Model
type OrderItem @model {
  id: ID!
  orderId: ID! @index(name: "byOrder")
  productId: ID! @index(name: "byProduct")
  userId: ID! # For authorization

  quantity: Int!
  price: Float! # Price at time of order
  giftType: String
  customization: AWSJSON
  notes: String

  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!

  # Relationships
  order: Order @belongsTo(fields: ["orderId"])
  product: Product @belongsTo(fields: ["productId"])
}

# Inquiry/Contact Form Model
type Inquiry @model {
  id: ID!
  userId: ID @index(name: "byUser") # Optional - can be anonymous
  type: InquiryType! @index(name: "byType")

  # Contact Information
  name: String!
  firstName: String
  lastName: String
  email: String! @index(name: "byEmail")
  phone: String
  company: String

  # Inquiry Details
  subject: String!
  message: String!
  category: String # "general", "product", "order", "complaint", "suggestion"
  priority: String # "low", "medium", "high", "urgent"

  # Event/Occasion Details (for special occasion inquiries)
  eventType: String # "wedding", "corporate", "birthday", "anniversary"
  giftingType: String # "corporate-gifting", "wedding-gifts", "personal-gifts"
  eventDate: AWSDate
  eventLocation: String # Separate from general location
  guestCount: Int
  totalGifts: String # "1-25", "26-50", "51-100", "101-250", "250+"
  totalBudget: String # "under-10k", "10k-25k", "25k-50k", "50k-100k", "100k+"
  budget: String # Legacy field for backward compatibility
  location: String # General location field
  eventDetails: String # Additional event details
  source: String # "google-search", "social-media", "referral", "website"
  referral: String # Referral source details

  # Admin fields
  status: InquiryStatus!
  assignedTo: String
  isRead: Boolean!
  isReplied: Boolean!
  adminNotes: String
  internalNotes: String

  # Attachments and references
  attachments: [String!] # URLs to uploaded files
  relatedOrderId: String
  relatedProductId: String

  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!

  # Relationships
  user: UserProfile @belongsTo(fields: ["userId"])
  responses: [InquiryResponse] @hasMany(indexName: "byInquiry", fields: ["id"])
}

# Inquiry Response Model (for admin replies)
type InquiryResponse @model {
  id: ID!
  inquiryId: ID! @index(name: "byInquiry")
  responderId: String! # Admin user ID
  responderName: String!
  responderEmail: String!

  message: String!
  isInternal: Boolean! # Internal note vs customer-facing response
  attachments: [String!]

  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!

  # Relationships
  inquiry: Inquiry @belongsTo(fields: ["inquiryId"])
}

# Newsletter Subscription Model
type Newsletter @model {
  id: ID!
  email: String! @index(name: "byEmail")
  name: String
  phone: String
  preferences: AWSJSON # Subscription preferences
  isActive: Boolean!
  source: String # "website", "social", "event", "referral"
  tags: [String!] # Segmentation tags

  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}

# Review Model
type Review @model @auth(rules: [{allow: public}]) {
  id: ID!
  productId: ID! @index(name: "byProduct")
  userId: ID @index(name: "byUser")

  # Review content
  rating: Int! # 1-5 stars
  title: String!
  content: String!

  # Reviewer information
  reviewerName: String!
  reviewerEmail: String!
  isVerifiedPurchase: Boolean!

  # Review metadata
  isApproved: Boolean!
  isHelpful: Int # Number of helpful votes
  isReported: Boolean

  # Admin fields
  moderatorNotes: String
  approvedBy: String
  approvedAt: AWSDateTime

  # Timestamps
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!

  # Relationships
  product: Product @belongsTo(fields: ["productId"])
  user: UserProfile @belongsTo(fields: ["userId"])
}

# Enums
enum OrderStatus {
  PENDING
  CONFIRMED
  PROCESSING
  PACKED
  SHIPPED
  OUT_FOR_DELIVERY
  DELIVERED
  CANCELLED
  REFUNDED
  RETURNED
}

enum PaymentStatus {
  PENDING
  PROCESSING
  PAID
  FAILED
  CANCELLED
  REFUNDED
  PARTIALLY_REFUNDED
}

enum InquiryType {
  GENERAL_INQUIRY
  PRODUCT_INQUIRY
  ORDER_INQUIRY
  SPECIAL_OCCASION
  CORPORATE_INQUIRY
  BULK_ORDER
  CUSTOM_GIFT_BOX
  COMPLAINT
  SUGGESTION
  PARTNERSHIP
  MEDIA_INQUIRY
  NEWSLETTER_SIGNUP
}

enum InquiryStatus {
  NEW
  OPEN
  IN_PROGRESS
  WAITING_FOR_CUSTOMER
  RESOLVED
  CLOSED
  ESCALATED
}

# Custom Types
type ShippingAddress {
  name: String!
  phone: String!
  email: String
  addressLine1: String!
  addressLine2: String
  landmark: String
  city: String!
  state: String!
  pincode: String!
  country: String!
  addressType: String # "home", "office", "other"
}

# Input Types for mutations
input CreateUserProfileInput {
  id: ID!
  email: String!
  name: String!
  firstName: String
  lastName: String
  phone: String
  alternatePhone: String

  # Address Information
  address: String
  addressLine2: String
  city: String
  state: String
  pincode: String
  country: String
  landmark: String

  # Personal Information
  dateOfBirth: AWSDate
  gender: String
  maritalStatus: String
  anniversary: AWSDate
  occupation: String
  company: String

  # Profile Details
  avatar: String
  bio: String
  website: String
  socialMedia: AWSJSON
  preferences: AWSJSON
  communicationPreferences: AWSJSON
  language: String
  currency: String
  timezone: String

  # Business Information
  businessName: String
  businessType: String
  gstNumber: String
  panNumber: String
  businessAddress: String
  businessPhone: String
  businessEmail: String

  # Admin and System Fields
  tags: [String!]
  notes: String
  customerType: String
  loyaltyPoints: Int
  totalSpent: Float
  lastOrderDate: AWSDateTime
  isActive: Boolean
  isVerified: Boolean
  isVIP: Boolean
  allowMarketing: Boolean
  role: String
  lastLoginAt: AWSDateTime
}

input CreateInquiryInput {
  userId: String
  type: InquiryType!
  name: String!
  firstName: String
  lastName: String
  email: String!
  phone: String
  company: String
  subject: String!
  message: String!
  category: String
  priority: String
  eventType: String
  giftingType: String
  eventDate: AWSDate
  eventLocation: String
  guestCount: Int
  totalGifts: String
  totalBudget: String
  budget: String
  location: String
  eventDetails: String
  source: String
  referral: String
  status: InquiryStatus
  assignedTo: String
  isRead: Boolean
  isReplied: Boolean
  adminNotes: String
  internalNotes: String
  attachments: [String!]
  relatedOrderId: String
  relatedProductId: String
}

input CreateNewsletterInput {
  email: String!
  name: String
  phone: String
  preferences: AWSJSON
  source: String
  tags: [String!]
}

