{"api": {"customine": {"dependsOn": [], "output": {"authConfig": {"additionalAuthenticationProviders": [], "defaultAuthentication": {"apiKeyConfig": {"apiKeyExpirationDays": 7}, "authenticationType": "API_KEY"}}}, "providerPlugin": "awscloudformation", "service": "AppSync"}}, "auth": {"custominea2e985a3": {"customAuth": false, "dependsOn": [], "frontendAuthConfig": {"mfaConfiguration": "OFF", "mfaTypes": ["SMS"], "passwordProtectionSettings": {"passwordPolicyCharacters": [], "passwordPolicyMinLength": 8}, "signupAttributes": ["EMAIL"], "socialProviders": [], "usernameAttributes": ["EMAIL", "PHONE_NUMBER"], "verificationMechanisms": ["EMAIL"]}, "providerPlugin": "awscloudformation", "service": "Cognito"}}, "storage": {"customine": {"dependsOn": [], "providerPlugin": "awscloudformation", "service": "S3"}}}