{"dev": {"awscloudformation": {"AuthRoleName": "amplify-customine-dev-1a04b-authRole", "UnauthRoleArn": "arn:aws:iam::549083880241:role/amplify-customine-dev-1a04b-unauthRole", "AuthRoleArn": "arn:aws:iam::549083880241:role/amplify-customine-dev-1a04b-authRole", "Region": "ap-south-1", "DeploymentBucketName": "amplify-customine-dev-1a04b-deployment", "UnauthRoleName": "amplify-customine-dev-1a04b-unauthRole", "StackName": "amplify-customine-dev-1a04b", "StackId": "arn:aws:cloudformation:ap-south-1:549083880241:stack/amplify-customine-dev-1a04b/bbd83720-6378-11f0-a53f-0ac1b711f8b3", "AmplifyAppId": "d1cemjnnhkz9bx"}, "categories": {"auth": {"custominea2e985a3": {}}, "api": {"customine": {}}, "storage": {"customine": {}}}}}